<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrShiftV2ExtendModel;
use FlashExpress\bi\App\Models\backyard\HrShiftV2Model;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStoreApplySupportModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\ShiftRepository;
use FlashExpress\bi\App\Repository\OsStaffRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\StoreSupportRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StoreSupportServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\StaffServer;
use Exception;

class StoreSupportServer extends GlobalBaseServer
{
    public $timezone;
    public $lang;

    protected $store_support;
    protected $os;
    protected $staff;
    protected   $auditlist;

    public $apply_store_category = [1, 10, 13];

    public function __construct($lang = 'zh-CN', $timezone)
    {
        $this->timezone = $timezone;
        $this->lang     = $lang;
        $this->os = new OsStaffRepository($timezone);
        $this->store_support = new StoreSupportRepository($timezone);
        $this->staff = new StaffRepository();
        $this->auditlist= new AuditlistRepository($this->lang, $this->timezone);

        parent::__construct($lang);
    }

    /**
     * 网点申请支援
     * @param $paramIn
     * @return array
     */
    public function addStoreApplySupport($paramIn) {
        $staff_info_id  = $this->processingDefault($paramIn, 'staff_info_id');
        $job_title_id   = $this->processingDefault($paramIn, 'job_title_id');
        $employ_date    = $this->processingDefault($paramIn, 'employment_date');
        $employment_days = $this->processingDefault($paramIn, 'employment_days');
        $shift_id       = $this->processingDefault($paramIn, 'shift_id');
        $reason_type    = $this->processingDefault($paramIn, 'reason_type', 2);
        $reason         = $this->processingDefault($paramIn, 'reason');
        $reason         = addcslashes(stripslashes($reason), "'");
        $store_id       = $this->processingDefault($paramIn, 'store_id');
        $demand_num     = $this->processingDefault($paramIn, 'demand_num');

        $logger = $this->getDI()->get('logger');
        $db = $this->getDI()->get('db');
        try {
            $staff_info = $this->staff->checkoutStaff($staff_info_id);
            $employment_end_date = date('Y-m-d', strtotime($employ_date) + 86400 * ($employment_days -1));
            $store_detail = SysStoreModel::findFirst([
                'conditions' => 'id = :store_id:',
                'bind' => [
                    'store_id' => $store_id,
                ],
            ]);

            if(empty($store_detail)) {
                $logger->write_log('addStoreApplySupport 找不到网点信息: 参数：store_id - '. $store_id);
                throw new BusinessException($this->getTranslation()->_('4008'));
            }
            $store_detail = $store_detail->toArray();
            //指定班次
            $enable_shift_ids = (new SettingEnvServer())->getSetVal('by_store_support_shift_ids',',');
            $shift_info  = (new ShiftRepository($this->timezone))->setEnableShiftIds($enable_shift_ids)->getV2ShiftInfo($shift_id);
            if (empty($shift_info)) {
                $logger->write_log('addStoreApplySupport 找不到班次:getWorkShift: 参数：shift_id - '. $shift_id);
                throw new BusinessException($this->getTranslation()->_('4008'));
            }

            $insertData = [
                'serial_no'             => 'SAS' . $this->getRandomId(),
                'staff_info_id'         => $staff_info_id,
                'apply_type'            => 1,
                'job_title_id'          => $job_title_id,
                'department_id'         => $staff_info['department_id'] ?? '',
                'store_id'              => $store_id,
                'store_name'            => $store_detail['name'],
                'store_region_id'       => $store_detail['manage_region'],
                'store_piece_id'        => $store_detail['manage_piece'],
                'employment_begin_date' => $employ_date,
                'employment_end_date'   => $employment_end_date,
                'employment_days'       => $employment_days,
                'shift_id'              => $shift_id,
                'shift_extend_id'       => $shift_info['shift_extend_id']??0,
                'shift_start'           => $shift_info['first_start'] ?? '',
                'shift_end'             => $shift_info['first_end'] ?? '',
                'demand_num'            => $demand_num,
                'reason_type'           => $reason_type,
                'reason'                => $reason,
                'status'                => enums::$audit_status['panding'],
                'final_audit_num'       => $demand_num,
            ];

            $db->begin();
            $apply_id = $this->store_support->insert_store_support('hr_store_apply_support' ,$insertData);
            if (empty($apply_id)) {
                $logger->write_log('hr_store_apply_support 插入失败 - 参数: '. json_encode($insertData));
                throw new Exception($this->getTranslation()->_('4008'));
            }

            $extend = $this->getIsManager($staff_info_id, $store_id);
            //创建审批流
            $server = new ApprovalServer($this->lang, $this->timezone);
            $requestId = $server->create($apply_id, AuditListEnums::APPROVAL_TYPE_SAS, $staff_info_id, null, $extend);
            if (!$requestId) {
                throw new Exception('创建审批流失败');
            }
            $db->commit();

            return $this->checkReturn([]);
        } catch (Exception $e) {
            $db->rollback();
            $logger->write_log('addStoreApplySupport'. $e->getMessage() . $e->getTraceAsString());
            return $this->checkReturn(-3, $e->getMessage());
        }
    }

    public function updateStoreApplySupport($paramIn = []) {
        $staff_info_id = $this->processingDefault($paramIn, 'staff_id', 2);
        $status  = $this->processingDefault($paramIn, 'status', 2);
        $reason  = $this->processingDefault($paramIn, 'reject_reason', 1);
        $support_id = $this->processingDefault($paramIn, 'audit_id', 2);
        $reason   = addcslashes(stripslashes($reason),"'");
        $demand_num = $this->processingDefault($paramIn, 'demand_num', 2);

        //详情
        $detail = $this->getStoreSupportApplyDetail($support_id);
        if (empty($detail)) {
            throw new Exception($this->getTranslation()->_('4008'), enums::$ERROR_CODE['1000']);
        }

        if ($detail['status'] != enums::$audit_status['panding'] && $status == enums::$audit_status['revoked']) {
            throw new Exception($this->getTranslation()->_('please try again'), enums::$ERROR_CODE['1000']);
        }

        try {
            //同意或者驳回等分开处理
            if ($status == enums::$audit_status['approved']) {
                //同意
                $server = new ApprovalServer($this->lang, $this->timezone);
                $server->approval($support_id, AuditListEnums::APPROVAL_TYPE_SAS, $staff_info_id);

                $this->getDI()->get('db')->updateAsDict(
                    'hr_store_apply_support',
                    ['final_audit_num' => $demand_num],
                    'id = '. $support_id
                );
            } else if ($status == enums::$audit_status['dismissed']) {
                //驳回
                $server = new ApprovalServer($this->lang, $this->timezone);
                $server->reject($support_id, AuditListEnums::APPROVAL_TYPE_SAS, $reason, $staff_info_id);
            } else {
                //撤销
                $server = new ApprovalServer($this->lang, $this->timezone);
                $server->cancel($support_id, AuditListEnums::APPROVAL_TYPE_SAS, $reason, $staff_info_id);
            }
            return $this->checkReturn([]);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("updateStoreApplySupport failure:" . $e->getMessage() . $e->getTraceAsString(), "notice");
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
    }

    //申请详情
    public function getDetail(int $auditId, $user, $comeFrom) {
        $result = $this->getStoreSupportApplyDetail($auditId);
        if (empty($result)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }

        //获取提交人用户信息
        $staff_info = (new StaffServer())->get_staff($result['staff_info_id']);
        if($staff_info['data']){
            $staff_info = $staff_info['data'];
        }

        $applying_num = $this->getStaffApplySupport($result['serial_no'], 1);
        $approve_num = $this->getStaffApplySupport($result['serial_no'], 2);

        if($result['job_title_id'] == enums::$job_title['branch_supervisor']) {
            $job_title_name = 'Branch Supervisor';
        } else {
            $job_title_name = UC('storeSupport')['jobTitles'][$result['job_title_id']] ?? '';
        }

        $detailLists = [
            'apply_parson'      => sprintf('%s ( %s )',$staff_info['name'] ?? '' , $staff_info['id'] ?? ''),
            'apply_department'  => sprintf('%s - %s',$staff_info['depart_name'] ?? '' , $staff_info['job_name'] ?? ''),
            'store_support_support_apply_type' => $this->getTranslation()->_('store_support_support_apply_type_1'),
            'store_support_staff_job_title' => $job_title_name,
            'store_name'        => $result['store_name'],
            'store_support_employment_begin_date' => $result['employment_begin_date'],
            'employment_days'   => $result['employment_days'],
            'work_shift'        => $result['work_shift'],
            //'store_support_demand_num'        => $result['final_audit_num'],
            'store_support_applying_num'      => $applying_num, //申请中人数
            'store_support_approve_num'       => $approve_num,  //已通过人数
            'store_support_reason_type'       => $this->getTranslation()->_('store_support_request_reason_'.$result['reason_type']),
            'store_support_reason'            => $result['reason'],
        ];

        //驳回状态，需要显示驳回原因
        if ($result['status'] == enums::$audit_status['dismissed']) {
            $detailLists = array_merge($detailLists, ['reject_reason' => $result['reject_reason'] ?? '']);
        }
        $returnData['data']['detail'] = $this->format($detailLists);

        $add_hour = $this->getDI()['config']['application']['add_hour'];
        $data = [
            'title'      => $this->auditlist->getAudityType(enums::$audit_type['SAS']),
            'id'         => $result['id'],
            'staff_id'   => $result['staff_info_id'],
            'type'       => enums::$audit_type['SAS'],
            'created_at' => date('Y-m-d H:i:s', strtotime($result['created_at']) + $add_hour * 3600),
            'updated_at' => date('Y-m-d H:i:s', strtotime($result['updated_at']) + $add_hour * 3600),
            'status'     => $result['status'],
            'status_text'=> $this->auditlist->getAuditStatus('10' . $result['status']),
            'serial_no'  => $result['serial_no'] ?? '',
            'demand_num' => $result['final_audit_num'],
            'applying_num' => $applying_num, //申请中人数
            'approve_num' => $approve_num,  //已通过人数
            'employment_days'   => $result['employment_days'],
        ];

        //当前用户是否已经审批
        $apply_num_edit = 0;
        if ($comeFrom == 2) { //获取审批人的审批状态
            $infos = AuditApprovalModel::findFirst([
                'conditions' => "biz_value = :value: and biz_type = :type: and approval_id = :approval_id: and deleted = 0",
                'bind' => [
                    'type'  => enums::$audit_type['SAS'],
                    'value' => $auditId,
                    'approval_id' => $user,
                ],
                'order' => 'id desc',
            ]);
            $state = $infos->getState() ?? 0;
            $option = ['beforeApproval' => "1,2", 'afterApproval' => '0'];

            $apply_num_edit = $state == 1 ? 1 : 0;
        } else { //获取申请的审批状态
            $state = $result['status'];
            $option = ['beforeApproval' => '3', 'afterApproval' => '0'];
        }
        $data['options'] = $this->getStaffOptions(['option' => $option, 'status' => $state]);

        array_splice($returnData['data']['detail'], 8, 0, [['key' => $this->getTranslation()->_('store_support_demand_num'), 'value' => $result['final_audit_num'], 'apply_num_edit' => $apply_num_edit, 'tips' => null]]);
        /*
        if ($result['status'] == 2) {
            $returnData['data']['confirm'] = [
                ['key' => $this->getTranslation()->_('final_num'), 'value' => $result['final_audit_num']],
            ];
        } else if ($result['status'] == 3) {
            $returnData['data']['confirm'] = [
                ['key' => $this->getTranslation()->_('reject_reason'), 'value' => $result['reject_reason']],
            ];
        }
        */
        if ($result['status'] == 3) {
            $returnData['data']['confirm'] = [
                ['key' => $this->getTranslation()->_('reject_reason'), 'value' => $result['reject_reason']],
            ];
        }

        $returnData['data']['head']   = $data;
        return $returnData;
    }

    /**
     * 设置回调属性
     * @param int $auditId
     * @param int $state
     * @param null $extend
     * @param bool $isFinal
     * @return mixed|void
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal) {
            // 更新 支援状态
            $support_status = $this->getSupportStatusById($auditId, $state);
            $params = ['status' => $state, 'support_status' => $support_status];
            if ($state == enums::APPROVAL_STATUS_APPROVAL) {
                $params['approval_agreed_time'] = gmdate('Y-m-d H:i:s');
            }
            $this->getDI()->get('db')->updateAsDict(
                'hr_store_apply_support',
                $params,
                'id = '.$auditId
            );
        }
    }

    /**
     * 生成概要信息
     * @param int $auditId
     * @param $user
     * @return mixed|void
     */
    public function genSummary(int $auditId, $user)
    {
        $info     = $this->getStoreSupportApplyDetail($auditId);
        if (!empty($info)) {
            if($info['job_title_id'] == enums::$job_title['branch_supervisor']) {
                $job_title_name = 'Branch Supervisor';
            } else {
                $job_title_name = UC('storeSupport')['jobTitles'][$info['job_title_id']] ?? '';
            }

            $param = [
                [
                    'key'   => "store_support_staff_job_title",
                    'value' => $job_title_name,
                ],
                [
                    'key'   => "store",
                    'value' => $info['store_name'],
                ],
                [
                    'key'   => "store_support_demand_num",
                    'value' => $info['final_audit_num'],
                ],
            ];
        }
        return $param ?? [];
    }

    /**
     * 获取审批流参数
     * @param $auditId
     * @param $user
     * @param null $state
     * @return mixed|void
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $auditInfo = HrStoreApplySupportModel::findFirst($auditId);
        if (empty($auditInfo)) {
            return false;
        }

        return $this->getIsManager($auditInfo->staff_info_id, $auditInfo->store_id);
    }

    //是否是 am dm store_manager 负责人
    public function getIsManager($staff_info_id, $store_id) {
        //是否AM
        $manageRegionAll = SysManageRegionModel::find([
            'conditions' => 'deleted = 0 and type = 1 ',
        ]);
        $region_arr = $manageRegionAll->toArray();
        $region_ids = array_column($region_arr, 'id');
        $region_manager_ids = array_column($region_arr, 'manager_id');
        //3-大区负责人
        if(in_array($staff_info_id, $region_manager_ids)) {
            $manageRegion = true;
        } else {
            $manageRegion = false;
        }
        //是否DM
        $managePiece = SysManagePieceModel::find([
            'conditions' => 'manager_id = :staff_id: and deleted = 0 and manage_region_id in ({manage_region_ids:array})',
            'bind'       => ['staff_id' => $staff_info_id, 'manage_region_ids' => $region_ids],
        ])->toArray();

        //是否网点负责人
        $manageStore = SysStoreModel::find([
            'conditions' => 'manager_id = :staff_id: and state = 1 and category in ({apply_category:array})',
            'bind'       => [
                'staff_id' => $staff_info_id,
                'apply_category' => $this->apply_store_category,
            ],
        ])->toArray();

        return [
            'is_am' => $manageRegion,
            'is_dm' => !empty($managePiece),
            'is_store_manager' => !empty($manageStore),
            'DM_store_ids' => [$store_id],
            'AM_store_ids' => [$store_id],
        ];
    }



    //申请详情
    public function getStoreSupportApplyDetail($support_id) {
        $store_apply_support_detail = HrStoreApplySupportModel::findFirst([
            'conditions' => "id = :support_id:",
            'bind' => [
                'support_id' => $support_id,
            ],
        ]);
        $detail = [];
        if($store_apply_support_detail){
            $detail = $store_apply_support_detail->toArray();
            $detail['work_shift'] = $this->getWorkShift($detail['shift_id'],$detail['shift_extend_id']);
        }
        return $detail;
    }

    //申请支援枚举值
    public function getStoreApplySysInfo(): array
    {
        $job_title_list = [
            [
                'job_title_id'   => enums::$job_title['van_courier'],
                'job_title_name' => 'Van Courier',
            ],
            [
                'job_title_id'   => enums::$job_title['bike_courier'],
                'job_title_name' => 'Bike Courier',
            ],
            [
                'job_title_id'   => enums::$job_title['car_courier'],
                'job_title_name' => 'Car Courier',
            ],
            [
                'job_title_id'   => enums::$job_title['branch_supervisor'],
                'job_title_name' => 'Branch Supervisor',
            ],
            [
                'job_title_id'   => enums::$job_title['dc_officer'],
                'job_title_name' => 'DC Officer',
            ],
        ];
        //指定班次 50-79
        $enable_shift_ids = (new SettingEnvServer())->getSetVal('by_store_support_shift_ids', ',');
        $shift_list       = (new ShiftRepository($this->timezone))->setEnableShiftIds($enable_shift_ids)->getV2ShiftList();
        foreach ($shift_list as &$item) {
            $item['shift_text'] = $item['first_start'] . '-' . $item['first_end'];
        }

        return [
            'job_title'  => $job_title_list,
            'shift_list' => $shift_list,
            'reason'     => $this->getOsStaffReqReason(),
        ];
    }

    //申请原因
    public function getOsStaffReqReason()
    {
        $retReason = [
            ["code" => 1, "title" => $this->getTranslation()->_('store_support_request_reason_1')],
            ["code" => 2, "title" => $this->getTranslation()->_('store_support_request_reason_2')],
            ["code" => 3, "title" => $this->getTranslation()->_('store_support_request_reason_3')],
            ["code" => 99, "title" => $this->getTranslation()->_('store_support_request_reason_99')],
        ];
        return $retReason;
    }

    //获取可申请支援的网点列表
    public function getSupportStoreList($paramIn) {
        try {
            $staff_info_id = $this->processingDefault($paramIn['userinfo'], 'staff_id', 2);
            //大区
            $region_list = SysManageRegionModel::find([
                'conditions' => "manager_id = :manager_id: and deleted = 0",
                'bind' => [
                    'manager_id' => $staff_info_id,
                ],
            ])->toArray();
            $region_store_list = [];
            if(!empty($region_list)) {
                $region_ids = array_column($region_list, 'id');
                $region_store_list = $this->getStoreListByRegionIds($region_ids, $this->apply_store_category);
                $region_store_list = array_column($region_store_list, null, 'id');
            }

            $piece_store_list = [];
            $piece_list = SysManagePieceModel::find([
                'conditions' => "manager_id = :manager_id: and deleted = 0",
                'bind' => [
                    'manager_id' => $staff_info_id,
                ],
            ])->toArray();
            if(!empty($piece_list)) {
                $piece_ids = array_column($piece_list, 'id');
                $piece_store_list = $this->getStoreListByPieceIds($piece_ids, $this->apply_store_category);
                $piece_store_list = array_column($piece_store_list, null, 'id');
            }

            //SP、BDC、CDC
            $store_list = SysStoreModel::find([
                'columns' => 'id, name, category, manage_region, manage_piece',
                'conditions' => "manager_id = :manager_id: and state = 1 and category in ({apply_category:array})",
                'bind' => [
                    'manager_id' => $staff_info_id,
                    'apply_category' => $this->apply_store_category,
                ],
            ])->toArray();

            if(!empty($store_list)) {
                $store_list = array_column($store_list, null, 'id');
            } else {
                $store_list = [];
            }

            $list = array_merge($region_store_list, $piece_store_list, $store_list);
            return $list;
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("getSupportStoreList 参数:" . json_encode($paramIn) .'; error:' . $e->getMessage() . $e->getTraceAsString());
            return [];
        }
    }

    //根据大区id 获取指定网点类型列表
    public function getStoreListByRegionIds($region_ids, $apply_category) {
        try {
            $store = SysStoreModel::find([
                'columns' => 'id, name, category, manage_region, manage_piece',
                'conditions' => "manage_region in ({region_ids:array}) and state = 1 and category in ({apply_category:array})",
                'bind' => [
                    'region_ids' => $region_ids,
                    'apply_category' => $apply_category,
                ],
            ])->toArray();
            return $store;
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("getStoreListByRegionIds 参数:" . json_encode($region_ids) .'; error:' . $e->getMessage() . $e->getTraceAsString());
            return [];
        }
    }

    //根据片区id 获取指定网点类型列表
    public function getStoreListByPieceIds($piece_ids, $apply_category) {
        try {
            $store = SysStoreModel::find([
                'columns' => 'id, name, category, manage_region, manage_piece',
                'conditions' => "manage_piece in ({piece_ids:array}) and state = 1 and category in ({apply_category:array})",
                'bind' => [
                    'piece_ids' => $piece_ids,
                    'apply_category' => $apply_category,
                ],
            ])->toArray();
            return $store;
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("getStoreListByPieceIds 参数:" . json_encode($piece_ids) .'; error:' . $e->getMessage() . $e->getTraceAsString());
            return [];
        }
    }

    //获取已申请的人数
    public function getStaffApplySupport($serial_no, $status) {
        $count = HrStaffApplySupportStoreModel::count([
            'conditions' => "store_apply_support_serial_no = :serial_no: and status = :status:",
            'bind' => [
                'serial_no' => $serial_no,
                'status' => $status,
            ],
        ]);
        return $count;
    }


}