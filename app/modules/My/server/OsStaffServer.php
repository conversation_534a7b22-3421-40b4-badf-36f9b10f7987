<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrShiftModel;
use FlashExpress\bi\App\Models\backyard\HrStaffOutsourcingModel;
use FlashExpress\bi\App\Models\backyard\OutsourcingCompanyModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Modules\My\enums\OsmEnums;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\HrStaffOutsourcingUrgentRepository;
use FlashExpress\bi\App\Repository\OsStaffRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\OsStaffServer AS GlobalBaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use Exception;

class OsStaffServer extends GlobalBaseServer
{
    /**
     * 添加外协员工申请
     * @param array $paramIn    传入参数
     * @return mixed
     * @throws Exception
     */
    public function addOsStaff($paramIn = [])
    {
        //[1]参数定义
        $staffId    = $this->processingDefault($paramIn,'staff_id');
        $jobId      = $this->processingDefault($paramIn,'job_id');
        $employDate = $this->processingDefault($paramIn,'employment_date');
        $employDays = $this->processingDefault($paramIn, 'employment_days');
        $shiftId    = $this->processingDefault($paramIn,'shift_id');
        $demendNum  = $this->processingDefault($paramIn,'demend_num');
        $reason     = $this->processingDefault($paramIn,'reason');
        $reason     = addcslashes(stripslashes($reason),"'");
        $submit_store_id = $this->processingDefault($paramIn, 'store_id');
        $reasonType = $this->processingDefault($paramIn,'reason_type', 2);
        $imagePathArr  = $this->processingDefault($paramIn,'image_path');
        $osType     = $this->processingDefault($paramIn,'os_type', 2);
        $store_id   = $this->processingDefault($paramIn,'organization_id');
        $out_company_id  = $this->processingDefault($paramIn, 'company_id', 2);
        $out_company_data= $this->processingDefault($paramIn, 'out_company_data',3);
        $osHireType      = $this->processingDefault($paramIn, 'hire_os_type');//雇佣类型
        $workPartition      = $this->processingDefault($paramIn, 'work_partition');//工作分区

        $staffInfo = $this->staff->checkoutStaff($staffId);

        //由于operation部门在ms中存的是子部门,
        //并且hr中job_title与部门的关联关系是job_title与一级部门的关联而不是子部门关联
        //该位置必须查询申请人在bi系统中的部门
        if ($staffInfo) {
            $department_id = $osType == enums::$os_staff_type['motorcade'] ? 32: $staffInfo['department_id'];
        } else {
            $department_id = null;
        }

        if (empty($department_id)) {
            throw new Exception($this->getTranslation()->_('err_msg_do_not_have_department'));
        }
        $envService               = new SettingEnvServer();
        $workPartitionStoreIds    = $envService->getSetValFromCache('hub_store_work_partition_store_ids',
            ',');
        $workPartitionJobTitleIds = $envService->getSetValFromCache('hub_store_work_partition_job_title_ids',
            ',');
        //校验hub工作分区
        if (in_array($submit_store_id, $workPartitionStoreIds) &&
            in_array($jobId, $workPartitionJobTitleIds)
            && (empty($workPartition) || !in_array($workPartition, enums::$hubWorkPartitionMY))) {
            throw new ValidationException($this->getTranslation()->_('work_partition_error'));
        }
        //申请的外协类型是短期或长期外协
        //只有OS类型的网点才可以申请Onsite Officer
        //只有USHOP、SHOP(pickup-only)类型的网点才可以申请Shop Officer
        /*
        if (in_array($osType, [enums::$os_staff_type['normal'], enums::$os_staff_type['long_term']])
            &&
            (
                $jobId == enums::$job_title['onsite_officer'] && $staff_info['category'] != enums::$stores_category['os']
                ||
                $jobId == enums::$job_title['shop_officer'] && !in_array($staff_info['category'], [
                    enums::$stores_category['shop_pickup_only'],
                    enums::$stores_category['shop_ushop']])
                ||
                $jobId == enums::$job_title['warehouse_staff_sorter'] && in_array($staff_info['category'], [
                    enums::$stores_category['shop_pickup_only'],
                    enums::$stores_category['shop_ushop'],
                    enums::$stores_category['os']])
            )
        ) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('err_msg_job_title_department_dismatch')));
        }
        */

        if (!empty($out_company_data)){
            if (count($out_company_data) != count(array_unique(array_column($out_company_data, 'out_company_id')))) {
                throw new ValidationException($this->getTranslation()->_('err_msg_out_company_repeat'));
            }
            foreach ($out_company_data as $v){
                if (empty($v['out_company_id']) || empty($v['demend_num']) || intval($v['out_company_id'])<=0 || intval($v['demend_num'])<=0){
                    throw new ValidationException($this->getTranslation()->_('err_msg_out_company_repeat'));
                }
            }
        }

        $shift_info  = $this->getShiftDetail($shiftId);
        $shift_start = '';
        $shift_end = '';
        if(!empty($shift_info)) {
            $shift_start = $shift_info['start'];
            $shift_end = $shift_info['end'];
        }
        //hub 外协 申请订单。
        $hubOsRoles    = (new SettingEnvServer())->getSetValFromCache('hub_os_roles', ',');
        if (array_intersect($hubOsRoles, $paramIn['param']['position_category'])) {
            if(!empty($submit_store_id)) {
                //分拨经理角色 网点是选择的网点
                $store_id = $submit_store_id;
            }
            $employDateShiftStart = strtotime($employDate . ' ' . $shift_start);
            if($jobId == enums::$my_job_title['outsource']) {
                if (time() > $employDateShiftStart) {
                    throw new ValidationException($this->getTranslation()->_('shift_error_less'));
                }

            } else {
                // 不可选择距离当前时间小于2小时开始的班次
                if (time() > ($employDateShiftStart - 2 * 60 * 60)) {
                    throw new ValidationException($this->getTranslation()->_('shift_error_2'));
                }
            }
        } else {
            //非车队外协需要验证
            //部门下是否存在该职位
            if ($osType != enums::$os_staff_type['motorcade']) {
                $relate = $this->os->getRelation($department_id, $jobId);
                if (empty($relate)) {
                    throw new ValidationException($this->getTranslation()->_('err_msg_job_title_department_dismatch'));
                }
            }

            //Line haul & Transportation部门的人，不可以申请普通外协、长期外协申请。
            //只可以申请[车队外协申请条件]
            //[车队外协申请条件][Line haul & Transportation部门(26)，角色为线路中控人员(31)/线路规划管理员的员工申请(32)]
            if ($staffInfo['department_id'] == 26 && in_array($osType, [enums::$os_staff_type['normal'], enums::$os_staff_type['long_term']])) {
                throw new ValidationException($this->getTranslation()->_('err_msg_do_not_have_department'));
            }

            //如果是车队外协需调整申请的网点

            $store_id = $osType == enums::$os_staff_type['motorcade'] ? $submit_store_id: $store_id;

        }

        //获取转岗审批流Code
        $extend['flow_code'] = $this->getOsStaffWorkflowRole([
            'job_id'        => $jobId,
            'os_type'       => $osType,
            'staff_id'      => $staffId,
            'department_id' => $staffInfo['department_id'],
        ]);
        //if (empty($extend['flow_code'])) {
        //    throw new Exception($this->getTranslation()->_('err_msg_job_title_department_dismatch'));
        //}


        //[3]组织数据插入业务主数据
        $db = $this->getDI()->get('db');
        $db->begin();

        try {
            $insertData = [
                'serial_no'         => 'OS' . $this->getRandomId(),
                'os_type'           => $osType,
                'staff_id'          => $staffId,
                'job_id'            => intval($jobId),
                'department_id'     => $department_id,
                'store_id'          => $store_id,
                'employment_date'   => $employDate,
                'employment_days'   => $employDays,
                'shift_id'          => $shiftId,
                'status'            => enums::$audit_status['panding'],
                'demend_num'      => !empty($out_company_data) ? array_sum(array_column($out_company_data,'demend_num')) : $demendNum,
                'final_audit_num' => !empty($out_company_data) ? array_sum(array_column($out_company_data,'demend_num')) : $demendNum,
                'reason_type'       => $reasonType,
                'reason'            => $reason,
                'wf_role'           => 'os_new',
                'out_company_id'  => !empty($out_company_id) ? $out_company_id : 0,
                'out_company_data'=> !empty($out_company_data) ? json_encode($out_company_data, JSON_UNESCAPED_UNICODE) : '',
                'shift_begin_time'=> $shift_start,
                'shift_end_time'  => $shift_end,
                'hire_os_type'    => $osHireType,//雇佣类型
                'work_partition' => $workPartition,
            ];
            $osStaffId = $this->os->insertOsStaff($insertData);
            if (empty($osStaffId)) {
                throw new Exception($this->getTranslation()->_('4008'));

            }

            //插入业务关联图片
            if(!empty($imagePathArr)) {
                $insertImgData  = [];

                foreach($imagePathArr as $image) {
                    $insertImgData[] = [
                        'id'         => $osStaffId,
                        'image_path' => $image
                    ];
                }
                $this->pub->batchInsertImgs($insertImgData, 'OUTSOURCING_STAFF');
            }

            //HRBP根据申请的网点查找
            $extend['store_id'] = $store_id;

            //这里是 from 表单的内容,用于查找审批人
            $extend['from_submit'] = [
                'sys_store_id' => $store_id,
                'audit_type'   => AuditListEnums::APPROVAL_TYPE_OS,
            ];

            //创建
            $server = new ApprovalServer($this->lang, $this->timezone);
            $requestId = $server->create($osStaffId, AuditListEnums::APPROVAL_TYPE_OS, $staffId,null, $extend);
            if (!$requestId) {
                throw new Exception('创建审批流失败');
            }

            $db->commit();
        } catch (ValidationException $vException){
	        $db->rollback();
            throw $vException;
        }  catch (Exception $e){
            $db->rollback();
            throw $e;
        }
        return $this->checkReturn([]);
    }


    /**
     * 获取审批流角色
     * 详细见 doc/OS_workflow.md
     * @param $paramIn
     * @return string
     */
    public function getOsStaffWorkflowRole($paramIn)
    {
        //[1]获取参数
        $staffId    = $this->processingDefault($paramIn,'staff_id');
        $jobId      = $this->processingDefault($paramIn,'job_id');
        $osType     = $this->processingDefault($paramIn,'os_type', 2);
        $departmentId = $this->processingDefault($paramIn,'department_id', 2);

        //[2]获取员工详情
        $staffInfo = $this->staff->getStaffPositionv3($staffId);

        //[3]找到合适的审批流
        if ($osType == enums::$os_staff_type['normal']) { //短期外协
            if($jobId == enums::$job_title['hub_staff']) { //HUB STAFF职位
                //申请人 -> 上级 -> 30286
                $flowCode = 1;
            } else if (in_array($jobId, [enums::$job_title['bike_courier'],
                enums::$job_title['van_courier'],
                enums::$job_title['boat_courier'],
                enums::$job_title['warehouse_staff_sorter'],
                enums::$job_title['car_courier']
            ] //bike courier、van courier、car courier、boat courier、warehouse staff(sorter)职位
            )) {
                if ($departmentId == enums::$department['Fulfillment'] && $jobId == enums::$job_title['warehouse_staff_sorter']) {
                    //申请人→网点负责人→fulfillment部门负责人
                    $flowCode = 6;
                } else {
                    //申请人 -> 上级 -> 申请人所在网点对应的大区经理
                    $flowCode = 2;
                }
            } else {
                $flowCode = 2;
            }
        } else if ($osType == enums::$os_staff_type['long_term']) { //长期外协
            //申请人-> 上级 -> 所属区域的HRBP终审
            $flowCode = 7;
        } else { //车队外协申请
            //申请人-> 51290
            $flowCode = 5;
        }

        //onsite_officer、shop_officer的长期、短期外协申请审批流是一样的
        if ($jobId == enums::$job_title['onsite_officer'] && $staffInfo['category'] == enums::$stores_category['os']) {
            //申请人 -> 申请人所在网点负责人 -> 21715
            $flowCode = 4;
        } else if ($jobId == enums::$job_title['shop_officer'] && in_array($staffInfo['category'], [enums::$stores_category['shop_pickup_only'],enums::$stores_category['shop_ushop']])) {
            //申请人 -> 网点负责人 -> 20467 (Shop Operation Manager) ->17574
            $flowCode = 3;
        }

        return $flowCode;
    }

    /**
     * 获取请求列表
     */
    public function getRequestList($paramIn = [])
    {
        //[1]参数定义
        $staffInfo  = $this->processingDefault($paramIn, 'userinfo');
        $jobId = $this->processingDefault($paramIn, 'job_id', 2);
        //查班次用
        $employment_date = $this->processingDefault($paramIn, 'employment_date');
        //雇佣类型
        $hireOsType = $this->processingDefault($paramIn, 'hire_os_type');
        $storeId    = $staffInfo['organization_id'] ?? '';
        $storeName  = $staffInfo['organization_name'] ?? '';
        $type       = $staffInfo['organization_type'] ?? '';
        $jobTitle   = $staffInfo['job_title'] ?? '';
        $positions  = $staffInfo['positions'] ?? [];

        //[2]获取网点详情
        $storeInfo = (new SysStoreServer())->getStoreByid($storeId);

        //hub可以申请长期外协的角色
        $hubRequestRole = UC('outsourcingStaff')['osLongPeriodRequestRole'];

        //可以申请车队外协申请的角色
        $motorcadeRequestRole = UC('outsourcingStaff')['osMotorcadeRequestRole'];

        if (in_array($storeInfo['category'], [enums::$stores_category['hub'], enums::$stores_category['os'], enums::$stores_category['shop_pickup_only'], enums::$stores_category['shop_pickup_delivery'], enums::$stores_category['shop_ushop']])
        ) {
            $ret['os_mode'] = enums::$os_staff_type['long_term'];
            $ret['os_type'] = [
                ["code" => 1, "title" => $this->getTranslation()->_('os_request_mode_1')],
                ["code" => 2, "title" => $this->getTranslation()->_('os_request_mode_2')],
            ];
        } else if ($type == 2 && $storeId == 319 && array_intersect($positions, array_keys($motorcadeRequestRole))) { //车队外协
            $ret['os_mode'] = enums::$os_staff_type['motorcade'];
            $ret['os_type'] = [
                ["code" => 3, "title" => $this->getTranslation()->_('os_request_mode_3')],
            ];
        } else { //短期外协
            $ret['os_mode'] = enums::$os_staff_type['normal'];
            $ret['os_type'] = [
                ["code" => 1, "title" => $this->getTranslation()->_('os_request_mode_1')],
            ];
        }
        $ret['store_list'] = [];
        //hub 外协工单，分拨经理 有入口权限
        $is_out_company   = false;
        $out_company_list = [];
        $hubOsRoles    = (new SettingEnvServer())->getSetValFromCache('hub_os_roles', ',');
        if (array_intersect($hubOsRoles, $positions)) {
            //短期外协
            $ret['os_mode']   = enums::$os_staff_type['normal'];
            $ret['os_type']   = [
                ['code' => 1, 'title' => $this->getTranslation()->_('os_request_mode_1')],
                ["code" => 2, "title" => $this->getTranslation()->_('os_request_mode_2')],
            ];
            $is_out_company   = true;
            $out_company_list = $this->getOutCompanyInfo();
            $this->setDisableShift([]);//分拨经理查看全部的班次
            $ret['store_list'] = $this->getStaffManageStore($staffInfo['staff_id']);
        }

        //获取班次下拉 全天或者半天
        $shiftInfo = [];
        if(!empty($hireOsType) && !empty($jobId)){
            if($jobId == enums::$job_title['dc_officer'] && $hireOsType == HrStaffOutsourcingModel::HIRE_OS_TYPE_3){
                $shiftInfo =  $this->getShiftList($jobId, HrShiftModel::SHIFT_GROUP_HALF_DAY_SHIFT, $employment_date);
            }else{
                $shiftInfo = $this->getShiftList($jobId, HrShiftModel::SHIFT_GROUP_FULL_DAY_SHIFT, $employment_date);
            }
        }
        $settingEnvServer = new SettingEnvServer();
        $department = (new DepartmentRepository())->getDepartmentNameById($staffInfo['department_id'] ?? '');
        $ret['store_name']  = $ret['os_mode'] == enums::$os_staff_type['motorcade'] ? '': $storeName;
        $ret['department']  = $department;
        $ret['shift_info']  = $shiftInfo;
        $ret['reason']      = $this->getOsStaffReqReason($storeId);
        $ret['is_out_company']   = $is_out_company;
        $ret['out_company_list'] = $out_company_list;
        $ret['work_partition_store_ids'] = $settingEnvServer->getSetValFromCache('hub_store_work_partition_store_ids',
            ',');
        $ret['work_partition_job_ids'] = $settingEnvServer->getSetValFromCache('hub_store_work_partition_job_title_ids',
            ',');
        $ret['work_partitions'] =$this->getHubWorkPartition();
        return $ret;
    }
    /**
     * 获取My hub 网点分区
     * @return array
     */
    public function getHubWorkPartition(): array
    {
        $returnData = [];
        foreach (enums::$hubWorkPartitionMY as $code) {
            $returnData[] = [
                'value' => $code,
                'label' => $this->getTranslation()->_('hub_work_partition_' . strtolower($code)),
            ];
        }
        return $returnData;
    }
    public function getCondition($jobId = 0, $shift_group)
    {
        $shift_group = in_array($jobId, [enums::$my_job_title['security_outsource'], enums::$my_job_title['outsource']]) ? HrShiftModel::SHIFT_GROUP_HUB_SECURITY_OUTSOURCE_SHIFT : $shift_group;

        $condition = "shift_attendance_type = :shift_attendance_type: and shift_group = :shift_group:";
        $bind['shift_attendance_type'] =  HrShiftModel::SHIFT_ATTENDANCE_TYPE_FIXED;
        $bind['shift_group']           = $shift_group;

        return [$condition, $bind];
    }

    /**
     * 马来重写
     * @param int $jobId
     * @param int $shift_group
     * @param string $employment_date
     * @return array
     */
    public function getShiftList($jobId = 0, $shift_group = HrShiftModel::SHIFT_GROUP_FULL_DAY_SHIFT, $employment_date = '')
    {
        [$condition, $bind] = $this->getCondition($jobId, $shift_group);

        $disableShiftIds = $this->getDisableShift();
        if (!empty($disableShiftIds)) {
            $condition   .= " AND id NOT IN ({ids:array})";
            $bind['ids'] = $disableShiftIds;
        }

        $shiftList = HrShiftModel::find([
            'conditions' => $condition,
            'bind'       => $bind,
            'order'      => "type,start asc",
        ])->toArray();

        $current_day  = date('Y-m-d', time());
        $current_time = date('Y-m-d H:i:s', time());

        $effective_date_add_hours = (new SettingEnvServer())->getSetVal('os_effective_date_add_hours');
        $effective_date_add_hours = !empty($effective_date_add_hours) ? $effective_date_add_hours : 3;
        $employment_time          = date('Y-m-d H:i:s', strtotime($current_time) + ($effective_date_add_hours * 3600));

        foreach ($shiftList as $k => &$item) {
            if ($item["id"] == self::BOAT_COURIER_SHIFT_ID && $jobId != enums::$job_title['boat_courier']) {
                unset($shiftList[$k]);
                continue;
            }
            //非 Outsource ，Security Outsource 走这个逻辑
            if (!empty($employment_date) && $current_day == $employment_date && !in_array($jobId, array_keys(OsmEnums::$job_title))) {
                $shift_time = $current_day . ' ' . $item['start'];
                if (strtotime($employment_time) > strtotime($shift_time)) {
                    unset($shiftList[$k]);
                    continue;
                }
            }

            switch ($item['type']) {
                case 'EARLY':
                    $item['type_text'] = $this->getTranslation()->_('shift_early');
                    break;
                case 'MIDDLE':
                    $item['type_text'] = $this->getTranslation()->_('shift_middle');
                    break;
                case 'NIGHT':
                    $item['type_text'] = $this->getTranslation()->_('shift_night');
                    break;
                default:
                    break;
            }
        }
        return array_values($shiftList);
    }


    /**
     * 获取职位列表
     * @param array $paramIn
     * @return array
     */
    public function getOsJobTitleList($paramIn = [])
    {
        //[1]获取参数
        $osType     = $this->processingDefault($paramIn, 'type');
        $staffInfo  = $this->processingDefault($paramIn, 'userinfo');

        //hub 外协 申请订单。
        $hubOsRoles    = (new SettingEnvServer())->getSetValFromCache('hub_os_roles', ',');

        //获取可以申请的职位
        if(array_intersect($hubOsRoles, $staffInfo['positions'])) {
            if($osType == enums::$os_staff_type['normal']) {
                return [
                    [
                        'job_id' => enums::$my_job_title['outsource'],
                        'job_title' => 'Outsource',
                    ]
                ];
            } else {
                return [
                    [
                        'job_id' => enums::$my_job_title['security_outsource'],
                        'job_title' => 'Security Outsource',
                    ]
                ];
            }
        }
        //[2]组织列表
        //1)短期外协职位下拉列表[全部职位]
        //2)长期外协职位下拉列表[Hub Staff|Onsite Officer|Shop Officer]
        //3)车队外协下拉列表[Van Courier]
        switch ($osType) {
            case enums::$os_staff_type['normal']: //短期外协
                $returnArr = $this->getShortTermJobTitleList(['staff_id' => $staffInfo['id']]);
                break;
            case enums::$os_staff_type['long_term']: //长期外协
                $returnArr = $this->getLongTermJobTitleList(['staff_id' => $staffInfo['id']]);
                break;
            case enums::$os_staff_type['motorcade']: //车队外协
                $returnArr = [
                    [
                        'job_id'        => enums::$job_title['van_courier'],
                        'job_title'     => 'Van Courier',
                    ]
                ];
                break;
            default:
                $returnArr = [];
                break;
        }
        return $returnArr;
    }

    /**
     * 获取外协员工职位列表
     * @param array $paramIn
     * @return array
     */
    public function getShortTermJobTitleList($paramIn = [])
    {
        //[1]获取参数
        $staffId = $this->processingDefault($paramIn, 'staff_id');

        //[2]获取申请人所在部门
        $staffInfo = $this->staff->getStaffInfoById($staffId);

        $department = SettingEnvModel::find([
            'conditions' => 'code in ({code:array})',
            'bind' => [
                'code' => ['dept_os_network_management_id', 'dept_os_shop_management_id', 'dept_os_fulfillment_id', 'dept_os_my_hub_management_id']
            ]
        ])->toArray();
        $department = array_column($department, 'set_val', 'code');
        switch ($staffInfo['sys_department_id']) {
            case $department['dept_os_network_management_id']: //Malaysia Network Management 316
                $returnArr     = [
                    [
                        'job_id'        => enums::$job_title['van_courier'],
                        'job_title'     => 'Van Courier',
                    ],
                    [
                        'job_id'        => enums::$job_title['bike_courier'],
                        'job_title'     => 'Bike Courier',
                    ],
                    [
                        'job_id'        => enums::$job_title['dc_officer'],
                        'job_title'     => 'DC Officer',
                    ],
                    [
                        'job_id'        => enums::$job_title['tricycle_courier'],
                        'job_title'     => 'Tricycle Courier',
                    ],
                    [
                        'job_id'        => enums::$job_title['car_courier'],
                        'job_title'     => 'Car Courier',
                    ],
                ];
                if ($this->os->isStoreIsland($staffInfo['sys_store_id'])) {
                    $returnArr = array_merge($returnArr, [
                        [
                            'job_id'        => enums::$job_title['boat_courier'],
                            'job_title'     => 'Boat Courier',
                        ]
                    ]);
                }
                break;
            case $department['dept_os_shop_management_id']: //Malaysia Shop Management 337
                $returnArr     = [
                    [
                        'job_id'        => enums::$job_title['shop_officer'],
                        'job_title'     => 'Shop Officer',
                    ],
                ];
                break;
            case $department['dept_os_fulfillment_id']: //Malaysia Fulfillment 244
                $returnArr     = [
                    [
                        'job_id'        => enums::$job_title['warehouse_staff_sorter'],
                        'job_title'     => 'Warehouse Staff (Sorter)',
                    ]
                ];
                break;
            case $department['dept_os_my_hub_management_id']: //Malaysia Hub Management 318
                $returnArr     = [
                    [
                        'job_id'        => enums::$job_title['onsite_staff'],
                        'job_title'     => 'Onsite Staff',
                    ],
                    [
                        'job_id'        => enums::$job_title['hub_staff'],
                        'job_title'     => 'Hub Operator',
                    ]
                ];
                break;
            default:
                $returnArr = [];
                break;
        }
        return $returnArr;
    }

    /**
     * 获取有效的外协员工列表
     * @param array $paramIn
     * @return array
     */
    public function getLongTermJobTitleList($paramIn = [])
    {
        //[1]获取参数
        $staffId = $this->processingDefault($paramIn, 'staff_id');

        //[2]获取申请人所在部门
        $staff_info = $this->staff->getStaffInfoById($staffId);

        //申请列表
        //1)Shop Pickup Only & Shop Ushop类型网点可以申请Shop Officer
        //2)OS类型网点可以申请Onsite Officer
        //3)HUB类型可以申请Hub Staff
        if (in_array($staff_info['category'], [enums::$stores_category['shop_pickup_only'], enums::$stores_category['shop_ushop'], enums::$stores_category['shop_pickup_delivery']])) {
            $returnArr     = [
                [
                    'job_id'        => enums::$job_title['shop_officer'],
                    'job_title'     => 'Shop Officer',
                ]
            ];

        } else if (in_array($staff_info['category'], [enums::$stores_category['os']])) {
            $returnArr     = [
                [
                    'job_id'        => enums::$job_title['onsite_officer'],
                    'job_title'     => 'Onsite Officer',
                ]
            ];
        } else {
            $returnArr     = [
                [
                    'job_id'        => enums::$job_title['hub_staff'],
                    'job_title'     => 'hub operator',
                ]
            ];
        }
        return $returnArr;
    }

// -------- country 迁移过来的------

    //获取审批详情的 tbl 列表
    public function get_apply_form($returnData, $timezone)
    {
//        $this->os             = new OsStaffRepository($this->timeZone);
        $data                 = [];
        $returnData['extend'] = [];
        $start_time           = strtotime(date("Y-m-d", strtotime("-4 day")));
        $end_time             = strtotime(date("Y-m-d", strtotime("-1 day")));
        $ret                  = $this->os->getOsStaffReferInfo($returnData['store_id'], $start_time, $end_time);
        if ($ret) {
            //在职van bike人数
            $van_cnt  = $this->os->getCourierCnt($returnData['store_id'], 110);
            $bike_cnt = $this->os->getCourierCnt($returnData['store_id'], 13);
            //在职仓管人数
            $soles_cnt = $this->os->getCourierSoles($returnData['store_id'], 2);
        }
        if ((isset($ret['avg']) && $ret['avg']) || (isset($ret['cnt']) && $ret['cnt']) || (isset($ret['undertake']) && $ret['undertake'])) {

            $dates     = DateHelper::DateRange($start_time, $end_time);
            $van_cnt   = $van_cnt ?? 0;
            $bike_cnt  = $bike_cnt ?? 0;
            $soles_cnt = $soles_cnt ?? 0;
            foreach ($dates as $date) {
                $date = date("m-d", strtotime($date));
                //揽件量
                $delivery_undertake = $ret['undertake'][$date] ?? 0;
                //应派件数
                $delivery_cnt = intval($ret['cnt'][$date] ?? 0);
                //人均妥投
                $parcel_avg = $ret['avg'][$date] ?? 0;
                //人均效能
                $delivery_avg = $soles_cnt == 0 ? 0 : round(($delivery_cnt + $delivery_undertake) / $soles_cnt, 2);
                $data[]       = [
                    'stat_date'          => ['num'    => $date,               //日期
                        'is_red' => 1
                    ],
                    'delivery_undertake' => ['num'    => $delivery_undertake, //揽件量
                        'is_red' => 1
                    ],
                    'delivery_cnt'       => ['num'    => $delivery_cnt,       //应派件数
                        'is_red' => 1
                    ],
                    'parcel_per'         => ['num'    => $parcel_avg,         //人均妥投
                        'is_red' => 1
                    ],
                    'delivery_avg'       => ['num'    => $delivery_avg,       //人均效能[仓管人效]
                        'is_red' => 1
                    ],
                ];
            }
        }
        $retData              = $this->generateTable($this->getTranslation()->_('last_4_days_parcel'), $data);
        $retData['van_cnt']   = $van_cnt ?? 0;
        $retData['bike_cnt']  = $bike_cnt ?? 0;
        $retData['soles_cnt'] = $soles_cnt ?? 0;
        array_push($returnData['extend'], $retData);
        return $returnData['extend'];
    }


    /**
     * 组织表数据
     * @param string $title     表头
     * @param array  $tableData 表数据
     * @return array
     */
    public function generateTable($title, $tableData)
    {
        $returnData = [];
        if ($tableData) {
            $detail = [];
            foreach ($tableData as $k => $v) {
                $detail[] = [
                    $v['stat_date'],
                    $v['delivery_undertake'] ?? '',
                    $v['delivery_cnt'] ?? '',
                    $v['parcel_per'] ?? '',
                    $v['delivery_avg'] ?? '',
                ];
            }

            $returnData = [
                'title'     => $title,
                'title_two' => '',
                'th'        => [
                    $this->getTranslation()->_('date'),
                    $this->getTranslation()->_('undertake_count'),
                    $this->getTranslation()->_('parcel_count'),
                    $this->getTranslation()->_('parcel_per'),
                    $this->getTranslation()->_('parcel_avg'),
                ],
                'td'        => $detail
            ];
        }
        return $returnData;
    }


    public function get_this_month_os_happening($jobId, $store_id, $submit_store_id, $osType, $timezone, $roles = [])
    {
        $returnData = [
            'is_remind' => 1,  //1 不需要提醒 2 需要提醒
            'info'      => [
                'store_name'         => '',
                'os_num'             => ['num' => "0", 'is_remind' => 1],//累计外协人次
                'inefficient_os_num' => ['num' => "0", 'is_remind' => 1],//累计低效外协
                'inefficient_os_pe'  => ['num' => "0%", 'is_remind' => 1],//低效外协占比
            ]
        ];
        return $returnData;
    }

    public function get_os_risk_prompt($jobId, $store_id, $submit_store_id, $osType, $timezone)
    {

        $returnData = [
            'is_remind' => 1,//1 不需要提醒 2 需要提醒
            'info_list' => [//返回 ['date_val'='6/09','compliance_line'=>100,'formal_human_effect'=>10];  //达标线   正式员工派件人效
            ],
            'x'         => 0,//没有达标的天数
        ];
        return $returnData;

    }

    // ------------------ country  迁移 结束-----------

    /**
     * [2.4]校验天数
     * @param $employmentDays
     * @param $osStaffInfo
     * @return bool
     * @throws Exception
     */
    public function checkEmploymentDays($employmentDays, $osStaffInfo)
    {
        //[2.4]校验天数
        if (isset($employmentDays) && $employmentDays) {
            if (in_array($osStaffInfo['os_type'], [enums::$os_staff_type['motorcade'], enums::$os_staff_type['normal']])) { //车队外协 、短期外协都是1~7天
                if ($employmentDays < 1 || $employmentDays > 7) {
                    throw new Exception("'employment_days' invalid input", enums::$ERROR_CODE['1000']);
                }
            } else { //长期外协是90到365天

                //hub 外协 申请订单。
                if ($osStaffInfo['job_id'] ==  enums::$my_job_title['security_outsource'] && $osStaffInfo['os_type'] == enums::$os_staff_type['long_term']) {
                    if($employmentDays < 15 || $employmentDays > 90) {
                        throw new Exception("'employment_days' invalid input", enums::$ERROR_CODE['1000']);
                    }
                    return true;
                }

                if ($employmentDays < 90 || $employmentDays > 365) {
                    throw new Exception("'employment_days' invalid input", enums::$ERROR_CODE['1000']);
                }
            }
        }
        return true;
    }


    /**
     * 可以修改需求人数的职位
     * @return array
     */
    protected function getOutsourceCanEditNnumJobTitle():array
    {
        return [enums::$my_job_title['outsource']];
    }
    /**
     * 更新 外协申请，外协公司人数信息
     * @param $out_company_list
     * @param $osStaffInfo
     * @return array
     */
    public function updateOutCompanyData($out_company_list, $osStaffInfo)
    {
        if (empty($out_company_list) || empty($osStaffInfo['out_company_data'])) {
            return [];
        }
        if (!in_array($osStaffInfo['job_id'], $this->getOutsourceCanEditNnumJobTitle())) {
            return [];
        }

        $out_company_data_arr = json_decode($osStaffInfo['out_company_data'], true);
        $out_company_listId = array_column($out_company_list, 'final_audit_num', 'out_company_id');
        $final_audit_num = 0;
        foreach ($out_company_data_arr as $key => $oneData) {
            if (!isset($out_company_listId[$oneData['out_company_id']])) {
                continue;
            }

            if($oneData['demend_num'] < $out_company_listId[$oneData['out_company_id']]) {
                $outsourcing_company_model = new OutsourcingCompanyModel();
                $outsourcing_company       = $outsourcing_company_model->getOneById($oneData['out_company_id']);
                throw new ValidationException($this->getTranslation()->_('approved_number_cannot_exceed_apply', ['company_name' => $outsourcing_company['company_name']]));

            }

            if($out_company_listId[$oneData['out_company_id']] <= 0) {
                throw new ValidationException($this->getTranslation()->_('must_be_greater'));
            }

            $out_company_data_arr[$key]['final_audit_num'] = $out_company_listId[$oneData['out_company_id']];
            $final_audit_num = $final_audit_num + $out_company_listId[$oneData['out_company_id']];
        }

        $paramData['out_company_data'] = json_encode($out_company_data_arr, JSON_UNESCAPED_UNICODE);
        //核定人数
        $paramData['final_audit_num'] = $final_audit_num;

        return $paramData;
    }

    public function checkNeedAudit($params)
    {
        $demend_num = $params['demend_num'];//当前申请，所需人数

        $where['job_id']          = $params['job_id'];
        $where['store_id']        = $params['store_id'];
        $where['employment_date'] = $params['employment_date'];
        $where['shift_id']        = $params['shift_id'];
        $where['status']          = enums::$audit_status['approved'];
        $where['is_audit']        = $params['is_audit'];
        //查询经过审批的 申请。
        $staffOutsourcing = OsStaffRepository::getStaffOutsourcingList($where);

        //需要走审批
        if (empty($staffOutsourcing)) {
            return true;
        }

        //人工审批通过的总人数
        $final_audit_num = 0;
        foreach ($staffOutsourcing as $oneOutsourcing) {
            $final_audit_num = $final_audit_num + $oneOutsourcing['final_audit_num'];
        }

        //查询所有审批通过申请
        unset($where['is_audit']);
        $allStaffOutsourcing = OsStaffRepository::getStaffOutsourcingList($where);

        $currentNum = $demend_num;
        foreach ($allStaffOutsourcing as $oneStaffOutsourcing) {
            if(empty($oneStaffOutsourcing['out_company_data'])) {
                continue;
            }
            $out_company_data = json_decode($oneStaffOutsourcing['out_company_data'], true);

            foreach ($out_company_data as $oneCompany) {
                if(!empty($oneCompany['apply_update_num'])) {
                    $currentNum = $currentNum + $oneCompany['apply_update_num'];
                    continue;
                }

                if(!empty($oneCompany['final_audit_num'])) {
                    $currentNum = $currentNum + $oneCompany['final_audit_num'];
                    continue;
                }

                if(!empty($oneCompany['demend_num'])) {
                    $currentNum = $currentNum + $oneCompany['demend_num'];
                    continue;
                }
            }
        }

        $this->getDI()->get('logger')->write_log(['add-os-checkAutoPass' => ['params' => $where,'final_audit_num' => $final_audit_num, 'currentNum' => $currentNum]], 'info');

        if($final_audit_num >= $currentNum) {
            //不需要走审批
            return false;
        }
        //需要走审批
        return true;
    }

    /**
     *
     * @param $auditId
     * @return bool
     */
    public function checkAutoPass($auditId)
    {
        $osStaffInfo = OsStaffRepository::getStaffOutsourceOne(['id' => $auditId]);

        $checkData['job_id']          = $osStaffInfo['job_id'];
        $checkData['store_id']        = $osStaffInfo['store_id'];
        $checkData['employment_date'] = $osStaffInfo['employment_date'];
        $checkData['shift_id']        = $osStaffInfo['shift_id'];
        $checkData['demend_num']      = $osStaffInfo['demend_num'];
        $checkData['is_audit']        = HrStaffOutsourcingModel::IS_AUDIT_YES;
        $isAudit                      = $this->checkNeedAudit($checkData);
        //不需要审批，则标记一下。
        if (!$isAudit) {
            $this->getDI()->get('db')->updateAsDict('hr_staff_outsourcing',
                ["is_audit" => HrStaffOutsourcingModel::IS_AUDIT_NO], [
                    'conditions' => "id = " . $auditId,
                ]);
        }

        return !$isAudit;
    }
}