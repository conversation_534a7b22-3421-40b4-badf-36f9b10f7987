<?php
/**
 * Author: Bruce
 * Date  : 2022-06-16 17:14
 * Description:
 */

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Server\StaffServer as GlobalServer;


class StaffServer extends GlobalServer
{
    public static $show_job_title = [13,110,1199,37];
    public static $counselor_department_id = [316];
    /**
     * 获取Network部门清单
     * @return array
     */
    public function getNetWorkInfo()
    {
        return (new DepartmentRepository())->getDepartmentChildInfo(self::$counselor_department_id);//network
    }

    /**
     * 获取展示辅导员的职位
     * @return array
     */
    public function getShowJobTitle() :array
    {
        return self::$show_job_title;
    }

}