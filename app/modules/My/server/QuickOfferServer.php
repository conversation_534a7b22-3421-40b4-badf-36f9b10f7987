<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\CommunicationResumeLogModel;
use FlashExpress\bi\App\Models\backyard\HrAnnexModel;
use FlashExpress\bi\App\Models\backyard\HrBlackListModel;
use FlashExpress\bi\App\Models\backyard\HrEconomyAbilityModel;
use FlashExpress\bi\App\Models\backyard\HrFamilyModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\hrHcRelateManagerModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewModel;
use FlashExpress\bi\App\Models\backyard\HrResumeFilterModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysProvinceModel;
use FlashExpress\bi\App\Models\backyard\whrDataPermissionV2GroupManagePieceModel;
use FlashExpress\bi\App\Models\backyard\whrDataPermissionV2GroupManageRegionModel;
use FlashExpress\bi\App\Repository\HrJDRepository;
use FlashExpress\bi\App\Server\ApprovalFinderServer;
use FlashExpress\bi\App\Server\OutsourcingBlackListServer;
use FlashExpress\bi\App\Server\QuickOfferServer as BaseCancelQuickOfferServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\SysServer;
use WebGeeker\Validation\Validation;

class QuickOfferServer extends BaseCancelQuickOfferServer
{
    protected $notRequiredField = [
        'half_length_photo',
        'car_owner',
        'driver_number',
        'driving_license_type',
        'car_number',
        'gdl_driving_license_img',
        'relationship_call_name',
        'relationship_first_name',
        'relationship_last_name',
        'relationship_mobile',
        'relationship',
    ];//非必填字段

    protected $noNeedDriverLicenseJd = ['10914'];//DCO

    protected $needCarInfoJdType = [1,2,4,5,6,7];//需要填写车辆信息的jdType


    /**
     * 获取静态枚举
     * @return array
     */
    public function enums(): array
    {
        $result['type'] = $this->getType();
        $result['relationship_list'] = $this->getRelationshipList();
        $result['car_owner_list']    = $this->getCarOwnerList();
        return $result;
    }

    /**
     * 车辆来源
     * @return array[]
     */
    public function getCarOwnerList()
    {
        $t = $this->getTranslation();
        return [
            ['value' => 1, 'label' => $t->_('car_owner_1')],
            ['value' => 3, 'label' => $t->_('car_owner_3')],
        ];
    }

    protected function getVehicleTypeList(): array
    {
        $carTypes = (new SettingEnvServer())->getSetVal('car_types_group');
        $result   = [];
        if ($carTypes) {
            $carTypes = json_decode($carTypes, true);
            foreach ($carTypes as $jdId => $carIds) {
                foreach ($carIds as $carId) {
                    $result[$jdId][] = [
                        'value' => strval($carId),
                        'label' => HrResumeModel::CAR_TYPE_DESC[$carId],
                    ];
                }
            }
        }
        return $result;
    }

    protected function validationBlackList($paramIn)
    {
        $blackListExist = HrBlackListModel::findFirst([
                'conditions' => 'mobile = :mobile: and status  = 1',
                'bind'       => ['mobile' => $paramIn['phone']],
            ]
        );
        if ($blackListExist) {
            throw new BusinessException($this->getTranslation()->_('qo_error_msg_003',
                ['mobile' => $paramIn['phone']]));
        }

        $blackListExist = HrBlackListModel::findFirst([
                'conditions' => 'identity = :identity: and status  = 1',
                'bind'       => ['identity' => $paramIn['credentials_num']],
            ]
        );
        if ($blackListExist) {
            throw new BusinessException($this->getTranslation()->_('qo_error_msg_008',
                ['credentials_num' => $paramIn['credentials_num']]));
        }
        if (!empty($paramIn['credentials_num'])) {
            $outsourcingBlackList = (new OutsourcingBlackListServer())->check($paramIn['credentials_num'], 'winhr', false,$this->lang);
            if ($outsourcingBlackList && $outsourcingBlackList['is_black']){
                throw new BusinessException($this->getTranslation()->_('qo_error_msg_008',
                    ['credentials_num' => $paramIn['credentials_num']]));
            }
        }
    }

    /**
     * @throws BusinessException
     */
    protected function validationStartDate($paramIn)
    {
        if ($paramIn['expected_arrivaltime'] < date('Y-m-d') || $paramIn['expected_arrivaltime'] > date('Y-m-d',
                strtotime('+ 14 days'))) {
            throw  new BusinessException($this->getTranslation()->_('qo_error_msg_006'));
        }
    }


    protected function validationStaffInfoMobile($paramIn)
    {
        $phone   = $paramIn['phone'];
        $pad_len = strlen($phone) == 9 ? 10 : 11;
        $phone   = str_pad($phone, $pad_len, "0", STR_PAD_LEFT);

        $phoneExist = HrStaffInfoModel::findFirst([
            'conditions' => ' mobile = :mobile: and state in (1,3) and is_sub_staff = 0',
            'bind'       => ['mobile' => $phone],
        ]);
        if ($phoneExist) {
            throw new BusinessException($this->getTranslation()->_('qo_error_msg_002', ['mobile' => $phone]));
        }

        $identityExist = HrStaffInfoModel::findFirst([
            'conditions' => ' identity = :identity: and state in (1,3) and is_sub_staff = 0',
            'bind'       => ['identity' => $paramIn['credentials_num']],
        ]);
        if ($identityExist) {
            throw new BusinessException($this->getTranslation()->_('qo_error_msg_009',
                ['credentials_num' => $paramIn['credentials_num']]));
        }
    }

    /**
     * 获取jd
     * @param $type
     * @return array
     * @throws BusinessException
     */
    public function getJDList($type): array
    {
        $key = self::$type_setting_key_map[$type] ?? '';

        $settingServer = new SettingEnvServer();
        $qo_jds        = $settingServer->getSetVal($key, ',');
        if (empty($qo_jds)) {
            return [];
        }
        $jdList          = HrJDRepository::getJDList($qo_jds);
        $vehicleTypeList = $this->getVehicleTypeList();
        foreach ($jdList as &$item) {
            $item['vehicle_type_list']      = $vehicleTypeList[$item['job_id']] ?? [];
            //是否需要车辆类型
            $item['is_need_vehicle_type']   = !empty($item['vehicle_type_list']);
            /**
             * @see QuickOfferServer::getJdType
             */
            $jd_type = $this->getJdTypeFromCache($item['job_id']);
            //是否需要驾照
            $item['is_need_driver_license'] = in_array($jd_type, $this->needCarInfoJdType);
            //是否需要车辆信息
            $item['is_need_vehicle_info'] = in_array($jd_type, $this->needCarInfoJdType);
            //驾照类型：1-公共驾驶执照，2-私人驾驶执照,3-B，4-B2，5-GDL，6-CDL
            if ($item['is_need_vehicle_info']) {
                $item['driving_license_type_enum'] = $this->drivingLicenseTypeEnumByJdType($jd_type);
            }
        }
        return $jdList;
    }

    /**
     * 驾照类型
     * @param $jdType
     * @return array
     */
    protected function drivingLicenseTypeEnumByJdType($jdType)
    {
        if ($jdType == '1') {
            return [
                [
                    'value' => HrEconomyAbilityModel::DRIVING_LICENSE_TYPE_B,
                    'label' => HrEconomyAbilityModel::$driving_license_type_list[HrEconomyAbilityModel::DRIVING_LICENSE_TYPE_B],
                ],
                [
                    'value' => HrEconomyAbilityModel::DRIVING_LICENSE_TYPE_B2,
                    'label' => HrEconomyAbilityModel::$driving_license_type_list[HrEconomyAbilityModel::DRIVING_LICENSE_TYPE_B2],
                ],
            ];
        } else {
            return [

                [
                    'value' => HrEconomyAbilityModel::DRIVING_LICENSE_TYPE_GDL,
                    'label' => HrEconomyAbilityModel::$driving_license_type_list[HrEconomyAbilityModel::DRIVING_LICENSE_TYPE_GDL],
                ],
                [
                    'value' => HrEconomyAbilityModel::DRIVING_LICENSE_TYPE_CDL,
                    'label' => HrEconomyAbilityModel::$driving_license_type_list[HrEconomyAbilityModel::DRIVING_LICENSE_TYPE_CDL],
                ],
            ];
        }
    }


    /**
     * 获取jd类型
     * @param $job_id
     * @return string
     * @throws BusinessException
     */
    protected function getJdType($job_id): string
    {
        if(empty($job_id)){
            return '0';
        }
        $apiClient = new ApiClient("winhr_rpc",'','get_jd_type', $this->lang);
        $res = $apiClient->setParams(['job_id' => $job_id])->execute();
        if ($res['code'] != 1) {
            throw new BusinessException($res['error']);
        }
        return strval($res['result']);
    }


    /**
     * 其他简历信息
     * @param $resumeModel
     * @param $paramIn
     * @return bool
     */
    protected function bindOtherResumeData($resumeModel, &$paramIn): bool
    {
        // '0=其他，1=本人半身照，2=身份证正面，3=身份证反面，4=户籍照第一页，5=户籍照应聘者本人信息页，6=兵役服役证明，7=成绩报告单，
//8=清白证明书，9=补充附件，10=驾驶证正面，11=驾驶证反面，12=车辆登记薄，13=车辆照片，14=车辆使用授权，
//15=签名，16=个人简历, 17=残疾证正面，18=残疾证反面, 19=最高学历证书，20=OR附件，21=承诺书，22=授权书 29=银行证明';

        if (!empty($paramIn['credentials_img'])) {
            $annexModel = HrAnnexModel::find([
                'conditions' => 'type = 1 and oss_bucket_key = :resume_id: and deleted = 0 and file_type = :file_type:',
                'bind'       => [
                    'resume_id' => $resumeModel->id,
                    'file_type' => HrAnnexModel::FILE_TYPE_IDENTITY_FRONT,
                ],
            ]);
            $image_path = str_replace(env('img_prefix'), '', $paramIn['credentials_img']);

            $paramIn['credentials_img'] = $image_path;
            if (!empty($annexModel)) {
                foreach ($annexModel as $item) {
                    $item->deleted = 1;
                    $item->save();
                }
            }
            $annexModel = new HrAnnexModel();
            $annexModel->create([
                'oss_bucket_key'  => $resumeModel->id,
                'oss_bucket_type' => 'HR_CV_ANNEX',
                'object_key'      => $image_path,
                'type'            => 1,
                'bucket_name'     => '',
                'original_name'   => '',
                'file_type'       => HrAnnexModel::FILE_TYPE_IDENTITY_FRONT,
            ]);
        }

        if (!empty($paramIn['half_length_photo'])) {
            $annexModel = HrAnnexModel::find([
                'conditions' => 'type = 1 and oss_bucket_key = :resume_id: and deleted = 0 and file_type = 1',
                'bind'       => [
                    'resume_id' => $resumeModel->id,
                ],
            ]);
            $image_path = str_replace(env('img_prefix'), '', $paramIn['half_length_photo']);

            $paramIn['half_length_photo'] = $image_path;
            if (!empty($annexModel)) {
                foreach ($annexModel as $item) {
                    $item->deleted = 1;
                    $item->save();
                }
            }
            $annexModel = new HrAnnexModel();
            $annexModel->create([
                'oss_bucket_key'  => $resumeModel->id,
                'oss_bucket_type' => 'HR_CV_ANNEX',
                'object_key'      => $image_path,
                'type'            => 1,
                'bucket_name'     => '',
                'original_name'   => '',
                'file_type'       => 1,
            ]);
        }
        //车辆照片
        if (!empty($paramIn['vehicle_photo'])) {
            $annexModel = HrAnnexModel::find([
                'conditions' => 'type = 1 and oss_bucket_key = :resume_id: and deleted = 0 and file_type = 13',
                'bind'       => [
                    'resume_id' => $resumeModel->id,
                ],
            ]);
            $image_path = str_replace(env('img_prefix'), '', $paramIn['vehicle_photo']);

            $paramIn['vehicle_photo'] = $image_path;
            if (!empty($annexModel)) {
                foreach ($annexModel as $item) {
                    $item->deleted = 1;
                    $item->save();
                }
            }
            $annexModel = new HrAnnexModel();
            $annexModel->create([
                'oss_bucket_key'  => $resumeModel->id,
                'oss_bucket_type' => 'HR_CV_ANNEX',
                'object_key'      => $image_path,
                'type'            => 1,
                'bucket_name'     => '',
                'original_name'   => '',
                'file_type'       => 13,
            ]);
        }

        //GDL驾照正面
        if (!empty($paramIn['gdl_driving_license_img'])) {
            $annexModel = HrAnnexModel::find([
                'conditions' => 'type = 1 and oss_bucket_key = :resume_id: and deleted = 0 and file_type = :file_type:',
                'bind'       => [
                    'resume_id' => $resumeModel->id,
                    'file_type' => HrAnnexModel::FILE_TYPE_GDL,
                ],
            ]);
            $image_path = str_replace(env('img_prefix'), '', $paramIn['gdl_driving_license_img']);

            $paramIn['gdl_driving_license_img'] = $image_path;
            if (!empty($annexModel)) {
                foreach ($annexModel as $item) {
                    $item->deleted = 1;
                    $item->save();
                }
            }
            $annexModel = new HrAnnexModel();
            $annexModel->create([
                'oss_bucket_key'  => $resumeModel->id,
                'oss_bucket_type' => 'HR_CV_ANNEX',
                'object_key'      => $image_path,
                'type'            => 1,
                'bucket_name'     => '',
                'original_name'   => '',
                'file_type'       => HrAnnexModel::FILE_TYPE_GDL,
            ]);
        }

        //银行证明
        if (!empty($paramIn['bank_statement'])) {
            $annexModel = HrAnnexModel::find([
                'conditions' => 'type = 1 and oss_bucket_key = :resume_id: and deleted = 0 and file_type = :file_type:',
                'bind'       => [
                    'resume_id' => $resumeModel->id,
                    'file_type' => HrAnnexModel::FILE_TYPE_BANK,
                ],
            ]);
            $image_path = str_replace(env('img_prefix'), '', $paramIn['bank_statement']);

            $paramIn['bank_statement'] = $image_path;
            if (!empty($annexModel)) {
                foreach ($annexModel as $item) {
                    $item->deleted = 1;
                    $item->save();
                }
            }
            $annexModel = new HrAnnexModel();
            $annexModel->create([
                'oss_bucket_key'  => $resumeModel->id,
                'oss_bucket_type' => 'HR_CV_ANNEX',
                'object_key'      => $image_path,
                'type'            => 1,
                'bucket_name'     => '',
                'original_name'   => '',
                'file_type'       => HrAnnexModel::FILE_TYPE_BANK,
            ]);
        }
        //紧急联系人
        if (!empty($paramIn['relationship_call_name']) ||
            !empty($paramIn['relationship_first_name']) ||
            !empty($paramIn['relationship_last_name']) ||
            !empty($paramIn['relationship_mobile']) ||
            !empty($paramIn['relationship'])) {
            $hrFamilyModel = HrFamilyModel::findFirst([
                'conditions' => 'resume_id = :resume_id:',
                'bind'       => [
                    'resume_id' => $resumeModel->id,
                ],
            ]);
            if (empty($hrFamilyModel)) {
                $hrFamilyModel            = new HrFamilyModel();
                $hrFamilyModel->resume_id = $resumeModel->id;
            }
            if (!empty($paramIn['relationship_call_name'])) {
                $hrFamilyModel->relationship_call_name = $paramIn['relationship_call_name'];
            }
            if (!empty($paramIn['relationship_first_name'])) {
                $hrFamilyModel->relationship_first_name = $paramIn['relationship_first_name'];
            }
            if (!empty($paramIn['relationship_last_name'])) {
                $hrFamilyModel->relationship_last_name = $paramIn['relationship_last_name'];
            }
            if (!empty($paramIn['relationship_mobile'])) {
                $hrFamilyModel->relationship_mobile           = $paramIn['relationship_mobile'];
                $hrFamilyModel->relationship_mobile_area_code = 60;
            }
            if (!empty($paramIn['relationship'])) {
                $hrFamilyModel->relationship = $paramIn['relationship'];
            }
            $hrFamilyModel->save();
        }

        //车辆信息
        if (!empty($paramIn['car_owner']) ||
            !empty($paramIn['driver_number']) ||
            !empty($paramIn['car_number']) ||
            !empty($paramIn['driving_license_type'])
        ) {
            $hrEconomyAbilityModel = HrEconomyAbilityModel::findFirst([
                'conditions' => 'resume_id = :resume_id:',
                'bind'       => [
                    'resume_id' => $resumeModel->id,
                ],
            ]);
            if (empty($hrEconomyAbilityModel)) {
                $hrEconomyAbilityModel            = new HrEconomyAbilityModel();
                $hrEconomyAbilityModel->resume_id = $resumeModel->id;
            }
            if (!empty($paramIn['car_owner'])) {
                $hrEconomyAbilityModel->car_owner = $paramIn['car_owner'];
            }
            if (!empty($paramIn['driver_number'])) {
                $hrEconomyAbilityModel->driver_number = $paramIn['driver_number'];
            }
            if (!empty($paramIn['car_number'])) {
                $hrEconomyAbilityModel->car_number = $paramIn['car_number'];
            }
            if (!empty($paramIn['driving_license_type'])) {
                $hrEconomyAbilityModel->driving_license_type = $paramIn['driving_license_type'];
            }
            $hrEconomyAbilityModel->save();
        }

        return true;
    }

    /**
     * 差异化字段绑定
     * @param $resumeModel
     * @param $paramIn
     * @param $is_create
     * @return mixed
     */
    protected function bindResumeData($resumeModel, $paramIn, $is_create)
    {
        $resumeModel->first_name              = $this->nameToUpper($paramIn['name']);
        $resumeModel->name                    = $this->nameToUpper(str_replace(' ', '', $paramIn['name']));
        $resumeModel->register_country        = $paramIn['register_country'];
        $resumeModel->email                   = $paramIn['email'];
        $resumeModel->credentials_num         = $paramIn['credentials_num'];
        $resumeModel->credentials_category    = 2;
        $resumeModel->register_government     = $paramIn['register_government'];
        $resumeModel->register_city           = $paramIn['register_city'];
        $resumeModel->register_postcodes      = $paramIn['register_postcodes'];
        $resumeModel->register_detail_address = $paramIn['register_detail_address'];
        if (!empty($paramIn['vehicle_type'])) {
            $resumeModel->car_type = $paramIn['vehicle_type'];
        }
        if ($resumeModel->fit == 1) {
            $resumeModel->residence_country        = $paramIn['register_country'];
            $resumeModel->residence_government     = $paramIn['register_government'];
            $resumeModel->residence_city           = $paramIn['register_city'];
            $resumeModel->residence_postcodes      = $paramIn['register_postcodes'];
            $resumeModel->residence_detail_address = $paramIn['register_detail_address'];
        }
        if ($is_create) {
            $resumeModel->resume_last_operator       = $paramIn['staff_id'];
            $resumeModel->resume_last_operation_time = gmdate('Y-m-d H:i:s');
            $resumeModel->recommender_staff_id       = $paramIn['staff_id'];
            $resumeModel->recommend_store_id         = $paramIn['worknode_id'];
        }
        return $resumeModel;
    }


    protected function getOtherValidationsFieldByJD($jd_id): array
    {
        $result = [];

        $jd_type = $this->getJdTypeFromCache($jd_id);
        if (in_array($jd_type, $this->needCarInfoJdType)) {
            $result["driver_license_front"] = "StrLenGeLe:1,255|>>>:driver_license_front error";
            $result["vehicle_photo"]        = "StrLenGeLe:1,255|>>>:vehicle_photo error";
        }
        $vehicleTypeList = $this->getVehicleTypeList();
        //车类型的岗位验证
        if (!empty($vehicleTypeList[$jd_id])) {
            $result["vehicle_type"] = "Required|Int|>>>:vehicle_type error";
        }

        return $result;
    }


    /**
     * 必填字段验证
     * @return string[]
     */

    protected function getDefaultValidationsField(): array
    {
        $t = $this->getTranslation();
        return [
            "type"                    => 'Required|IntIn:' . implode(',',
                    array_keys(self::$type_setting_key_map)) . '|>>>:type' . $t->_('reserve_type_error'),
            "name"                    => "StrLenGeLe:1,50|>>>:" . $t->_('qo_name') . $t->_('qo_error_msg_007'),
            "store_id"                => "StrLenGeLe:1,50|>>>:" . $t->_('qo_store') . $t->_('qo_error_msg_007'),
            "hc_id"                   => "Required|Int",
            // 手机号
            "phone"                   => "Required|StrLenGeLe:9,10|>>>:" . $t->_('mobile') . $t->_('qo_error_msg_007'),
            // 邮箱
            "email"                   => "Required|Email|>>>:" . $t->_('email') . $t->_('qo_error_msg_007'),
            "expected_arrivaltime"    => 'Required|Date',
            "job_id"                  => 'Required|Int',
            //NRIC Number
            "credentials_num"         => "StrLenGeLe:12,12|>>>:" . $t->_('qo_nric_number') . $t->_('qo_error_msg_007'),
            //户口国家
            "register_country"        => "Required|Int|>>>:" . $t->_('country') . $t->_('qo_error_msg_007'),
            //州
            "register_government"     => "StrLenGeLe:1,10|>>>:" . $t->_('qo_state') . $t->_('qo_error_msg_007'),
            //户口市
            "register_city"           => "StrLenGeLe:1,50|>>>:" . $t->_('qo_city') . $t->_('qo_error_msg_007'),
            //户口邮编
            "register_postcodes"      => "StrLenGeLe:5,5|>>>:" . $t->_('postcode') . $t->_('qo_error_msg_007'),
            //户口地详细地址
            "register_detail_address" => "StrLenGeLe:1,120|>>>:" . $t->_('detail_address') . $t->_('qo_error_msg_007'),
            //银行流水
            "bank_statement"          => "StrLenGeLe:1,500|>>>:" . $t->_('bank_statement') . $t->_('qo_error_msg_007'),
            //证件图
            "credentials_img"         => "StrLenGeLe:1,500|>>>:" . $t->_('credentials_img') . $t->_('qo_error_msg_007'),
        ];
    }

    /**
     *
     * @param $result
     * @return void
     * @throws BusinessException
     */
    protected function bindDetailData(&$result)
    {
        $vehicleTypeList                    = $this->getVehicleTypeList();
        $carOwnerList                       = array_column($this->getCarOwnerList(), 'label', 'value');
        $province_obj                       = SysProvinceModel::findFirstByCode($result['register_government'] ?? '');
        $cityList                           = array_column((new SysServer())->getTotalDictionaryRegionByDictCode('address_country_region'),
            'label', 'value');
        $result['register_country_text']    = $cityList[$result['register_country']] ?? '';
        $result['register_government_text'] = $province_obj->name ?? $result['register_government'];
        $result['half_length_photo']        = !empty($result['half_length_photo']) ? $this->getDI()->getConfig()->application['img_prefix'] . $result['half_length_photo'] : '';
        $result['bank_statement']           = !empty($result['bank_statement']) ? $this->getDI()->getConfig()->application['img_prefix'] . $result['bank_statement'] : '';
        $result['vehicle_photo']            = !empty($result['vehicle_photo']) ? $this->getDI()->getConfig()->application['img_prefix'] . $result['vehicle_photo'] : '';
        $result['vehicle_type_list']        = $vehicleTypeList[$result['job_id']] ?? [];
        $result['is_need_vehicle_type']     = !empty($result['vehicle_type_list']);
        $result['car_owner']                = !empty($result['car_owner']) ? intval($result['car_owner']) : null;
        $result['car_owner_text']           = $carOwnerList[$result['car_owner']] ?? '';
        $list                               = array_column($this->getRelationshipList(), 'label', 'value');
        $result['relationship']             = intval($result['relationship']);
        $result['relationship_text']        = $list[$result['relationship']] ?? '';
        $result['driving_license_type']     = !empty($result['driving_license_type']) ? intval($result['driving_license_type']):null;
        $result['driving_license_type_text']  = HrEconomyAbilityModel::$driving_license_type_list[$result['driving_license_type']] ?? '';
        //是否需要车辆信息
        $result['is_need_vehicle_info']     = in_array($this->getJdTypeFromCache($result['job_id']), $this->needCarInfoJdType);


    }

    /**
     * 获取quick offer的招聘负责人
     * @param $resumeModel
     * @param $paramIn
     * @param $resume_is_new
     * @return int
     */
    protected function getQuickOfferRecruiterId($resumeModel, $paramIn, $resume_is_new): int
    {
        return 0;
        if (!isCountry('MY')) {
            return 0;
        }

        $hc_id        = $paramIn['hc_id'] ?? '';
        $currentUser  = $paramIn['staff_id'] ?? '';
        $originalHcId = $paramIn['original_hc_id'] ?? '';
        if (empty($hc_id)) {
            return 0;
        }

        // HC没有变更不处理
        if ($hc_id == $originalHcId) {
            return 0;
        }

        // 不是新的
        if (!$resume_is_new) {
            // 检查简历状态条件：
            // 1. 简历状态=未沟通且沟通状态=无沟通记录或沟通失败
            // 2. 简历状态=筛选不通过/取消筛选/已拒绝/已取消
            $checkStatus = $this->checkResumeStatusForQuickOffer($resumeModel);
            if (!$checkStatus) {
                return 0;
            }
        }

        $hcInfo = HrHcModel::findFirstByHcId($hc_id);
        if (empty($hcInfo)) {
            return 0;
        }

        // 根据网点查找管辖信息
        $manageInfo = ApprovalFinderServer::getInstance()->getManageInfoByStoreId($hcInfo->worknode_id);
        if (empty($manageInfo)) {
            return 0;
        }
        $manageRegionId = $manageInfo->region_id;
        $managePieceId  = $manageInfo->piece_id;

        if (!empty($manageRegionId)) {
            $regionInfo = whrDataPermissionV2GroupManageRegionModel::findFirst([
                'conditions' => 'region_id = :region_id:',
                'bind' => [
                    'region_id' => $manageRegionId,
                ],
            ]);
        }
        if (!empty($managePieceId)) {
            $pieceInfo = whrDataPermissionV2GroupManagePieceModel::findFirst([
                'conditions' => 'piece_id = :piece_id:',
                'bind' => [
                    'piece_id' => $managePieceId,
                ],
            ]);
        }
        if (empty($pieceInfo) && empty($regionInfo)) {
            return 0;
        }

        // 获取有效的HC负责人
        $hcManagerInfo = $this->getValidHcManager($hc_id);
        if (empty($hcManagerInfo)) {
            return 0;
        }

        // 当前登录人是有效的HC负责人，则不再变更新的招聘负责人
        if (!empty($resumeModel->recruiter_id) && in_array($resumeModel->recruiter_id, $hcManagerInfo)) {
            return $resumeModel->recruiter_id;
        }

        // 操作人是关联后的HC的【HC负责人/HC协助负责人】，则赋值给操作人
        if (in_array($currentUser, $hcManagerInfo)) {
            return $currentUser;
        }

        //若当前简历【招聘负责人】已经是关联后的HC的【HC负责人/HC协助负责人】，不赋值
        if (!empty($resumeModel) && in_array($resumeModel->recruiter_id, $hcManagerInfo)) {
            return $resumeModel->recruiter_id;
        }

        // 随机分配
        // 从有效的HC负责人中随机选择一个
        $randomIndex = array_rand($hcManagerInfo);
        return $hcManagerInfo[$randomIndex];
    }

    /**
     * 获取有效的HC负责人
     * @param $hc_id
     * @return array
     */
    private function getValidHcManager($hc_id): array
    {
        if (empty($hc_id)) {
            return [];
        }

        $departmentIds = $this->getResumeRecruiterDepartmentData();
        if (empty($departmentIds)) {
            return [];
        }

        // 获取HC负责人/协助负责人
        $hcManagerList = HrHcRelateManagerModel::find([
            'conditions' => 'hc_id = :hc_id: and deleted = 0',
            'bind' => [
                'hc_id' => $hc_id,
            ],
            'columns' => ['staff_info_id'],
        ])->toArray();
        if (empty($hcManagerList)) {
            return [];
        }
        $hcManagerIds = array_column($hcManagerList, 'staff_info_id');

        // 员工信息
        $validHcManagerList = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id in ({staff_info_id:array}) and state = 1 and formal in (1,4) and is_sub_staff = 0 and node_department_id in ({department_ids:array})',
            'bind' => [
                'staff_info_id' => $hcManagerIds,
                'department_ids' => $departmentIds,
            ],
            'columns' => ['staff_info_id'],
        ])->toArray();
        if (empty($validHcManagerList)) {
            return [];
        }
        return array_column($validHcManagerList, 'staff_info_id');
    }

    /**
     * 获取配置的简历招聘负责人取值返回的所有部门ID
     * @return array
     */
    private function getResumeRecruiterDepartmentData(): array
    {
        $departIds = (new SettingEnvServer())->getSetVal('talent_acquisition_department_id', CommonEnums::SEPARATOR_DEFAULT);
        if (empty($departIds)) {
            return [];
        }
        $settingDeptIdArr = array_values(array_unique($departIds));

        //获取所有设置部门（有效的未删除部门）
        $settingDepartment = SysDepartmentModel::find([
            'conditions' => 'id in ({dept_id:array}) and deleted = 0 ',
            'bind'       => [
                'dept_id' => $settingDeptIdArr,
            ],
            'columns'    => ['id', 'ancestry_v3', 'name'],
        ])->toArray();

        if (empty($settingDepartment)) {
            return [];
        }

        //获取所有有效部门数据
        $allDepartmentList = SysDepartmentModel::find([
            'conditions' => 'deleted = 0',
            'columns'    => ['id', 'ancestry_v3', 'name'],
        ])->toArray();

        $allDepartmentIds = $settingDeptIdArr;//先把当前设置的部门ID记录寄来

        //循环所有部门数据中属于前设置部门的子部门的ID
        foreach ($allDepartmentList as $item) {
            foreach ($settingDepartment as $settingDept) {
                if (strpos($item['ancestry_v3'], $settingDept['ancestry_v3'].'/') !== false) {
                    $allDepartmentIds[] = $item['id'];
                }
            }
        }
        if (empty($allDepartmentIds)) {
            return [];
        }
        return $allDepartmentIds;
    }

    /**
     * 检查简历状态是否符合Quick Offer条件
     * @param $resumeModel
     * @return bool true表示应该返回0，false表示可以继续处理
     */
    private function checkResumeStatusForQuickOffer($resumeModel): bool
    {
        if (empty($resumeModel)) {
            return false;
        }

        $resumeId = $resumeModel->id;

        // 条件1：简历状态=未沟通且沟通状态=无沟通记录或沟通失败
        if ($resumeModel->state_code == HrResumeModel::STATE_CODE_NO_COMMUNICATION) {

            // 获取沟通记录
            $communicationLog = CommunicationResumeLogModel::findFirst([
                'conditions' => 'resume_id = :resume_id: AND newest = 1',
                'bind' => [
                    'resume_id' => $resumeId,
                ],
            ]);

            // 无沟通记录或沟通失败
            if (empty($communicationLog) || $communicationLog->status == CommunicationResumeLogModel::STATUS_FAIL) {
                return true;
            }
        }

        // 条件2：检查筛选状态=筛选不通过/取消筛选
        // 筛选不通过或取消筛选
        if (!empty($resumeModel->filter_state) && in_array($resumeModel->filter_state, [
            HrResumeFilterModel::FILTER_STATE_REJECT, // 筛选不通过
            HrResumeFilterModel::FILTER_STATE_CANCEL,  // 筛选撤回（取消）
        ])) {
            return true;
        }

        //条件3：检查面试状态=已拒绝/已取消
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('i.state');
        $builder->from(['h' => HrResumeModel::class]);
        $builder->join(HrInterviewModel::class, 'i.resume_id=h.id and i.hc_id=h.hc_id', 'i');
        $builder->where('h.id = :resume_id:', ['resume_id' => $resumeId]);
        $builder->orderBy('i.interview_id desc');
        $interviewModel = $builder->getQuery()->getSingleResult();

        if (!empty($interviewModel)) {

            $state = $this->getInterviewState($resumeModel->filter_state, $interviewModel->state, $resumeModel->is_out, $resumeModel->state_code);

            // 已拒绝或已取消
            if (in_array($state, [
                HrInterviewModel::INTERVIEW_REJECTED,  // 已拒绝
                HrInterviewModel::INTERVIEW_CANCELLED,  // 已取消
            ]) || $resumeModel->is_out == HrResumeModel::IS_OUT_YES) {
                return true;
            }
        } else if ($resumeModel->is_out == HrResumeModel::IS_OUT_YES) {
            return true;
        }

        return false;
    }

    /**
     * 获取面试状态
     * @param $resume_filter_state
     * @param $interview_state
     * @param $resume_is_out
     * @param $resume_state_code
     * @return mixed
     */
    public function getInterviewState($resume_filter_state, $interview_state, $resume_is_out, $resume_state_code)
    {
        if (!empty($interview_state)) {
            //已经进入面试环节后，则用面试表中状态
            $state = $interview_state;
        } elseif (!empty($resume_filter_state)) {
            //未进入面试前判断是否有筛选状态，如果有返回筛选状态，
            $state = $resume_filter_state;
        } elseif ($resume_state_code == enums::$resume_state_code['re_hc_wait_feedback']) {
            //重新关联hc未沟通
            $state = $resume_state_code;
        } else {
            //默认显示简历初始状态（未沟通）
            $state = HrInterviewModel::NO_COMMUNICATION;
        }

        if ($resume_is_out == 1) {
            $state = HrInterviewModel::INTERVIEW_REJECTED;//已拒绝
        }

        return $state;
    }


    /**
     * @param $paramIn
     * @return void
     * @throws ValidationException
     */
    public function validationField($paramIn)
    {
        $t = $this->getTranslation();
        if (!empty($paramIn['driver_number'])) {
            $validations['driver_number'] = "StrLenGeLe:1,12|>>>:" . $t->_('driver_number') . $t->_('qo_error_msg_007');
        }
        if (!empty($paramIn['car_number'])) {
            $validations['car_number'] = "StrLenGeLe:1,20|>>>:" . $t->_('car_number') . $t->_('qo_error_msg_007');
        }
        if (!empty($paramIn['relationship_call_name'])) {
            $validations['relationship_call_name'] = "IntIn:".implode(',',array_keys(HrfamilyModel::$relationship_call_name_list))."|>>>:" . $t->_('relationship_call_name') . $t->_('qo_error_msg_007');
        }
        if (!empty($paramIn['relationship_first_name'])) {
            $validations['relationship_first_name'] = "StrLenGeLe:1,25|>>>:" . $t->_('relationship_first_name') . $t->_('qo_error_msg_007');
        }
        if (!empty($paramIn['relationship_last_name'])) {
            $validations['relationship_last_name'] = "StrLenGeLe:1,25|>>>:" . $t->_('relationship_last_name') . $t->_('qo_error_msg_007');
        }
        if (!empty($paramIn['relationship_mobile'])) {
            $validations['relationship_mobile'] = "StrLenGeLe:9,10|>>>:" . $t->_('relationship_mobile') . $t->_('qo_error_msg_007');
        }
        if (!empty($paramIn['relationship'])) {
            $validations['relationship'] = "IntIn:".implode(',',array_keys(HrfamilyModel::$relationship_list))."|>>>:" . $t->_('relationship') . $t->_('qo_error_msg_007');
        }

        try {
           !empty($validations) && Validation::validate($paramIn, $validations);
        }catch (\Exception $e) {
            throw new ValidationException($e->getMessage());
        }
    }

}