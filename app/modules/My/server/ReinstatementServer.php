<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\InnerException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\ReinstatementRequestModel;
use FlashExpress\bi\App\Models\backyard\SuspensionManageLogModel;
use FlashExpress\bi\App\Repository\AuditlistRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\ReinstatementServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use FlashExpress\bi\App\Server\WorkflowServer;

class ReinstatementServer extends GlobalBaseServer
{
    /**
     * @param $params
     * @return bool
     */
    public function addRequest($params)
    {
        [$checkResult,$log] = $this->checkCanApply($params['staff_info_id']);

        //马来只能 今天和明天申请恢复
        if($params['expected_date'] < date('Y-m-d') || $params['expected_date'] > date('Y-m-d',strtotime('+1 day'))){
            throw new ValidationException($this->getTranslation()->_('reinstatement_date_notice'));
        }

        if ($checkResult){
            try {
                $this->getDI()->get('db')->begin();
                $staff_info_id = $params['staff_info_id'];
                $log_id = $log['id'];
                $this->updateReinstatementRequestOld($staff_info_id, $log_id);
                //保存申请数据
                $model = new ReinstatementRequestModel();
                $model->serial_no = 'RN' . $this->getRandomId();
                $model->staff_info_id = $staff_info_id;
                $model->origin_state = HrStaffInfoModel::STATE_3;//暂时
                $model->reason = $params['reason'];
                $model->reason_explanation = $params['reason_explanation'];
                $model->stop_duties_date = date('Y-m-d', strtotime($log['stop_duties_date']));
                $model->expected_date = $params['expected_date'];
                $model->attach = json_encode($params['attach']);
                $model->ref_id = $log_id;
                $model->state = enums::APPROVAL_STATUS_PENDING;
                $model->handled = ReinstatementRequestModel::NOT_HANDLED;
                $model->submitter_id = $params['staff_info_id'];
                //超时关闭任务用 马来是1天 其他国家是2天
                $extend['time_out'] = date('Y-m-d 00:00:00',strtotime('+2 day'));
                $model->time_out = $extend['time_out'];
                if (!$model->save()){
                    throw new InnerException("数据保存错误: 复职申请 ". json_encode($params));
                }
                //审批流
                $appServer = new ApprovalServer($this->lang, $this->timeZone);
                $app = $appServer->create($model->id, AuditListEnums::APPROVAL_TYPE_REINSTATEMENT, $params['staff_info_id'], null, $extend);
                if (!$app){
                    throw new InnerException("审批流创建错误：复职申请 ".json_encode($params));
                }
                $this->getDI()->get('db')->commit();
                return true;
            } catch (\Exception $e) {
                $this->getDI()->get('db')->rollback();
                $this->logger->write_log("复职申请提交失败：{$e->getMessage()}");
                return false;
            }
        }
        return false;
    }


    /**
     * @param $staffId
     * @param bool $checkDuplicate
     * @return array
     * @throws BusinessException
     */
    public function checkCanApply($staffId, $checkDuplicate= true)
    {

        $staffInfo = (new StaffRepository($this->lang))->getStaffInfoByStaffId($staffId,'hsi.staff_info_id,hsi.state');
        if (empty($staffInfo)){
            //return [false, $this->getTranslation()->t('员工信息不存在')];
            throw new BusinessException($this->getTranslation()->t('reinstatement_check_msg1'));//员工信息不存在
        }
        if ($staffInfo['state'] == HrStaffInfoModel::STATE_2){
            //return [false, $this->getTranslation()->t('您已离职，如需申请请线下联系主管')];
            throw new BusinessException($this->getTranslation()->t('reinstatement_check_msg2'));//您已离职，如需申请请线下联系主管
        }
        if ($staffInfo['state'] == HrStaffInfoModel::STATE_1){
            //return [false, $this->getTranslation()->t('您已在职，请返回登录页面登录')];
            throw new BusinessException($this->getTranslation()->t('reinstatement_check_msg3'));//您已在职，请返回登录页面登录
        }
        if ($staffInfo['state'] == HrStaffInfoModel::STATE_3){
            $log = $this->getLatestSuspensionLog($staffId);
            if (!$log){
                //return [false, $this->getTranslation()->t('没有停职记录')];
                throw new BusinessException($this->getTranslation()->t('reinstatement_check_msg4'));//没有停职记录
            }

            if (!in_array($log['stop_duty_reason'], [HrStaffInfoModel::STOP_DUTY_REASON_ABSENTEEISM, HrStaffInfoModel::STOP_DUTY_REASON_NOT_REFUNDED])){
                throw new BusinessException($this->getTranslation()->t('reinstatement_check_msg6'));//当前申请恢复在职功能只支持由于连续旷工和未回公款导致的停职，您的停职原因不符，请线下联系主管
            }
            //恢复在职检查ms
            $this->onJobCheekMs($staffId);
            return [true,$log];
        }
    }

    /**
     * @inheritDoc
     */
    public function getDetail(int $auditId, $user, $comeFrom)
    {
        $request = $this->getRequestById($auditId);
        $head = [
            'title'       => (new AuditlistRepository($this->lang, $this->timeZone))->getAudityType(AuditListEnums::APPROVAL_TYPE_REINSTATEMENT),
            'id'          => $request->id,
            'staff_id'    => $request->staff_info_id,
            'type'        => AuditListEnums::APPROVAL_TYPE_REINSTATEMENT,
            'created_at'  => DateHelper::utcToLocal($request->created_at),
            'updated_at'  => DateHelper::utcToLocal($request->updated_at),
            'status'      => $request->state,
            'serial_no'   => $request->serial_no ?? '',
        ];
        $staff_info = (new StaffServer())->getStaffInfoById($request->staff_info_id);
        $ss = new SysStoreServer();
        $storeRegionPieceId = $ss->getStoreRegionPiece($staff_info['sys_store_id']);
        if (empty($storeRegionPieceId['manage_region'])) {
            $storeRegionPiece['manage_region'] = '';
        } else {
            $storeRegionName = $ss->getStoreRegionName($storeRegionPieceId['manage_region']);
            $storeRegionPiece['manage_region'] = $storeRegionName['manage_region'];
        }
        if (empty($storeRegionPieceId['manage_piece'])) {
            $storeRegionPiece['manage_piece'] = '';
        } else {
            $storePieceName = $ss->getStorePieceName($storeRegionPieceId['manage_piece']);
            $storeRegionPiece['manage_piece'] = $storePieceName['manage_piece'];
        }
        $key = 'stop_duty_reason'.'_'.$request->reason;
        $detail = [
            'apply_parson'      => sprintf('%s ( %s )', $staff_info['staff_name'] ?? '', $staff_info['staff_info_id'] ?? ''),
            'apply_department'  => sprintf('%s - %s', $staff_info['department_name'] ?? '', $staff_info['job_name'] ?? ''),
            'staff_store'       => $staff_info['store_name'] ?? '',
            'region_piece_name' => $storeRegionPiece['manage_region'] . '-' . $storeRegionPiece['manage_piece'],
            'staff_job_title'   => $staff_info['job_name'] ?? '',
            'hire_type'         => $this->getTranslation()->_('hire_type_' . $staff_info['hire_type']), // 雇佣类型,
            'stop_duties_date' => $request->stop_duties_date,
            'stop_duty_reason' => $this->getSuspensionReason()[$request->reason] ?? '',
            $key               => $request->reason_explanation,
            'expected_return_date' => $request->expected_date ?? '',
            'attach'           => json_decode($request->attach, true),
        ];

        $returnData['data']['suspension_log_list'] = $this->getSuspensionLogList($request->staff_info_id); //停职记录
        $returnData['data']['head'] = $head;
        $returnData['data']['detail'] = $this->format($detail);

        return $returnData;
    }

    /**
     * @inheritDoc
     */
    public function genSummary(int $auditId, $user)
    {
        $request = $this->getRequestById($auditId);
        $staff = (new StaffServer())->getStaffInfoById($request->staff_info_id);
        $data = [
            [
                'key' => 'staff_store',
                'value' => $staff->store_name ?? '',
            ],
            [
                'key' => 'staff_job_title',
                'value' => $staff->job_name ?? '',
            ],
            [
                'key' => 'stop_duty_reason',
                'value' => $request->reason,
            ],
            [
                'key' => 'created_at',
                'value' => $request->created_at,
            ]
        ];
        return $data;
    }

    /**
     * @inheritDoc
     */
    public function setProperty(int $auditId, int $state, $extend = null, $isFinal = true)
    {
        if ($isFinal){
            $request = $this->getRequestById($auditId);
            if ($request){
                //更新审批状态
                $request->state = $state;
                $res = true;
                $staffServer = new StaffServer();
                $this->staffInfo =  $staffServer->getStaffInfoById($request->staff_info_id);
                //如果 非停职状态 不发消息 下面用
                if($this->staffInfo['state'] != enums::$service_status['suspension']){
                    $res = false;
                }
                if ($state == enums::APPROVAL_STATUS_APPROVAL){
                    $today = date('Y-m-d');
                    $request->effective_date = $request->expected_date;
                    //更新生效时间 https://flashexpress.feishu.cn/wiki/EwM4w5O1CiavztkQK0McVbKsnzh 审批通过立即生效 期望日期之前审批通过
                    if (strtotime($request->expected_date) <= strtotime($today)) {
                        $request->handled = ReinstatementRequestModel::HANDLED;
                        if ($res) {
                            $res = $this->reinstatement($request, $today);

                            if ($res !== false) {//停职状态 记录 实际恢复时间
                                //修改实际恢复时间
                                $request->effective_date = date('Y-m-d H:i:s');
                                [$result1, $result2] = $res;
                                $this->logger->write_log("恢复在职审批通过 立即生效 {$request->staff_info_id} {$result1},{$result2}",
                                    'info');
                            }
                        }
                    }
                }
                //驳回原因回写
                if($state != enums::APPROVAL_STATUS_APPROVAL){
                    $request->reject_reason = $extend['remark'] ?? '';
                }
                $request->save();
                //如果 已经是在职了 不发消息
                if($res === false){
                    return true;
                }
                //发送短信提醒
                $this->composeAndSendMsg($state,$request,$extend);
            }
        }
    }

    /**
     * @inheritDoc
     */
    public function getWorkflowParams($auditId, $user, $state = null)
    {
        $request = $this->getRequestById($auditId);

        return [
            'reason' => $request ? $request->reason:0,
        ];
    }

    /**
     * @param $requestId
     * @return mixed
     */
    public function getRequestById($requestId)
    {
        $request = ReinstatementRequestModel::findFirst($requestId);

        return $request;
    }

    /**
     * @param $staffId
     * @param $suspensionId
     * @return mixed
     */
    public function getRequestByStaffId($staffId,$suspensionId)
    {
        $request = ReinstatementRequestModel::findFirst(
            [
                'conditions' => "staff_info_id=:staff_id: and ref_id=:sus_id: and handled=0",
                'bind'=>['staff_id'=> $staffId,'sus_id'=> $suspensionId],
                'order' => "id desc"
            ]
        );
        return $request;
    }

    /**
     * 根据审批和处理状态获取申请
     * @param $auditStatus
     * @param array $params
     * @return ResultsetInterface
     */
    public function getRequests($auditStatus,$params=[])
    {
        $conditions = "state in ({status:array})";
        $bind = ['status'=> $auditStatus];
        if (isset($params['handled'])){
            $conditions .= " and handled=:handled:";
            $bind['handled'] = $params['handled'];
        }
        if (!empty($params['ref_id'])){
            $conditions .= " and ref_id=:ref_id:";
            $bind['ref_id'] = $params['ref_id'];
        }
        if (!empty($params['expected_date'])){
            $conditions .= " and expected_date=:expected_date:";
            $bind['expected_date'] = $params['expected_date'];
        }
        if (!empty($params['effective_date'])){
            $conditions .= " and effective_date=:effective_date:";
            $bind['effective_date'] = $params['effective_date'];
        }
        if (!empty($params['created_at'])){
            $conditions .= " and created_at < :created_at:";
            $bind['created_at'] = $params['created_at'];
        }

        $list = ReinstatementRequestModel::find(
            [
                'conditions'=> $conditions,
                'bind' => $bind,
                'order' => 'id desc'
            ]
        );

        return $list;
    }

    /**
     * @param $id
     * @return \Phalcon\Mvc\Model
     */
    public function getSuspensionLog($id)
    {
        $log = SuspensionManageLogModel::findFirst($id);
        return $log;
    }

    /**
     * @param $params
     * @param $staffId
     * @return array
     * @throws \Exception
     */
    public function audit($params,$staffId)
    {
        $auditType = AuditListEnums::APPROVAL_TYPE_REINSTATEMENT;
        $approval_id = $staffId;//操作人工号
        $audit_id = $params['audit_id'];
        $status = intval($params['status']);

        $app_server = new ApprovalServer($this->lang,$this->timeZone);

        if ($status == enums::$audit_status['approved']) {//审核通过
            $res = $app_server->approval($audit_id, $auditType, $approval_id);
        } elseif ($status == enums::$audit_status['dismissed']) {//驳回
            $res = $app_server->reject($audit_id, $auditType, $params['reject_reason'], $approval_id);
        } elseif ($status == enums::$audit_status['revoked']) {//撤销
            $res = $app_server->cancel($audit_id, $auditType, $params['reason'], $approval_id);
        }else{
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }
        if (empty($res)) {
            $this->logger->write_log('audit_update_status error ' . json_encode($params).json_encode($res),'info');
            return $this->checkReturn(-3, 'server error');
        }else{
            return $this->checkReturn(1);
        }
    }

    /**
     * @param $status
     * @param $request
     * @param $extend
     * @return void
     */
    private function composeAndSendMsg($status,$request, $extend)
    {
        $staffServer = new StaffServer();
        if(empty($this->staffInfo)){
            $this->staffInfo =  $staffServer->getStaffInfoById($request->staff_info_id);
        }
        if(empty($this->staffInfo)){
            return true;
        }

        switch ($status){
            case enums::APPROVAL_STATUS_APPROVAL:
                //审批通过处理；
                $effectiveDate = date('Y-m-d', strtotime($request->effective_date));
                //给员工发短信
                $msg_key = in_array($this->staffInfo['hire_type'],HrStaffInfoModel::$agentTypeTogether) ? 'reinstatement_approve_sms_my_un_paid' : 'reinstatement_approve_sms_my';
                $this->sendSms([$this->staffInfo], $msg_key, ['date' => $effectiveDate]);
                //给上级发消息
                $manager = $staffServer->getStaffMangerId($request->staff_info_id);
                if (!empty($manager)){
                    $manager = array_column($manager,'value');

                    $this->sendMessage($manager, 'reinstatement_approve_title', 'reinstatement_approve_manager_msg', [
                        'staff_name'    => $this->staffInfo['staff_name'],
                        'staff_info_id' => $this->staffInfo['staff_info_id']
                    ], [
                        'staff_name'    => $this->staffInfo['staff_name'],
                        'staff_info_id' => $this->staffInfo['staff_info_id'],
                        'dep_name'      => $this->staffInfo['department_name'],
                        'job_name'      => $this->staffInfo['job_name'],
                        'store_name'    => $this->staffInfo['store_name'],
                        'date'          => $effectiveDate,
                    ]);
                }

                break;
            case enums::APPROVAL_STATUS_REJECTED:
                //审批驳回处理
                //给员工发短信
                $msg_key = in_array($this->staffInfo['hire_type'],HrStaffInfoModel::$agentTypeTogether) ? 'reinstatement_reject_sms_my_un_paid' : 'reinstatement_reject_sms_my';

                $this->sendSms([$this->staffInfo],$msg_key,['reason'=> $extend['remark'] ?? '']);
                break;
            case enums::APPROVAL_STATUS_TIMEOUT:
                //审批超时处理
                //给员工发短信
                $msg_key = in_array($this->staffInfo['hire_type'],HrStaffInfoModel::$agentTypeTogether) ? 'reinstatement_timeout_sms_my_un_paid' : 'reinstatement_timeout_sms_my';

                $this->sendSms([$this->staffInfo],$msg_key,[]);
                break;
            default:
                break;
        }
    }

    /**
     * @return array|mixed
     */
    public function getSuspensionReason()
    {
        $client = new ApiClient('hcm_rpc','','getSuspensionReason',$this->lang);
        $client->setParams([]);
        $result = $client->execute();
        if ($result['result']['code'] == 1){
            $reasons =  $result['result']['data'];
            $reasons = array_column($reasons,'label','value');
            return $reasons;
        }
        return [];
    }


    /**
     * 发送消息 固定英文
     * @param $staffs
     * @param $titleKey
     * @param $contentKey
     * @param $titlePlaceholder
     * @param $contentPlaceholder
     * @return bool
     */
    public function sendMessage($staffs,$titleKey,$contentKey,$titlePlaceholder,$contentPlaceholder)
    {
        $staffServer = new StaffServer();
        $t = $this->getTranslation('en');
        foreach ($staffs as $v){
            $staffInfo = $staffServer->getStaffInfoById($v);
            if ($staffInfo['state'] != enums::$service_status['incumbency']){
                continue;
            }
            $bi_rpc = (new ApiClient('hcm_rpc', '', 'add_kit_message', 'en'));
            $msg_data = [
                "staff_users"       => [['id'=>$v]],
                "staff_info_ids_str"=> $v,
                "message_title"     => $t->t($titleKey,$titlePlaceholder), //标题
                "message_content"   => $t->t($contentKey,$contentPlaceholder),//内容
                "category"          => MessageEnums::CATEGORY_GENERAL,
            ];
            $bi_rpc->setParams($msg_data);
            $return = $bi_rpc->execute();//响应
            if (isset($return['result'])) {
                $this->logger->write_log("消息发送成功：". json_encode(['data'=>$msg_data,'result'=>$return]),'info');
            } else {
                $this->logger->write_log("消息发送失败：". json_encode(['data'=>$msg_data,'result'=>$return]),'notice');
            }
        }
    }


    /**
     * 更新is_new
     * @param $staff_info_id
     * @return mixed
     */
    public function updateReinstatementRequestOld($staff_info_id, $ref_id) {
        return $this->getDI()->get('db')->updateAsDict(
            'reinstatement_request',
            ['is_new' => ReinstatementRequestModel::IS_NEW_NO],
            [
                "conditions" => 'staff_info_id = ? and ref_id = ?',
                'bind'       => [$staff_info_id, $ref_id],
            ]
        );
    }
}