<?php

namespace FlashExpress\bi\App\Modules\My\Server;


use FlashExpress\bi\App\Models\backyard\HrStaffAnnexInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Modules\My\library\Enums\CommonEnums;
use FlashExpress\bi\App\Server\SalaryServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffFaceBlacklistServer as BaseStaffFaceBlacklistServer;
use FlashExpress\bi\App\Server\SysDepartmentServer;
use FlashExpress\bi\App\Server\WorkflowServer;

class StaffFaceBlacklistServer extends BaseStaffFaceBlacklistServer
{

    /**
     * 设置员工信息附件表人脸黑名单状态
     * @return bool
     */
    protected function setStaffAnnexInfoCheckState(): bool
    {
        if (!in_array($this->staffInfo['formal'], [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN])) {
            return true;
        }

        $staffAnnexInfo = HrStaffAnnexInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and type = :type:',
            'bind'       => [
                'staff_info_id' => $this->staffInfo['staff_info_id'],
                'type'          => HrStaffAnnexInfoModel::TYPE_ID_CARD,
            ],
        ]);
        if (empty($staffAnnexInfo)) {
            $this->logger->write_log(['setStaffAnnexInfoCheckState' => $this->staffInfo, 'error' => '未找到附件表信息'],
                'error');
            return false;
        }
        if (!empty($staffAnnexInfo->face_check_state)) {
            return true;
        }
        $staffAnnexInfo->face_check_state = $this->isMatch() ? HrStaffAnnexInfoModel::FACE_CHECK_STATE_HIT : HrStaffAnnexInfoModel::FACE_CHECK_STATE_NOT_HIT;;
        $staffAnnexInfo->updated_at = new \Phalcon\Db\RawValue('updated_at');
        return $staffAnnexInfo->save();
    }

    /**
     * 职位属于HCM-设置中心-系统设置-WinHR-first_line_jobs配置职位，或雇佣类型=个人代理/兼职个人代理
     * @return bool
     */
    protected function checkIsSendContract(): bool
    {
        $service = new SalaryServer($this->lang, $this->timeZone);

        return $service->isFirstLineJob($this->staffInfo['node_department_id'],
                $this->staffInfo['job_title']) || in_array($this->staffInfo['hire_type'],
                [HrStaffInfoModel::HIRE_TYPE_UN_PAID,HrStaffInfoModel::HIRE_TYPE_PART_TIME_AGENT]);
    }



    protected function sendMessageToManager($msg_to_other_staff): bool
    {
        $staffInfo = $this->staffInfo;

        $dept_config = (new SettingEnvServer())->getMultiEnvByCode([
            'dept_hub_management_id',
            'dept_network_management_id',
        ]);
        [$dept_network_management_id, $dept_hub_management_id] = [
            $dept_config['dept_network_management_id'],
            $dept_config['dept_hub_management_id'],
        ];

        $networkDepartmentInfo     = (new SysDepartmentServer())->getDepartmentDetail($staffInfo['node_department_id'],
            ['ancestry_v3']);
        $staffDepartmentDeptList = explode('/', $networkDepartmentInfo['ancestry_v3']);

        if (in_array($dept_network_management_id, $staffDepartmentDeptList)) {
            $staffs = HrStaffInfoModel::find([
                'conditions' => 'node_department_id = :node_department_id: and state = :state: and is_sub_staff = 0 ',
                'bind'       => [
                    'node_department_id' => CommonEnums::NETWORK_OPERATIONS_RECRUITING,
                    'state'              => HrStaffInfoModel::STATE_1,
                ],
                'columns'    => 'staff_info_id',
            ])->toArray();

            $msg_to_other_staff_query = array_column($staffs, 'staff_info_id');
        } elseif (in_array($dept_hub_management_id, $staffDepartmentDeptList)) {
            $HR_BP              = (new WorkflowServer($this->lang,
                $this->timeZone))->findHRBP($staffInfo['node_department_id'],
                ['store_id' => $staffInfo['sys_store_id']]);
            $msg_to_other_staff_query = explode(',', $HR_BP);
        } else {
            $msg_to_other_staff_query = [];
        }
        $msg_to_other_staff = array_values(array_filter(array_unique(array_merge($msg_to_other_staff,$msg_to_other_staff_query))));
        $this->logger->write_log(['msg_to_other_staff'=>$msg_to_other_staff],'info');
        $this->sendMessage($msg_to_other_staff);
        return true;
    }

}