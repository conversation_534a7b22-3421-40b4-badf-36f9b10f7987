<?php

namespace FlashExpress\bi\App\Modules\My\Server;


use FlashExpress\bi\App\Enums\AttendanceCalendarEnums;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrShiftV2ExtendModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftV2Model;
use FlashExpress\bi\App\Models\backyard\HrStaffTransferModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditReissueForBusinessModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Modules\My\library\Enums\OvertimeEnums;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\BusinesstripRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\AttendanceBusinessServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

class AttendanceCalendarV2Server extends AttendanceCalendarServer
{


    //当日日期类型
    //假日>休息日>请假>出差
    // BT:出差、LEAVE:请假、REST:rest day、OFF:off day 、PH: ph、NORMAL:正常（不展示）
    const DATE_TYPE_NORMAL      = 0;
    const DATE_TYPE_PH          = 1;
    const DATE_TYPE_REST        = 2;
    const DATE_TYPE_OFF         = 3;
    const DATE_TYPE_LEAVE_ALL   = 40;
    const DATE_TYPE_LEAVE_UP    = 41;
    const DATE_TYPE_LEAVE_DOWN  = 42;
    const DATE_TYPE_BT          = 5;
    //考勤数据
    const ONCE_SHIFT_IN             = 11;
    const ONCE_SHIFT_OUT            = 12;
    const TWICE_SHIFT_FIRST_IN      = 21;
    const TWICE_SHIFT_FIRST_OUT     = 22;
    const TWICE_SHIFT_SECOND_IN     = 23;
    const TWICE_SHIFT_SECOND_OUT    = 24;

    //考勤 上下班
    const ATTENDANCE_TYPE_IN  = 1;//上班
    const ATTENDANCE_TYPE_OUT = 2;//下班

    //补卡的考勤数据枚举
    const MAKE_UP_CARD_TYPE_1 = 1;
    const MAKE_UP_CARD_TYPE_2 = 2;
    const MAKE_UP_CARD_TYPE_3 = 3;
    const MAKE_UP_CARD_TYPE_4 = 4;

    protected static $dateTypeMap = [
        self::DATE_TYPE_PH         => 'PH',
        self::DATE_TYPE_REST       => 'REST',
        self::DATE_TYPE_OFF        => 'OFF',
        self::DATE_TYPE_LEAVE_ALL  => 'LEAVE',
        self::DATE_TYPE_LEAVE_UP   => 'LEAVE',
        self::DATE_TYPE_LEAVE_DOWN => 'LEAVE',
        self::DATE_TYPE_BT         => 'BT',
        self::DATE_TYPE_NORMAL     => 'NORMAL',
    ];
    //0 正常、1 出差-待审批、2 迟到、3 早退、4缺卡
    const STATE_NORMAL          = 0;
    const STATE_BT_WAIT_AUDIT   = 1;
    const STATE_LATE_IN         = 2;
    const STATE_EARLY_OUT       = 3;
    const STATE_NO_RECORD       = 4;

    //申请补卡状态 0不展示 1 申请补卡 、2申请补卡审批中、3 已补卡
    const MAKE_UP_STATE_NORMAL              =  0;
    const MAKE_UP_STATE_ALLOW_APPLY         =  1;//申请
    const MAKE_UP_STATE_WAIT_AUDIT          =  2;
    const MAKE_UP_STATE_FIX_ATTENDANCE_DONE =  3;
    /**
     * 不需要打卡的类型
     * @var int[]
     */
    protected static $notNeedAttendanceMap=[
        self::DATE_TYPE_PH,self::DATE_TYPE_REST,self::DATE_TYPE_OFF,self::DATE_TYPE_LEAVE_ALL
    ];

    /**
     * 剩余补卡次数
     * @var int
     */
    protected $remainAttendanceCarNum = 0;

    //对应固化表数据
    protected $transferData = [];
    //考勤server
    protected $attendanceServer = null;
    //主播职位id
    public $liveJobId = [];

    //是否是主播
    protected $isLive;
    //是否居家办公日期列表
    protected $workHomeDates = [];
    protected $today;

    /**
     * 考勤明细配置
     * @var \array[][]
     */
    protected $staffAttendanceListConfig = [
        HrShiftV2ExtendModel::SHIFT_TYPE_ONCE  => [
            [
                'attendance_order'  => self::ONCE_SHIFT_IN,
                'date_type'         => self::DATE_TYPE_NORMAL,
                'shift_type'        => HrShiftV2ExtendModel::SHIFT_TYPE_ONCE,
                'attendance_type'   => self::ATTENDANCE_TYPE_IN,
                'show_shift'        => '',
                'state'             => self::STATE_NORMAL,
                'show_info'         => 'no_attendance',
                'make_up_state'     => self::MAKE_UP_STATE_NORMAL,
                'check_data_config' => [
                    'attendance_data_index'                              => 0,
                    'attendance_time_field'                              => 'started_at',
                    'check_allow_attendance_time_field'                  => 'first_allow_attendance_time',
                    'check_no_record_time_field'                         => 'first_check_no_record_time',
                    'check_late_in_early_out_time_field'                 => 'first_check_late_in_time',
                    'check_late_in_early_out_time_when_up_leave_field'   => 'up_leave_check_late_in_time',
                    'check_late_in_early_out_time_when_down_leave_field' => 'down_leave_check_late_in_time',
                    'make_up_card_index'                                 => self::MAKE_UP_CARD_TYPE_1,
                    'check_attendance_state'                             => 'start_state',
                    'no_need_attendance_leave_type'                      => self::DATE_TYPE_LEAVE_ALL,
                ],
            ],
            [
                'attendance_order'  => self::ONCE_SHIFT_OUT,
                'date_type'         => self::DATE_TYPE_NORMAL,
                'shift_type'        => HrShiftV2ExtendModel::SHIFT_TYPE_ONCE,
                'attendance_type'   => self::ATTENDANCE_TYPE_OUT,
                'show_shift'        => '',
                'state'             => self::STATE_NORMAL,
                'show_info'         => 'no_attendance',
                'make_up_state'     => self::MAKE_UP_STATE_NORMAL,
                'check_data_config' => [
                    'attendance_data_index'                              => 0,
                    'attendance_time_field'                              => 'end_at',
                    'check_allow_attendance_time_field'                  => 'first_allow_attendance_time',
                    'check_no_record_time_field'                         => 'first_check_no_record_time',
                    'check_late_in_early_out_time_field'                 => 'first_check_early_out_time',
                    'check_late_in_early_out_time_when_up_leave_field'   => 'up_leave_check_early_out_time',
                    'check_late_in_early_out_time_when_down_leave_field' => 'down_leave_check_early_out_time',
                    'make_up_card_index'                                 => self::MAKE_UP_CARD_TYPE_2,
                    'check_attendance_state'                             => 'end_state',
                    'no_need_attendance_leave_type'                      => self::DATE_TYPE_LEAVE_ALL,
                ],
            ],
        ],
        HrShiftV2ExtendModel::SHIFT_TYPE_TWICE => [
            [
                'attendance_order'  => self::TWICE_SHIFT_FIRST_IN,
                'date_type'         => self::DATE_TYPE_NORMAL,
                'shift_type'        => HrShiftV2ExtendModel::SHIFT_TYPE_TWICE,
                'attendance_type'   => self::ATTENDANCE_TYPE_IN,
                'show_shift'        => '',
                'state'             => self::STATE_NORMAL,
                'show_info'         => 'no_attendance',
                'make_up_state'     => self::MAKE_UP_STATE_NORMAL,
                'check_data_config' => [
                    'attendance_data_index'                              => 1,
                    'attendance_time_field'                              => 'started_at',
                    'check_allow_attendance_time_field'                  => 'first_allow_attendance_time',
                    'check_no_record_time_field'                         => 'first_check_no_record_time',
                    'check_late_in_early_out_time_field'                 => 'first_check_late_in_time',
                    'check_late_in_early_out_time_when_up_leave_field'   => 'up_leave_check_late_in_time',
                    'check_late_in_early_out_time_when_down_leave_field' => 'down_leave_check_late_in_time',
                    'make_up_card_index'                                 => self::MAKE_UP_CARD_TYPE_1,
                    'check_attendance_state'                             => 'start_state',
                    'no_need_attendance_leave_type'                      => self::DATE_TYPE_LEAVE_UP,
                ],
            ],
            [
                'attendance_order'  => self::TWICE_SHIFT_FIRST_OUT,
                'date_type'         => self::DATE_TYPE_NORMAL,
                'shift_type'        => HrShiftV2ExtendModel::SHIFT_TYPE_TWICE,
                'attendance_type'   => self::ATTENDANCE_TYPE_OUT,
                'show_shift'        => '',
                'state'             => self::STATE_NORMAL,
                'show_info'         => 'no_attendance',
                'make_up_state'     => self::MAKE_UP_STATE_NORMAL,
                'check_data_config' => [
                    'attendance_data_index'                              => 1,
                    'attendance_time_field'                              => 'end_at',
                    'check_allow_attendance_time_field'                  => 'first_allow_attendance_time',
                    'check_no_record_time_field'                         => 'first_check_no_record_time',
                    'check_late_in_early_out_time_field'                 => 'first_check_early_out_time',
                    'check_late_in_early_out_time_when_up_leave_field'   => 'up_leave_check_early_out_time',
                    'check_late_in_early_out_time_when_down_leave_field' => 'down_leave_check_early_out_time',
                    'make_up_card_index'                                 => self::MAKE_UP_CARD_TYPE_2,
                    'check_attendance_state'                             => 'end_state',
                    'no_need_attendance_leave_type'                      => self::DATE_TYPE_LEAVE_UP,
                ],
            ],
            [
                'attendance_order'  => self::TWICE_SHIFT_SECOND_IN,
                'date_type'         => self::DATE_TYPE_NORMAL,
                'shift_type'        => HrShiftV2ExtendModel::SHIFT_TYPE_TWICE,
                'attendance_type'   => self::ATTENDANCE_TYPE_IN,
                'show_shift'        => '',
                'state'             => self::STATE_NORMAL,
                'show_info'         => 'no_attendance',
                'make_up_state'     => self::MAKE_UP_STATE_NORMAL,
                'check_data_config' => [
                    'attendance_data_index'                              => 2,
                    'attendance_time_field'                              => 'started_at',
                    'check_allow_attendance_time_field'                  => 'second_allow_attendance_time',
                    'check_no_record_time_field'                         => 'second_check_no_record_time',
                    'check_late_in_early_out_time_field'                 => 'second_check_late_in_time',
                    'check_late_in_early_out_time_when_up_leave_field'   => 'up_leave_check_late_in_time',
                    'check_late_in_early_out_time_when_down_leave_field' => 'down_leave_check_late_in_time',
                    'make_up_card_index'                                 => self::MAKE_UP_CARD_TYPE_3,
                    'check_attendance_state'                             => 'start_state',
                    'no_need_attendance_leave_type'                      => self::DATE_TYPE_LEAVE_DOWN,
                ],
            ],
            [
                'attendance_order'  => self::TWICE_SHIFT_SECOND_OUT,
                'date_type'         => self::DATE_TYPE_NORMAL,
                'shift_type'        => HrShiftV2ExtendModel::SHIFT_TYPE_TWICE,
                'attendance_type'   => self::ATTENDANCE_TYPE_OUT,
                'show_shift'        => '',
                'state'             => self::STATE_NORMAL,
                'show_info'         => 'no_attendance',
                'make_up_state'     => self::MAKE_UP_STATE_NORMAL,
                'check_data_config' => [
                    'attendance_data_index'                              => 2,
                    'attendance_time_field'                              => 'end_at',
                    'check_allow_attendance_time_field'                  => 'second_allow_attendance_time',
                    'check_no_record_time_field'                         => 'second_check_no_record_time',
                    'check_late_in_early_out_time_field'                 => 'second_check_early_out_time',
                    'check_late_in_early_out_time_when_up_leave_field'   => 'up_leave_check_early_out_time',
                    'check_late_in_early_out_time_when_down_leave_field' => 'down_leave_check_early_out_time',
                    'make_up_card_index'                                 => self::MAKE_UP_CARD_TYPE_4,
                    'check_attendance_state'                             => 'end_state',
                    'no_need_attendance_leave_type'                      => self::DATE_TYPE_LEAVE_DOWN,
                ],
            ],

        ],
    ];




    /**
     * 出差打卡信息
     * @param $staff_id
     * @param $start_date
     * @param $end_date
     * @return AttendanceCalendarV2Server
     */

    public function initData($staff_id, $start_date, $end_date): AttendanceCalendarV2Server
    {
        $this->staffId   = $staff_id;
        //员工信息
        $this->staffInfo = (new StaffRepository())->getStaffPosition($staff_id);
        //主账号信息
        $this->masterInfo = $this->getMasterStaffInfo();
        //直播职位
        $jobId = (new SettingEnvServer())->getSetVal('free_shift_position');
        $this->liveJobId = empty($jobId) ? [] : explode(',', $jobId);
        $attendanceServer = new AttendanceServer($this->lang, $this->timeZone);
        //每日的班次信息
        if($this->staffInfo['formal'] == HrStaffInfoModel::FORMAL_0){
            $this->shiftInfo = $attendanceServer->getOldStaffShift($staff_id, $start_date, $end_date);
        }else{
            $this->shiftInfo = $this->getDailyShiftInfo($staff_id, $start_date, $end_date);
        }

        //打卡数据
        $this->staffAData = $this->getAttendanceMess($staff_id, $start_date, $end_date);
        //已审批出差 用于展示出差标签
        $this->tripDate = (new BusinesstripRepository($this->lang, $this->timeZone))->trip_dates($staff_id, $start_date, $end_date);
        //待审批、已审批的出差打卡数据 用来展示审批状态
        $this->staffAuditReissueForBusiness = $this->staff_audit_reissue_for_business($staff_id, $start_date, $end_date,[1,2]);
        //申请中的补卡数据
        $this->makeUpCard = $this->getMakeUpCardData($staff_id, $start_date, $end_date);
        //公共假期  ph
        $this->holidays = $this->getHolidayData($start_date, $end_date);
        //休息日  off day 、 rest day
        $this->staffOffDate = $this->dealWithOffDay($start_date, $end_date);
        //审批通过的请假数据
        $this->dateLeave = $this->getLeaveDate($this->masterInfo['staff_info_id'], $start_date, $end_date);
        //当月日期
        $this->monthDay = DateHelper::DateRange(strtotime($start_date), strtotime($end_date));
        //是否是居家办公日期
        $this->workHomeDates = $attendanceServer->getWorkHomeDates($this->monthDay, $this->staffInfo);
        //支援信息
        $this->supportData = $this->getSupportData($start_date, $end_date);
        $this->dealSupportData();//把支援信息 放到每一天
        //考勤server
        $this->attendanceServer = new AttendanceServer($this->lang,$this->timeZone);
        $this->attendanceServer->initFreeShiftConfig();
        $this->today = date('Y-m-d');
        return $this;
    }


    public function handleDailyData(): array
    {
        $list = [];
        foreach ($this->monthDay as $day) {
            $result    = [
                'shift_info'      => '',
                'rest_info'       => '',
                'attendance_info' => [],
                'support_info'    => null,
            ];
            //支援信息
            $result['support_info'] = $this->supportData[$day] ?? null;
            $shiftInfo = $this->shiftInfo[$day] ?? [];
            //判断 是否是主播
            if ($day < $this->today) {
                $current_job_title = $this->transferData[$day]['job_title'];
            } else {
                $current_job_title = $this->staffInfo['job_title'];
            }
            $liveFlag = in_array($current_job_title, $this->liveJobId);
            //非主播 没班次的人 原逻辑
            if (empty($shiftInfo) && !$liveFlag) {
                $list[$day] = $result;
                continue;
            }
            $this->isLive = $liveFlag;
            $leaveInfo = $this->dateLeave[$day] ?? [];
            $shiftType = $shiftInfo['shift_type'] ?? 0;
            if ($liveFlag) {
                //上班打卡时间 注意出差打卡
                $startTime = $this->staffAData[$day][0]['started_at'] ?? null;
                if (empty($startTime) && !empty($this->staffAuditReissueForBusiness[$day])) {
                    $startTime = $this->staffAuditReissueForBusiness[$day][0]['started_at'] ?? null;
                }
                [$result['shift_info'],$leaveTips] = $this->attendanceServer->makeLeaveTipsShiftInfoV2($this->staffId,
                    $current_job_title, $this->staffInfo['hire_type'], $day,
                    $startTime,
                    [],
                    $leaveInfo['type'] ?? -1);
                $shiftType            = HrShiftV2ExtendModel::SHIFT_TYPE_ONCE;//不考虑多班次
            } else {
                [$result['shift_info'],$leaveTips] = $this->attendanceServer->makeLeaveTipsShiftInfoV2($this->staffId,
                    $current_job_title, $this->staffInfo['hire_type'], $day,
                    null,
                    $shiftInfo,
                    $leaveInfo['type'] ?? -1);
                $result['rest_info']  = $shiftInfo['middle_rest_start'] != $shiftInfo['middle_rest_end'] ? $shiftInfo['middle_rest_start'].'-'.$shiftInfo['middle_rest_end'] : '';
            }

            if($day > date('Y-m-d',strtotime('+1 day'))){
                $list[$day] = $result;
                continue;
            }
            $result['attendance_info'] = $this->staffAttendanceListConfig[$shiftType]??[];
            foreach ($result['attendance_info'] as &$item) {
                $this->getStateAndShowInfoAndMarkUpState($day, $item);
                $item['date_type']       = self::$dateTypeMap[$this->getDateType($day, $item['attendance_order'])];
                $item['show_shift']      = $this->getShowShit($day, $item['attendance_order']);
                $item['shift_type']      = $this->getShiftType($day);
                unset($item['check_data_config']);
            }
            $list[$day] = $result;
        }
        return $list;
    }

    /**
     * 处理 几个状态、展示打卡信息
     * @param $day
     * @param $item
     * @return void
     */
    protected function getStateAndShowInfoAndMarkUpState($day,&$item){
        $shiftInfo      = $this->shiftInfo[$day] ?? [];
        $btAttendance   = $this->staffAuditReissueForBusiness[$day] ?? [];
        $attendance     = $this->staffAData[$day] ?? [];
        $dateType       = $this->getDateType($day, $item['attendance_order']);
        $currentTime    = date("Y-m-d H:i:s");
        $makeUpCardData = $this->makeUpCard[$day] ?? [];
        $leaveInfo      = $this->dateLeave[$day]??[];
        $config         = $item['check_data_config'];
        $showKey = $item['show_info'];
        $suffix = '';
        if(in_array($this->staffInfo['hire_type'],HrStaffInfoModel::$agentTypeTogether)){
            $showKey = $showKey . '_unpaid';
            $suffix = '_unpaid';
        }
        //主播 清空班次信息
        if($this->isLive){
            $shiftInfo = [];
        }

        $item['show_info'] = $this->getTranslation()->_($showKey);
        if ((empty($attendance[$config['attendance_data_index']]) || empty($attendance[$config['attendance_data_index']][$config['attendance_time_field']]))) {
            //已过可打卡时间且未打卡，显示无打卡时间字样
            if (!empty($shiftInfo[$config['check_allow_attendance_time_field']]) && $currentTime > $shiftInfo[$config['check_allow_attendance_time_field']]) {
                //"打卡时间:11-12 10:00:00",//  or "not_need_attendance 无需打卡" or "dont_have_attendance_time 无打卡时间" or "no_attendance 尚未打卡"
                $item['show_info'] = $this->getTranslation()->_('dont_have_attendance_time' . $suffix);
            }
            //缺卡
            if (!empty($shiftInfo[$config['check_no_record_time_field']]) && $currentTime > $shiftInfo[$config['check_no_record_time_field']]) {
                $item['state'] = self::STATE_NO_RECORD;
            }
        }
        //迟到 早退
        if (!empty($attendance[$config['attendance_data_index']]) && !empty($attendance[$config['attendance_data_index']][$config['attendance_time_field']])) {
            //打卡时间
            $item['show_info'] = $this->getTranslation()->_('attendance_time' . $suffix).' '.date('m-d H:i', strtotime($attendance[$config['attendance_data_index']][$config['attendance_time_field']]));

            //上班 看迟到  下班看早退
            if ($item['attendance_type'] == self::ATTENDANCE_TYPE_IN) {
                $check_late_in_time = $shiftInfo[$config['check_late_in_early_out_time_field']] ?? '';
                if(!empty($leaveInfo) && $leaveInfo['type'] ==  AttendanceCalendarEnums::LEAVE_UP_TYPE){
                    $check_late_in_time = $shiftInfo[$config['check_late_in_early_out_time_when_up_leave_field']] ?? '';
                }
                //迟到
                if (!empty($check_late_in_time) && strtotime($attendance[$config['attendance_data_index']][$config['attendance_time_field']]) >= strtotime($check_late_in_time)) {
                    $item['state'] = self::STATE_LATE_IN;
                }
            } else {
                $check_early_out_time_time = $shiftInfo[$config['check_late_in_early_out_time_field']] ?? '';
                if(!empty($leaveInfo) && $leaveInfo['type'] ==  AttendanceCalendarEnums::LEAVE_DOWN_TYPE){
                    $check_early_out_time_time = $shiftInfo[$config['check_late_in_early_out_time_when_down_leave_field']] ?? '';
                }
                //早退
                if (!empty($check_early_out_time_time) && strtotime($attendance[$config['attendance_data_index']][$config['attendance_time_field']]) <= strtotime($check_early_out_time_time)) {
                    $item['state'] = self::STATE_EARLY_OUT;
                }
            }
        }

        //主播职位 显示缺卡
        if($this->isLive && empty($attendance[$config['attendance_data_index']][$config['attendance_time_field']])){
            if($day < date('Y-m-d') && $config['attendance_time_field'] == 'started_at'){
                $item['state'] = self::STATE_NO_RECORD;
            }
            if($day <= date('Y-m-d') && $config['attendance_time_field'] == 'end_at'){
                $start = $attendance[$config['attendance_data_index']]['started_at'] ?? '';
                if(empty($start)){
                    $item['state'] = $day == date('Y-m-d') ? self::STATE_NORMAL : self::STATE_NO_RECORD;
                }else{
                    $item['state'] = $start > date('Y-m-d H:i:s',strtotime('-22 hour')) ? self::STATE_NORMAL : self::STATE_NO_RECORD;
                }
            }
        }

        //主播职位 显示早退
        if($this->isLive && !empty($attendance[$config['attendance_data_index']][$config['attendance_time_field']]) && $config['attendance_time_field'] == 'end_at' && $day <= date('Y-m-d')){
            $item['state'] = $this->setLiveLeaveEarly($day, $leaveInfo['type'] ?? 0) ? self::STATE_EARLY_OUT : self::STATE_NORMAL;
        }

        if (in_array($item['state'], [self::STATE_LATE_IN, self::STATE_EARLY_OUT, self::STATE_NO_RECORD])) {
            //有次数且 未申请 申请状态
            if ($this->checkCanFixAttendance($day)) {
                // 申请补卡状态 0不展示 1 申请补卡 、2申请补卡审批中、3 已补卡
                $item['make_up_state'] = empty($makeUpCardData[$config['make_up_card_index']]) ? self::MAKE_UP_STATE_ALLOW_APPLY : self::MAKE_UP_STATE_WAIT_AUDIT;
            }
        }

        //补的卡 已补卡
        if (isset($attendance[$config['attendance_data_index']]) && in_array($attendance[$config['attendance_data_index']][$config['check_attendance_state']],
                [3, 5])) {
            $item['make_up_state'] = self::MAKE_UP_STATE_FIX_ATTENDANCE_DONE;
        }
       
        //出差 看是否打卡 未审批通过的出差打卡，不处理迟到早退 ，不标记补卡
        if (isset($btAttendance[$config['attendance_data_index']]) && $btAttendance[$config['attendance_data_index']]['status'] == enums::APPROVAL_STATUS_PENDING) {
            $item['state']         = self::STATE_BT_WAIT_AUDIT;
            $item['make_up_state'] = self::MAKE_UP_STATE_NORMAL;
            if($btAttendance[$config['attendance_data_index']][$config['attendance_time_field']]){
                $item['show_info']     = $this->getTranslation()->_('attendance_time' . $suffix).' '.date('m-d H:i', strtotime($btAttendance[$config['attendance_data_index']][$config['attendance_time_field']]));
            }
        }
        //无需打卡 可以给打卡时间 不判断迟到早退
        if (in_array($dateType, array_merge(self::$notNeedAttendanceMap, [$config['no_need_attendance_leave_type']]))) {
            $item['state']         = self::STATE_NORMAL;
            $item['make_up_state'] = self::MAKE_UP_STATE_NORMAL;
            $item['show_info']     = !in_array($item['show_info'],
                [$this->getTranslation()->_('dont_have_attendance_time' . $suffix), $this->getTranslation()->_('no_attendance' . $suffix)]) ? $item['show_info'] : $this->getTranslation()->_('not_need_attendance' . $suffix);
        }
    }

    protected function getShiftType($day): int
    {
        $info = 0;
        $shiftInfo = $this->shiftInfo[$day]??[];
        if(empty($shiftInfo)){
            return $info;
        }
        return intval($shiftInfo['shift_type']);

    }


    protected function getShowShit($day, $attendance_order)
    {
        $time      = '';
        $shiftInfo = $this->shiftInfo[$day] ?? [];
        if (empty($shiftInfo) || $this->isLive) {
            return $time;
        }
        switch ($attendance_order) {
            case self::ONCE_SHIFT_IN:
            case self::TWICE_SHIFT_FIRST_IN:
                $time = $shiftInfo['first_start'];
                break;
            case self::ONCE_SHIFT_OUT:
            case self::TWICE_SHIFT_FIRST_OUT:
                $time = $shiftInfo['first_end'];
                break;
            case self::TWICE_SHIFT_SECOND_IN:
                $time = $shiftInfo['second_start'];
                break;
            case self::TWICE_SHIFT_SECOND_OUT:
                $time = $shiftInfo['second_end'];
                break;
            default:
        }
        return $time;
    }



    protected function getDateType($day,$attendance_order): string
    {
        $shiftInfo = $this->shiftInfo[$day] ?? [];
        if (isset($this->holidays[$day])) {
            return self::DATE_TYPE_PH;
        }
        if (isset($this->staffOffDate[$day]) && $this->staffOffDate[$day] == AttendanceCalendarEnums::TYPE_REST_DAY) {
            return self::DATE_TYPE_REST;
        }
        if (isset($this->staffOffDate[$day]) && $this->staffOffDate[$day] == AttendanceCalendarEnums::TYPE_OFF_DAY) {
            return self::DATE_TYPE_OFF;
        }
        if (isset($this->dateLeave[$day])) {
            if ($this->dateLeave[$day]['type'] == AttendanceCalendarEnums::LEAVE_WHOLE_TYPE) {
                return self::DATE_TYPE_LEAVE_ALL;
            }
            $showLeaveItems = $shiftInfo &&  $shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_ONCE ? [self::ONCE_SHIFT_IN] : [
                self::TWICE_SHIFT_FIRST_IN,
                self::TWICE_SHIFT_FIRST_OUT,
            ];
            if ($this->dateLeave[$day]['type'] == AttendanceCalendarEnums::LEAVE_UP_TYPE && in_array($attendance_order, $showLeaveItems)) {
                return self::DATE_TYPE_LEAVE_UP;
            }
            $showLeaveItems = $shiftInfo && $shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_ONCE ? [self::ONCE_SHIFT_OUT] : [
                self::TWICE_SHIFT_SECOND_IN,
                self::TWICE_SHIFT_SECOND_OUT,
            ];
            if ($this->dateLeave[$day]['type'] == AttendanceCalendarEnums::LEAVE_DOWN_TYPE && in_array($attendance_order, $showLeaveItems)) {
                return self::DATE_TYPE_LEAVE_DOWN;
            }
        }
        //审批通过的出差 才展示出差字样
        if(isset($this->tripDate[$day])){
            return self::DATE_TYPE_BT;
        }
        return self::DATE_TYPE_NORMAL;

    }


    /**
     * 月份统计
     * @param $paramIn
     * @return array
     */
    public function month($paramIn): array
    {
        $start_date = $paramIn['start_date'] ?? $paramIn['month'].'-01';
        $end_date   = $paramIn['end_date'] ?? date('Y-m-t', strtotime($paramIn['month']));
        return $this->initData($paramIn['staff_id'], $start_date, $end_date)->handleMonthData();
    }

    /**
     * 处理月份统计数据
     * @return array
     */
    protected function handleMonthData(): array
    {
        $result = [];
        foreach ($this->monthDay as $day) {
            $item['date']                 = $day;
            $item['is_ph']                = isset($this->holidays[$day]);
            $item['work_from_home']       = $this->setMonthWorkHome($day);
            $item['is_off_leave_day']     = isset($this->staffOffDate[$day]) || isset($this->dateLeave[$day]);
            $item['is_no_record']         = $this->checkIsNoRecord($day);
            $item['is_late_in_early_out'] = $this->checkIsLateOrEarly($day);
            $item['is_support']           = !empty($this->supportData[$day]);
            $result[]                     = $item;
        }
        return $result;
    }


    protected function checkCanFixAttendance($day): bool
    {
        return $this->remainAttendanceCarNum > 0 && $day >= date('Y-m-d',strtotime('-2 days')) && $day <= date('Y-m-d');
    }

    protected function setMonthWorkHome($day){
        $isWorkHome = false;
        //居家办公 历史记录 用打卡表 未来的 用配置
        if($day < $this->today){
            if(!empty($this->staffAData[$day])){
                $isWorkHome = $this->staffAData[$day][0]['started_state'] == StaffWorkAttendanceModel::STATE_WORK_HOME || $this->staffAData[$day][0]['end_state'] == StaffWorkAttendanceModel::STATE_WORK_HOME;
            }
        }else{
            $isWorkHome = in_array($day, $this->workHomeDates);
        }
        return $isWorkHome;
    }


    /**
     * 判断迟到早退
     * @param $day
     * @return bool
     */
    protected function checkIsLateOrEarly($day): bool
    {
        //未来的都不缺卡
        if ($day > date('Y-m-d')) {
            return false;
        }
        //休息日 不缺勤
        if (isset($this->staffOffDate[$day])) {
            return false;
        }
        //公休日 不缺勤
        if (isset($this->holidays[$day])) {
            return false;
        }
        //全天假  不缺勤
        $leaveInfo = $this->dateLeave[$day] ?? [];
        if (!empty($leaveInfo) && $leaveInfo['type'] == AttendanceCalendarEnums::LEAVE_WHOLE_TYPE) {
            return false;
        }
        //考勤数据 产品说了，没打卡不计算迟到早退
        $attendanceInfo = $this->staffAData[$day] ?? [];
        if (empty($attendanceInfo)) {
            return false;
        }
        //判断 是否是主播
        if ($day < date('Y-m-d')) {
            $this->isLive = in_array($this->transferData[$day]['job_title'], $this->liveJobId);
        } else {
            $this->isLive = in_array($this->staffInfo['job_title'], $this->liveJobId);
        }

        if($this->isLive){
            return $this->setLiveLeaveEarly($day, $leaveInfo['type'] ?? 0);
        }

        //班次信息
        $shiftInfo = $this->shiftInfo[$day] ?? [];
        //一个班次的
        if (!empty($shiftInfo['shift_type']) && $shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_ONCE) {
            //验证迟到的时间节点
            $check_late_in_time = $shiftInfo['first_check_late_in_time'];
            //验证早退的时间节点
            $check_early_out_time = $shiftInfo['first_check_early_out_time'];

            //请上午假 根据休息结束时间看迟到、根据下班班次时间看早退
            if (!empty($leaveInfo) && $leaveInfo['type'] == AttendanceCalendarEnums::LEAVE_UP_TYPE) {
                $check_late_in_time   = $shiftInfo['up_leave_check_late_in_time'];
                $check_early_out_time = $shiftInfo['up_leave_check_early_out_time'];
            }
            //请下午假 根据班次开始时间开迟到、根据休息开始时间看早退
            if (!empty($leaveInfo) && $leaveInfo['type'] == AttendanceCalendarEnums::LEAVE_DOWN_TYPE) {
                $check_late_in_time   = $shiftInfo['down_leave_check_late_in_time'];
                $check_early_out_time = $shiftInfo['down_leave_check_early_out_time'];
            }
            //打卡了
            if (isset($attendanceInfo[0])) {
                //迟到
                if (!empty($attendanceInfo[0]['started_at']) && strtotime($attendanceInfo[0]['started_at']) >= strtotime($check_late_in_time)) {
                    return true;
                }
                //早退
                if (!empty($attendanceInfo[0]['end_at']) && strtotime($attendanceInfo[0]['end_at']) <= strtotime($check_early_out_time)) {
                    return true;
                }
            }
        }
        // 两个班次的
        if (isset($shiftInfo['shift_type']) && $shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE) { //两个班次的
            //请的半天的假
            if (!empty($leaveInfo)) {
                //判断迟到的时间节点
                $check_late_in_time     = $leaveInfo['type'] == AttendanceCalendarEnums::LEAVE_UP_TYPE ? $shiftInfo['up_leave_check_late_in_time'] : $shiftInfo['down_leave_check_late_in_time'];
                // 判断早退的时间节点
                $check_early_out_time   = $leaveInfo['type'] == AttendanceCalendarEnums::LEAVE_UP_TYPE ? $shiftInfo['up_leave_check_early_out_time'] : $shiftInfo['down_leave_check_early_out_time'];
                //考勤数据
                $index                  = $leaveInfo['type'] == AttendanceCalendarEnums::LEAVE_UP_TYPE ? 2 : 1;
                if (isset($attendanceInfo[$index])) {
                    //迟到
                    if (!empty($attendanceInfo[$index]['started_at']) && strtotime($attendanceInfo[$index]['started_at']) >= strtotime($check_late_in_time)) {
                        return true;
                    }
                    //早退
                    if (!empty($attendanceInfo[$index]['end_at']) && strtotime($attendanceInfo[$index]['end_at']) <= strtotime($check_early_out_time)) {
                        return true;
                    }
                }
                return false;
            }


            //第一个班的迟到
            if ((isset($attendanceInfo[1]) && strtotime($attendanceInfo[1]['started_at']) >= strtotime($shiftInfo['first_check_late_in_time']) )) {
                return true;
            }


            //第一个班的早退
            if ((isset($attendanceInfo[1]) && !empty($attendanceInfo[1]['end_at']) &&  strtotime($attendanceInfo[1]['end_at']) <= strtotime($shiftInfo['first_check_early_out_time']) )) {
                return true;
            }
            //第二个班的迟到
            if ((isset($attendanceInfo[2]) && strtotime($attendanceInfo[2]['started_at']) >= strtotime($shiftInfo['second_check_late_in_time']) )) {
                return true;
            }

            //第二个班的早退
            if ((isset($attendanceInfo[2]) && !empty(strtotime($attendanceInfo[2]['end_at']) ) && strtotime($attendanceInfo[2]['end_at']) <= strtotime($shiftInfo['second_check_early_out_time']) )) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断缺卡
     * @param $day
     * @return bool
     */
    protected function checkIsNoRecord($day): bool
    {
        //未来的都不缺卡
        if ($day > date('Y-m-d')) {
            return false;
        }
        //休息日 不缺勤
        if (isset($this->staffOffDate[$day])) {
            return false;
        }
        //公休日 不缺勤
        if (isset($this->holidays[$day])) {
            return false;
        }
        //全天假  不缺勤
        $leaveInfo = $this->dateLeave[$day] ?? [];
        if (!empty($leaveInfo) && $leaveInfo['type'] == AttendanceCalendarEnums::LEAVE_WHOLE_TYPE) {
            return false;
        }
        //打卡表查
        $attendanceInfo = $this->staffAData[$day]??[];
        //出差
        $btAttendanceInfo = $this->staffAuditReissueForBusiness[$day]??[];
        //判断 是否是主播
        if ($day < date('Y-m-d')) {
            $this->isLive = !empty($this->transferData[$day]) && in_array($this->transferData[$day]['job_title'], $this->liveJobId);
        } else {
            $this->isLive = in_array($this->staffInfo['job_title'], $this->liveJobId);
        }

        //班次信息
        $shiftInfo    = $this->shiftInfo[$day] ?? [];
        //主播职位 显示缺卡
        if($this->isLive){
            if($day < date('Y-m-d') && empty($attendanceInfo[0]['started_at'])){
                return true;
            }
            if($day <= date('Y-m-d') && !empty($attendanceInfo[0]['started_at']) && empty($attendanceInfo[0]['end_at'])){
                $start = $attendanceInfo[0]['started_at'];
                if(empty($start)){
                    return true;
                }else{
                    //判断22小时
                    return $start <= date('Y-m-d H:i:s',strtotime('-22 hour'));
                }
            }
        }

        $current_time = date('Y-m-d H:i:s');
        //一个班次的
        if (!empty($shiftInfo['shift_type']) && $shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_ONCE) {
            //出差 打卡 待审批 不计算缺卡
            if(isset($btAttendanceInfo[0]) && $btAttendanceInfo[0]['status'] == enums::APPROVAL_STATUS_PENDING){
                return false;
            }
            //是否超过下班班次打卡截止时间
            $is_over_time = $current_time > $shiftInfo['first_check_no_record_time'];
            //没请假看全天缺卡不
            return $is_over_time && (!isset($attendanceInfo[0]) || empty($attendanceInfo[0]['started_at']) || empty($attendanceInfo[0]['end_at']));
        }
        // 两个班次的
        if (isset($shiftInfo['shift_type']) && $shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE) { //两个班次的

            //出差 打卡 待审批 不计算缺卡
            if(isset($btAttendanceInfo[1]) && $btAttendanceInfo[1]['status'] == enums::APPROVAL_STATUS_PENDING){
                return false;
            }
            //出差 打卡 待审批 不计算缺卡
            if(isset($btAttendanceInfo[2]) && $btAttendanceInfo[2]['status'] == enums::APPROVAL_STATUS_PENDING){
                return false;
            }
            //请第一个班次的假 看第二个班次缺卡不
            if (!empty($leaveInfo) && $leaveInfo['type'] == AttendanceCalendarEnums::LEAVE_UP_TYPE) {
                //当前时间超过下班班次结束时间 加 最晚可打下班卡时间分钟数 且 还没打第二个班次的下班卡
                return $current_time > $shiftInfo['second_check_no_record_time'] && (!isset($attendanceInfo[2]) || empty($attendanceInfo[2]['started_at']) || empty($attendanceInfo[2]['end_at']));
            }

            //请第二个班次的假 看第一个班次缺卡不
            if (!empty($leaveInfo) && $leaveInfo['type'] == AttendanceCalendarEnums::LEAVE_DOWN_TYPE) {
                return $current_time > $shiftInfo['first_check_no_record_time'] && (!isset($attendanceInfo[1]) || empty($attendanceInfo[1]['started_at']) || empty($attendanceInfo[1]['end_at']));
            }
            return ($current_time > $shiftInfo['first_check_no_record_time']  && (!isset($attendanceInfo[1]) || empty($attendanceInfo[1]['started_at']) || empty($attendanceInfo[1]['end_at'])))
                || ($current_time > $shiftInfo['second_check_no_record_time'] && (!isset($attendanceInfo[2]) || empty($attendanceInfo[2]['started_at']) || empty($attendanceInfo[2]['end_at'])));
        }

        return false;
    }

    /**
     * 每日数据
     * @param $paramIn
     * @return array
     */
    public function daily($paramIn): array
    {
        $this->remainAttendanceCarNum = 3 -  (new AuditRepository($this->lang))->getAttendanceCarMonthData(['staff_id'=> $this->staffId, 'reissue_card_date' => date('Y-m-d')]);
        $data = $this->initData($paramIn['staff_id'], $paramIn['date'], $paramIn['date'])->handleDailyData();
        return $data[$paramIn['date']];
    }




    /**
     * 获取考勤信息
     * @param $staffId
     * @param $start_date
     * @param $end_date
     * @return array
     */
    public function getAttendanceMess($staffId, $start_date, $end_date): array
    {
        $selectSql  = "select 
                            started_state,
                            started_state as start_state,
                            end_state,
                            staff_info_id,
                            attendance_date,
                            shift_start,
                            shift_end,
                            shift_id,
                            shift_ext_id,
                            shift_type,
                            started_at AS start_data,
                            CONVERT_TZ( started_at, '+00:00', :time_zone ) AS started_at,
                            CONVERT_TZ( end_at, '+00:00', :time_zone ) AS end_at  ,
                            TIMESTAMPDIFF(hour ,started_at,end_at) sub_time 
                       from staff_work_attendance where staff_info_id= :staff_info_id
                       AND attendance_date >= :start_time
                       AND attendance_date <= :end_time";
        $data       = $this->getDI()->get('db_rby')->query($selectSql, [
            'time_zone'     => $this->timeZone,
            'staff_info_id' => $staffId,
            'start_time'    => $start_date,
            'end_time'      => $end_date,
        ]);

        $staffAData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $result     = [];
        foreach ($staffAData as $staffADatum) {
            $result[$staffADatum['attendance_date']][$staffADatum['shift_type']] = $staffADatum;
        }
        return $result;
    }


    /**
     * 出差数据
     * @param $staff_id
     * @param $start_date
     * @param $end_date
     * @param array $status
     * @return array
     */
    public function staff_audit_reissue_for_business($staff_id, $start_date, $end_date, array $status = [1])
    {
        $result = [];
        $data = StaffAuditReissueForBusinessModel::find(
            [
                'columns' => "attendance_date,start_time AS started_at,end_time AS end_at,business_trip_type,shift_type,status,start_time_zone,end_time_zone",
                'conditions' => 'staff_info_id = :staff_info_id: AND attendance_date >= :start_date: AND  attendance_date <= :end_date:  AND status in ({status:array}) ',
                'bind' => ['staff_info_id' => $staff_id, 'start_date' => $start_date, 'end_date' => $end_date, 'status' =>$status]
            ]
        )->toArray();
        if(empty($data)){
            return  $result;
        }
        $AttendanceBusinessServer = new AttendanceBusinessServer($this->lang, $this->timeZone);
        $add_hour = $this->config->application->add_hour;


        foreach ($data as $datum) {
            //计算出差打卡需要的时区偏移量  秒
            [$start_time_zone_s, $end_time_zone_s] = $AttendanceBusinessServer->getCalculateTimeZoneSecond($datum);
            $datum['started_at'] = empty($datum['started_at']) ? $datum['started_at'] : date('Y-m-d H:i:s',
                strtotime($datum['started_at']) + $start_time_zone_s + ($add_hour * 3600));
            $datum['end_at']     = empty($datum['end_at']) ? $datum['end_at'] : date('Y-m-d H:i:s',
                strtotime($datum['end_at']) + $end_time_zone_s + ($add_hour * 3600));

            $result[$datum['attendance_date']][$datum['shift_type']] = $datum;
        }
        return $result;
    }

    /**
     * 获取补卡记录
     * @param $staff_id
     * @param $start_date
     * @param $end_date
     * @return array
     */
    public function getMakeUpCardData($staff_id, $start_date, $end_date): array
    {
        $result = [];
        $auditData = StaffAuditModel::find(
            [
                'columns' => "attendance_date,reissue_card_date,attendance_type",
                'conditions' => 'staff_info_id = :staff_info_id: AND attendance_date >= :start_date: AND  attendance_date <= :end_date:  AND status = :status: AND audit_type = :audit_type: AND attendance_type in ({attendance_type:array})  ',
                'bind' => ['staff_info_id' => $staff_id, 'start_date' => $start_date, 'end_date' => $end_date, 'status' =>1,'audit_type'=>1,'attendance_type'=>[1,2,3,4]]
            ]
        )->toArray();

        if (empty($auditData)) {
            return $result;
        }
        foreach ($auditData as $item) {
            $result[$item['attendance_date']][$item['attendance_type']] = $item;
        }
        unset($auditData);
        return $result;
    }



    /**
     * 每日班次信息
     * @param $staff_id
     * @param $start_date
     * @param $end_date
     * @return array
     */
    public function getDailyShiftInfo($staff_id, $start_date, $end_date): array
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'ss.shift_id',
            'ss.shift_extend_id',
            'ss.shift_date',
            'se.work_time',
            'se.shift_type',
            'first_earliest',
            'first_start',
            'first_end',
            'first_latest',
            'first_late',
            'first_leave_early',
            'second_earliest',//最早可提前打卡分钟
            'second_start',
            'second_end',
            'second_latest',
            'second_late',
            'second_leave_early',
            'middle_rest_start',
            'middle_rest_end',
            'is_middle_rest',
        ]);
        $builder->from(['ss' => HrStaffShiftV2Model::class]);
        $builder->innerJoin(HrShiftV2ExtendModel::class, 'ss.shift_extend_id = se.id', 'se');
        $builder->where('ss.staff_info_id = :staff_info_id: AND ss.shift_date >= :start_shift_date: AND  ss.shift_date <= :end_shift_date:',
            ['staff_info_id' => $staff_id, 'start_shift_date' => $start_date, 'end_shift_date' => $end_date]);
        $shiftInfos = $builder->getQuery()->execute()->toArray();

        //查询 transfer 表获取职位
        $transferData = HrStaffTransferModel::find([
            'columns' => 'staff_info_id, stat_date, job_title',
            'conditions' => 'staff_info_id = :staff_id: and stat_date between :start_date: and :end_date:',
            'bind' => ['staff_id' => $staff_id, 'start_date' => $start_date, 'end_date' => $end_date]
        ])->toArray();
        $this->transferData = $transferData = empty($transferData) ? [] : array_column($transferData,null, 'stat_date');

        foreach ($shiftInfos as &$shiftInfo) {
            if(!empty($transferData[$shiftInfo['shift_date']]) && in_array($transferData[$shiftInfo['shift_date']]['job_title'], $this->liveJobId)){
                $shiftInfo = null;
                continue;
            }

            $real_early_out_day                      = $shiftInfo['first_start'] > $shiftInfo['first_end'] ? date('Y-m-d', strtotime($shiftInfo['shift_date'].' +1 day')) : $shiftInfo['shift_date'];
            //判断迟到的时间节点
            $shiftInfo['first_check_late_in_time']   = date('Y-m-d H:i:s', strtotime($shiftInfo['shift_date'].' '.$shiftInfo['first_start']) + $shiftInfo['first_late'] * 60);
            //判断早退的时间节点
            $shiftInfo['first_check_early_out_time'] = date('Y-m-d H:i:s', strtotime($real_early_out_day.' '.$shiftInfo['first_end']) - $shiftInfo['first_leave_early'] * 60);
            //判断旷工的时间节点
            $shift_end_time                          = $shiftInfo['first_start'] > $shiftInfo['first_end'] ? strtotime($shiftInfo['shift_date'].' '.$shiftInfo['first_end'].' +1 day') : strtotime($shiftInfo['shift_date'].' '.$shiftInfo['first_end']);
            //是否超过下班班次打卡截止时间
            $shiftInfo['first_check_no_record_time'] = date('Y-m-d H:i:s',$shift_end_time + $shiftInfo['first_latest'] * 60);
            //第一个班最早可打卡时间
            $shiftInfo['first_allow_attendance_time'] = date('Y-m-d H:i:s',strtotime($shiftInfo['shift_date'].' '. $shiftInfo['first_start'])-$shiftInfo['first_earliest']*60);

            //两个班次的情况
            if ($shiftInfo['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE) {
                $second_real_late_in_day = $shiftInfo['first_start'] > $shiftInfo['second_start'] ? date('Y-m-d', strtotime($shiftInfo['shift_date'].' +1 day')) : $shiftInfo['shift_date'];


                //第二个班最早可打卡时间
                $shiftInfo['second_allow_attendance_time'] = date('Y-m-d H:i:s',strtotime($second_real_late_in_day .' '. $shiftInfo['second_start'])-$shiftInfo['second_earliest']*60);


                //是否超过第二个班次的下班打卡截止时间
                $second_shift_end = $shiftInfo['first_start'] > $shiftInfo['second_end'] ? strtotime($shiftInfo['shift_date'].' '.$shiftInfo['second_end'].' +1 day') : strtotime($shiftInfo['shift_date'].' '.$shiftInfo['second_end']);
                $shiftInfo['second_check_no_record_time'] = date('Y-m-d H:i:s',$second_shift_end + $shiftInfo['second_latest'] * 60);

                //判定迟到的日期
                //判定早退所在的日期
                $second_real_early_out_day                = $shiftInfo['first_start'] > $shiftInfo['second_end'] ? date('Y-m-d', strtotime($shiftInfo['shift_date'].' +1 day')) : $shiftInfo['shift_date'];
                $shiftInfo['second_check_late_in_time']   = date('Y-m-d H:i:s', strtotime($second_real_late_in_day.' '.$shiftInfo['second_start']) + $shiftInfo['second_late'] * 60);
                $shiftInfo['second_check_early_out_time'] = date('Y-m-d H:i:s', strtotime($second_real_early_out_day.' '.$shiftInfo['second_end']) - $shiftInfo['second_leave_early'] * 60);

                //请第一个班次的假 判断迟到的时间节点
                $shiftInfo['up_leave_check_late_in_time'] = $shiftInfo['second_check_late_in_time'];
                //请第一个班次的假 判断早退的时间节点
                $shiftInfo['up_leave_check_early_out_time'] = $shiftInfo['second_check_early_out_time'];
                //请第二个班次的假 判断迟到的时间节点
                $shiftInfo['down_leave_check_late_in_time'] = $shiftInfo['first_check_late_in_time'];
                //请第二个班次的假 判断早退的时间节点
                $shiftInfo['down_leave_check_early_out_time'] = $shiftInfo['first_check_early_out_time'];
            } else {
                //请上半个班次的假
                $real_late_in_day = $shiftInfo['first_start'] > $shiftInfo['middle_rest_end'] ? date('Y-m-d', strtotime($shiftInfo['shift_date'].' +1 day')) : $shiftInfo['shift_date'];
                //请上半个班次的假 判断迟到的时间节点
                $shiftInfo['up_leave_check_late_in_time']   = date('Y-m-d H:i:s', strtotime($real_late_in_day.' '.$shiftInfo['middle_rest_end']) + $shiftInfo['first_late'] * 60);
                //请上半个班次的假 判断早退的时间节点
                $shiftInfo['up_leave_check_early_out_time'] = $shiftInfo['first_check_early_out_time'];

                //请下半个班次的假
                //请下半个班次的假 判断迟到的时间节点
                $shiftInfo['down_leave_check_late_in_time']   = $shiftInfo['first_check_late_in_time'];
                $real_early_out_day                         = $shiftInfo['first_start'] > $shiftInfo['middle_rest_start'] ? date('Y-m-d', strtotime($shiftInfo['shift_date'].' +1 day')) : $shiftInfo['shift_date'];
                //请下半个班次的假 判断早退的时间节点
                $shiftInfo['down_leave_check_early_out_time'] = date('Y-m-d H:i:s', strtotime($real_early_out_day.' '.$shiftInfo['middle_rest_start']) - $shiftInfo['first_leave_early'] * 60);
            }
        }
        return array_column($shiftInfos, null, 'shift_date');
    }


    //新类型员工 翻译key 变更 前端用
    public function getHireType(){
        return $this->staffInfo['hire_type'];
    }

    public function getIsLive(){
        return in_array($this->staffInfo['job_title'], $this->liveJobId);
    }


    /**
     * 标记 主播因 实际工作时长 小于 应工作时长 标记 早退
     *
     * 仅主播职位 使用
     *
     * @param $date
     * @param $leaveType
     * @return bool
     */
    public function setLiveLeaveEarly($date, $leaveType)
    {
        if(empty($this->staffAData[$date][0]['started_at']) || empty($this->staffAData[$date][0]['end_at'])) {
            return false;
        }

        $hours = $this->attendanceServer->getLiveHours($date, $this->staffAData[$date][0]['started_at'], $leaveType);

        //不考虑宽限分钟数，精确到分钟
        $start_at = date('Y-m-d H:i', strtotime($this->staffAData[$date][0]['started_at']));
        $end_at = date('Y-m-d H:i', strtotime($this->staffAData[$date][0]['end_at']));

        //实际工作时长 大于 等于 应工作时长 则 不标记
        $min = floor((strtotime($end_at) - strtotime($start_at)) / 60);

        if($min >= $hours * 60) {
            return false;
        }

        return true;
    }


}