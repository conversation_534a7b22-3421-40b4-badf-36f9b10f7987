<?php

namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\ThailandHolidayModel;
use FlashExpress\bi\App\Models\backyard\HrStaffTransferModel;
use FlashExpress\bi\App\Modules\My\library\Enums\CommonEnums;
use FlashExpress\bi\App\Repository\StaffPublicHolidayRepository;
use FlashExpress\bi\App\Server\AttendanceCalendarServer as BaseServer;

class AttendanceCalendarServer extends BaseServer
{

    public function getHolidayData($startDate = '', $endDate = ''): array
    {
        //获取固化数据
        $bind['staff_info_id'] = $this->staffInfo['staff_info_id'];
        $bind['start_date']    = $startDate;
        $bind['end_date']      = $endDate;

        $hrStaffTransfer = HrStaffTransferModel::find([
            'conditions' => 'staff_info_id = :staff_info_id: and stat_date >= :start_date: and stat_date <= :end_date: ',
            'columns'    => 'store_id,province_code,staff_info_id,stat_date,week_working_day,store_id as sys_store_id',
            'bind'       => $bind,
        ])->toArray();
        unset($bind);
        //员工信息固化数据
        $historyStaffInfoMap = array_column($hrStaffTransfer, null, 'stat_date');

        //获取指定区间的公共假期
        $condition          = 'day >= :start_date: and day <= :end_date:';
        $bind['start_date'] = $startDate;
        $bind['end_date']   = $endDate;

        //获取指定区间的公共假期
        $holidayData = ThailandHolidayModel::find([
            'conditions' => $condition,
            'columns'    => 'day,province_code,type',
            'bind'       => $bind,
        ])->toArray();
        $holidayMap  = [];
        foreach ($holidayData as $holidayDatum) {
            if ($holidayDatum['type'] == ThailandHolidayModel::TYPE_DEFAULT) {
                $holidayMap[$holidayDatum['day'].'-'.HrStaffInfoModel::WEEK_WORKING_DAY_5.'-'.$holidayDatum['province_code']] = $holidayDatum;
                $holidayMap[$holidayDatum['day'].'-'.HrStaffInfoModel::WEEK_WORKING_DAY_6.'-'.$holidayDatum['province_code']] = $holidayDatum;
                $holidayMap[$holidayDatum['day'].'-'.HrStaffInfoModel::WEEK_WORKING_DAY_FREE.'-'.$holidayDatum['province_code']] = $holidayDatum;
            } elseif ($holidayDatum['type'] == ThailandHolidayModel::TYPE_WEEK_WORKING_DAY_6) {
                $holidayMap[$holidayDatum['day'].'-'.HrStaffInfoModel::WEEK_WORKING_DAY_6.'-'.$holidayDatum['province_code']] = $holidayDatum;
            } elseif ($holidayDatum['type'] == ThailandHolidayModel::TYPE_WEEK_WORKING_DAY_5) {
                $holidayMap[$holidayDatum['day'].'-'.HrStaffInfoModel::WEEK_WORKING_DAY_5.'-'.$holidayDatum['province_code']] = $holidayDatum;
            }
        }

        //获取补的PH
        $staffPublicHoliday = array_column((new StaffPublicHolidayRepository($this->lang,
            $this->timeZone))->getStaffData($this->staffInfo['staff_info_id']), 'date_at');

        //获取指定区间的日期
        $allDays = DateHelper::DateRange(strtotime($startDate), strtotime($endDate));
        $storeServer = new SysStoreServer($this->lang,$this->timeZone);
        $staffHoliday = [];
        foreach ($allDays as $day) {
            //补的ph 直接给
            if (in_array($day, $staffPublicHoliday)) {
                $staffHoliday[$day] = true;
                continue;
            }
            //从员工信息固化表捞取数据
            if ($staticStaffInfo = $historyStaffInfoMap[$day] ?? null) {
                $province_code = $storeServer->getProvince($staticStaffInfo);
                if (isset($holidayMap[$day.'-'.$staticStaffInfo['week_working_day'].'-'.$province_code])) {
                    $staffHoliday[$day] = true;
                }
                continue;
            }
            $province_code = $storeServer->getProvince($this->staffInfo);
            if (isset($holidayMap[$day.'-'.$this->staffInfo['week_working_day'].'-'.$province_code])) {
                $staffHoliday[$day] = true;
            }
        }
        return $staffHoliday;
    }

}