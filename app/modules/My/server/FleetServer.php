<?php


namespace FlashExpress\bi\App\Modules\My\Server;

use FlashExpress\bi\App\Models\fle\SysFleetVanTypeModel;
use FlashExpress\bi\App\Server\FleetServer as GlobalBaseServer;

class FleetServer extends GlobalBaseServer
{
    /**
     * 获取加班车类型列表
     * MY就车型废弃，追加新的车型。但是就的车型要可以显示。
     * 这里为新的加班车下拉选项，反解析请使用 baseServer中的getCarTypeList
     *
     * 需求：
     * https://flashexpress.feishu.cn/docx/VF3TdexdPokAstxMOkicQRcknKf
     */
    public function getCarTypeListV2($carType = 0): array
    {
        //从java表中获取车辆全部类型
        $list = SysFleetVanTypeModel::find([
            'conditions' => "deleted = 0 and code between :start_id: and :end_id:",
            'bind' => [
                'start_id' => 227,
                'end_id'   => 233,
            ],
            'columns' => 'van_value as type_txt, code as type',
        ])->toArray();
        if (empty($list)) {
            $list = SysFleetVanTypeModel::find([
                'conditions' => " deleted = 0 ",
                'columns' => 'van_value as type_txt, code as type',
            ])->toArray();
        }
        return $list;
    }
}