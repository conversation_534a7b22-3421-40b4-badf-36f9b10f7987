<?php

namespace FlashExpress\bi\App\Modules\My\library\Enums;


class TicketEnums
{
    public static $ticket_job_title = [
        'area_manager' => 79, //Area Manager
        'network_operations_manager' => 491,//Network Operations Manager
        'shop_operations_manager' => 291,//Shop Operations Manager
        'claim_manager' => 215,//Claim Manager
        'project_manager' => 73, //Project Manager
        'customer_service_director' => 216,//Customer Service Director
        'project_director' => 946, //Project Director
        'hub_area_manager' => 724, //Hub Area Manage
        'hub_director' => 557, //Hub Director
        'claim_supervisor' => 939, //Claim Supervisor
        self::JOB_TITLE_HEAD_OF_NETWORK_OPERATIONS,
        self::JOB_TITLE_HEAD_OF_PROJECT_MANAGEMENT,
        self::JOB_TITLE_HEAD_OF_HUB_MANAGEMENT,
        self::JOB_TITLE_F_COM_CUSTOMER_SERVICE_SUPERVISOR,
        self::JOB_TITLE_KEY_ACCOUNT_SUPERVISOR,
        self::JOB_TITLE_HUB_SUPERVISOR,
        self::JOB_TITLE_PROJECT_MANAGEMENT_DEPUTY_DIRECTOR,
    ];

    //职位
    const JOB_TITLE_HEAD_OF_NETWORK_OPERATIONS = 1094;
    const JOB_TITLE_HEAD_OF_PROJECT_MANAGEMENT = 1320;
    const JOB_TITLE_HEAD_OF_HUB_MANAGEMENT = 1095; //Head of Hub Management
    const JOB_TITLE_F_COM_CUSTOMER_SERVICE_SUPERVISOR = 1227;
    const JOB_TITLE_KEY_ACCOUNT_SUPERVISOR = 259;
    const JOB_TITLE_HUB_SUPERVISOR = 272;
    const JOB_TITLE_PROJECT_MANAGEMENT_DEPUTY_DIRECTOR = 1102;

    const CS_TICKET_STORE_3 = 3;
    const CS_TICKET_STORE_22 = 324;
    public static $cs_ticker_store = [
        self::CS_TICKET_STORE_3  => 'CS',
        self::CS_TICKET_STORE_22  => 'KAM',
    ];

    const DEPARTMENT_CS = 3;
    const DEPARTMENT_KAM = 324;
    public static $cs_department = [
        self::DEPARTMENT_CS => 'department_3',
        self::DEPARTMENT_KAM => 'department_22',
    ];

    public static function getCodeTxtMap($lang = '')
    {
        return [];
    }
}
