<?php
// CEO 信箱 2.0版本 2020.11

namespace FlashExpress\bi\App\Modules\My\library\Enums;

use FlashExpress\bi\App\Enums\CeoMailEnums as BaseCeoMailEnums;

class CeoMailEnums extends BaseCeoMailEnums
{
    // 需要阅读信息承诺书的分类 = 针对大类(子类继承大类该属性) Information Commitment My 没有
    const MUST_READ_INFORMATION_COMMITMENT_CATEGORY_IDS = [
    ];

    const ABOUT_MALAYSIA_EXPRESS_CATEGORY_ID = 127;//关于Flash Malaysia Express薪酬发放
    const ABOUT_COMMERCE_CATEGORY_ID         = 128;//关于Flash Commerce Malaysia薪酬发放
    const ABOUT_FULFILLMENT_CATEGORY_ID      = 129;//关于Flash Fulfillment Malaysia薪酬发放
    const ABOUT_PAY_CATEGORY_ID              = 130;//关于Flash Pay Malaysia薪酬发放
    const ABOUT_MONEY_CATEGORY_ID            = 131;//关于Flash Money Malaysia薪酬发放

    const ABOUT_NETWORK_COMMISSION_CATEGORY_ID = 225;//没有收到服务费/服务费不正确

    const ABOUT_SYS_OTHER_CATEGORY_ID        = 218;//关于快递产品-其他28

    //关于薪酬分类对应的公司
    const COMPANY_COMMERCE    = 15006;
    const COMPANY_FULFILLMENT = 20001;
    const COMPANY_PAY         = 60001;
    const COMPANY_MONEY       = 15050;

    //公司 与 关于薪酬分类的关联关系
    const COMPENSATION_COMPANY_RELATE = [
        self::COMPANY_COMMERCE    => self::ABOUT_COMMERCE_CATEGORY_ID,
        self::COMPANY_FULFILLMENT => self::ABOUT_FULFILLMENT_CATEGORY_ID,
        self::COMPANY_PAY         => self::ABOUT_PAY_CATEGORY_ID,
        self::COMPANY_MONEY       => self::ABOUT_MONEY_CATEGORY_ID
    ];
}
