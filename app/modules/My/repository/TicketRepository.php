<?php

namespace FlashExpress\bi\App\Modules\My\Repository;


use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Modules\My\library\Enums\TicketEnums;
use FlashExpress\bi\App\Repository\TicketRepository as TicketBaseRepository;

class TicketRepository extends TicketBaseRepository
{
    public function getNums($userInfo)
    {
        $data = [];
        $data['is_it'] = 0;
        $data['is_dm'] = 0;
        $data['reply_num'] = $this->getNum(enums::TICKET_STATUS_REPLY, $userInfo['id']);
        $data['audit_num'] = 0;

        if ($this->isIt($userInfo)) {
            $data['is_it'] = 1;
            $data['audit_num'] = $this->getNum(enums::TICKET_STATUS_WAIT_REPLY, $userInfo['id'], true);
        }
        return $data;
    }

    /**
     * 是否有工单
     * @param $userInfo
     * @return bool
     */
    public function isDM($userInfo)
    {
        $sql  = "select  count(1) as num from sys_manage_piece   where manager_id =:staff_id";
        $sql2 = "select count(1) as num from sys_manage_region  where manager_id =:staff_id";
        $bind['staff_id'] = $userInfo['id'];
        $piece = $this->getDI()->get('db_rby')->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC, $bind);
        $region = $this->getDI()->get('db_rby')->fetchOne($sql2, \Phalcon\Db::FETCH_ASSOC, $bind);

        //新增逻辑，是否为ka 项目经理
        $is_project_manager = $this->getKaManager($userInfo['id']);


        $this->logger->write_log('[isDM]' . json_encode($userInfo), 'info');

        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind' => [ 'staff_info_id' => $userInfo['id'] ],
            'columns' => 'job_title'
        ]);
        $job_title = '';
        if(!empty($staffInfo)) {
            $staffInfo = $staffInfo->toArray();
            $job_title = $staffInfo['job_title'];
        }

        $ticket_job_title = array_values(TicketEnums::$ticket_job_title);
        if($piece['num'] > 0 || $region['num'] > 0 || in_array($job_title, $ticket_job_title) || $is_project_manager) {
            return true;
        }
        return false;
    }
}