<?php

namespace FlashExpress\bi\App\Modules\My\Repository;

class JobtransferRepository extends \FlashExpress\bi\App\Repository\JobtransferRepository
{



    public function getJobtransferInfo($paramIn = [])
    {
        $id      = $paramIn['id'] ?? "";
        $staffId = $paramIn['staff_id'] ?? "";
        $state   = $paramIn['state'] ?? "";
        if (!$id && !$staffId) {
            return [];
        }
        $sql = "--
                SELECT
                    jt.id,
                    jt.serial_no,
                    jt.staff_id,
                    jt.hc_id,
                    jt.state,
                    jt.`type`,
                    jt.current_department_id,
                    jt.current_store_id,
                    jt.current_position_id,
                    jt.current_role_id,
                    jt.current_manager_id,
                    jt.current_indirect_manger_id,
                    jt.after_department_id,
                    jt.after_store_id,
                    jt.after_position_id,
                    jt.after_manager_id,
                    DATE_FORMAT(jt.after_date,'%Y-%m-%d') as after_date,
                    jt.reason,
                    jt.job_handover_staff_id,
                    jt.is_pay_adjustment,
                    jt.approval_state,
                    jt.submitter_id,
                    jt.reject_reason,
                    DATE_FORMAT( CONVERT_TZ( jt.created_at, '+00:00', '{$this->timezone}' ), '%Y-%m-%d %H:%i:%s' ) AS created_at,
                    DATE_FORMAT( CONVERT_TZ( jt.updated_at, '+00:00', '{$this->timezone}' ), '%Y-%m-%d %H:%i:%s' ) AS updated_at,
                    jt.deleted,
                    jt.before_base_salary,
                    jt.after_base_salary,
                    jt.after_exp_allowance,
                    jt.after_position_allowance,
                    jt.after_car_rental,
                    jt.after_trip_payment,
                    jt.after_notebook_rental,
                    jt.after_recommended,
                    jt.after_food_allowance,
                    jt.after_dangerous_area,
                    jt.after_gasoline_allowance,
                    jt.after_house_rental,
                    jt.after_mobile_allowance,
                    jt.after_attendance_allowance,
                    jt.after_fuel_allowance,
                    jt.after_car_allowance,
                    jt.after_vehicle_allowance,
                    jt.after_gdl_allowance,
                    jt.after_island_allowance,
                    jt.after_deminimis_benefits,
                    jt.after_performance_allowance,
                    jt.after_other_non_taxable_allowance,
                    jt.after_other_taxable_allowance,
                    jt.after_phone_subsidy,
                    jt.hc_expiration_date,
                    jt.after_role_ids,
                    jt.rental_car_cteated_at,
                    jt.car_owner,
                    jt.senior_auditor,
                     jt.reject_salary_detail,
                    jt.vehicle_source,
                    jt.after_site_allowance
                FROM
                    job_transfer AS jt 
                WHERE
                    deleted=0 AND ";
        if ($id) {
            $sql .= "jt.id = {$id}";
        } elseif ($staffId) {
            $sql .= "jt.staff_id = {$staffId}";
        } else {
            return [];
        }
        if ($state) {
            $sql .= " AND jt.state = {$state}";
        }
        $obj  = $this->getDI()->get('db')->query($sql);
        $data = $obj->fetch(\Phalcon\Db::FETCH_ASSOC);


        $query_img_sql = "
                --
                select 
                    bucket_name
                    ,object_key
                from sys_attachment 
                where oss_bucket_key = ?
                    and oss_bucket_type = 'JOB_TRANSFER' 
                    and deleted = 0";
        $images = $this->getDI()->get('db')->query($query_img_sql, [$id])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (!empty($images)) {
            $data['image_path'] = [];
            foreach ($images as $image) {
                array_push($data['image_path'], convertImgUrl($image['bucket_name'], $image['object_key']));
            }
        }

        return $data;
    }

}

