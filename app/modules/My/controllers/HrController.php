<?php
/**
 * Author: Bruce
 * Date  : 2022-06-16 17:13
 * Description:
 */

namespace FlashExpress\bi\App\Modules\My\Controllers;

use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Modules\My\Server\StaffServer;


class HrController extends Controllers\ControllerBase
{
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    /**
     * 获取到岗确认列表
     * @param int    status             1=已入职, 2=待入职, 3=未入职
     * @param int    page_size          页面个数
     * @param int    page_num           页数
     * @return string
     */
    public function getEntryListAction()
    {
        //[1]参数定义
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['store_id'] = $this->userinfo['organization_id'];
        $logger              = $this->getDI()->get('logger');
        $method              = "getEntryList";
        $param               = [
            'worknode_id' => $paramIn['store_id'],
            'status'      => $paramIn['status'],
            'staff_id'    => $paramIn['staff_id'],
            'page_size'   => $paramIn['page_size'] ?? 30,
            'page_num'    => $paramIn['page_num'] ?? 1,
        ];

        //[2]验证
        $validations = [
            "status" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        if ($param['status'] == 2) {//待入职列表
            //[3]发送请求
            $logger->write_log("svc method {$method} param:" . json_encode($param), 'info');
            $fle_rpc = (new ApiClient("winhr_rpc", '', $method, $this->lang));
            $fle_rpc->setParams($param);
            $result = $fle_rpc->execute();
            if ($result['result']['code'] == 1) {
                if (!empty($result['result']['data']['dataList'])) {
                    $result['result']['data']['dataList'] = (new StaffServer($this->lang))->replace_entry_data($result['result']['data']['dataList']);
                }
                $this->jsonReturn($this->checkReturn([
                    'data' => $result['result']['data']
                ]));
            }
        }

        $result = (new StaffServer($this->lang))->getEntryList($param);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }
}