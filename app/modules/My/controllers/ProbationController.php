<?php
namespace FlashExpress\bi\App\Modules\My\Controllers;

use FlashExpress\bi\App\Controllers\ProbationController as BaseProbationController;
use FlashExpress\bi\App\Modules\My\Server\ProbationServer;
use Exception;

class ProbationController extends BaseProbationController
{
	
	/**
	 * @description:交转正评估评审
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/9/13 10:35
	 */
    public function auditAction() {

        $paramIn     = $this->paramIn;
        $validations = [
            "id"     => "Required|IntGe:0",
            'score'  => "Required",
            'remark' => "StrLenGeLe:0,255",
            'pic'    => "StrLenGeLe:0,255",
        ];
        $this->validateCheck($paramIn, $validations);

        $paramIn             = array_only($paramIn, array_keys($validations));
        $paramIn['audit_id'] = $this->userinfo['id'];
        //防止重复提交
        $cache_key = 'probation_controller_audit_' . $paramIn['id'];
        $data      = $this->atomicLock(function () use ( $paramIn ) {
            return (new ProbationServer($this->lang, $this->timezone))->probation_audit($paramIn['id'], $paramIn['audit_id'], $paramIn, 5, 5);
        }, $cache_key, 10, false);
        if ($data === false) { //没有获取到锁
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('ticket_repeat_msg')));
        }
        return $this->jsonReturn($data);

    }

    //重新发送
    public function resendAction(){
        $server = new ProbationServer($this->lang, $this->timezone);
        $paramIn     = $this->paramIn;
        $a = $server->msgPdfData($paramIn['staff_info_id']);
        var_dump($a);;
    }



}
