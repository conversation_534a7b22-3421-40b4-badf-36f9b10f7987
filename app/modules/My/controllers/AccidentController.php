<?php

namespace FlashExpress\bi\App\Modules\My\Controllers;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Server\BackyardServer;

class AccidentController extends BaseController
{
    /**
     * 马来调用bi新项目接口
     * @return void
     */
    public function accidentForBiAction()
    {
        $param             = $this->paramIn;
        $param['userInfo'] = $this->userinfo;

        $t = $this->getTranslation();

        $action = $param['action'];

        $ac = new ApiClient('ard_api', '', "accident.$action", $this->lang);
        $ac->setParams($param);
        $acResult = $ac->execute();
        if (isset($acResult['result'])) {
            //提交签字 置为已读
            if(($action == 'signSubmit' && !empty($param['msg_id'])) || ($action == 'getSignDetail' && !empty($acResult['result']['data']['sign_url']))) {
                (new BackyardServer($this->lang,$this->timezone))->has_read_operation($param['msg_id'],true);
            }

            $this->jsonReturn(self::checkReturn($acResult['result']));
        } else {
            $this->getDI()->get('logger')->write_log("事故上报BI-svc调用失败 参数:" . json_encode($param) . ";结果:" . json_encode($acResult));
            $this->jsonReturn(self::checkReturn(['code' => -1, 'msg' => $t->_('server_error')]));
        }
    }
}