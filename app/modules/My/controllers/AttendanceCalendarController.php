<?php
/**
 * Author: Bruce
 * Date  : 2022-06-16 18:02
 * Description:
 */

namespace FlashExpress\bi\App\Modules\My\Controllers;

use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Modules\My\Server\AttendanceCalendarV2Server;


class AttendanceCalendarController extends Controllers\ControllerBase
{
    /**
     * 月度统计
     */
    public function monthAction() {
        $paramIn = $this->paramIn;
        $paramIn['user'] = $this->userinfo;
        $paramIn             = $this->paramIn;
        $validations         = [
            "year"     => "Required|Int",
            "month"    => "Required|Int"
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        //[2]业务处理
        $paramIn['month']              = date('Y-m', strtotime($paramIn['year'] . '-' . $paramIn['month']));
        $server = new AttendanceCalendarV2Server($this->lang, $this->timezone);
        $result['attendance_list'] = $server->month($paramIn);
        $result['staff_info'] = $this->userinfo;
        $result['staff_info']['hire_type'] = $server->getHireType();
        $result['staff_info']['is_live_master'] = $server->getIsLive();
        return $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    /**
     * 每日明细
     */
    public function dailyAction() {
        $paramIn = $this->paramIn;
        $validations         = [
            "date"    => "Required|Date"
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['user'] = $this->userinfo;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $result = (new AttendanceCalendarV2Server($this->lang, $this->timezone))->daily($paramIn);

        return $this->jsonReturn($this->checkReturn(['data' => $result]));
    }
}