<?php
namespace FlashExpress\bi\App\Modules\My\Controllers;

use Exception;
use FlashExpress\bi\App\Controllers\TicketController as BaseTicketController;
use FlashExpress\bi\App\Modules\My\Server\TicketServer;


class TicketController extends BaseTicketController
{
    protected $paramIn;
    /**
     * @var \FlashExpress\bi\App\Server\TicketServer
     */
    protected $ticketServer;

    public function initialize()
    {
        parent::initialize();
        $this->paramIn     = $this->request->get();
        
        //会带个_url参数
        unset($this->paramIn['_url']);

        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        //$this->paramIn = array_filter($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 提交工单
     */
    public function addAction()
    {
        try {
            $paramIn   = $this->paramIn;

            if(empty($paramIn['line_id'])){
                unset($paramIn['line_id']);
            }

            $validations = [
                "item_type" => "Required|IntGe:0",
                "line_id"=>"StrLenGeLe:0,20",
                "mobile"=>"Required|StrLenGeLe:9,12",
                "info"=>"Required|StrLenGeLe:1,500",
                'pics'=>"StrLenGeLe:0,1000",
                //"device_store_id" => "Required|StrLen:10",    // 设备所在网点 10 位
            ];

            if (!empty($paramIn['anydesk_id'])) {
                $validations['anydesk_id'] = "StrLenGeLe:1,20";
            }

            if (!empty($paramIn['item_code'])) { // 资产编号
                $validations['item_code'] = "StrLenGeLe:1,20";
            }

            if (!empty($paramIn['device_store_id']) && $paramIn['device_store_id'] != '-1') {
                $validations['device_store_id'] = "Required|StrLen:10";
            }

            if (!empty($paramIn['device_store_id']) && $paramIn['device_store_id'] == '-1') {
                $validations['device_store_id'] = "Required|StrLen:2";
            }

            $this->validateCheck($paramIn, $validations);

            $paramIn = array_only($paramIn,array_keys($validations));

            $ticketServer = new TicketServer($this->lang, $this->timezone,$this->userinfo);
            $data = $ticketServer->add($paramIn);
            $this->jsonReturn($data);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("add 异常信息:" . $e->getMessage(), 'error');
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 获取工单列表
     */
    public function getCsListAction()
    {
        try {
            $paramIn   = $this->paramIn;
            $paramIn['status'] = $paramIn['status'] ?? 1;
            $validations = [
                "status" => "Required|IntIn:1,3,4",
                'page_num'=>"IntGe:1",
                'page_size'=>"IntGe:1"
            ];
            $this->validateCheck($paramIn, $validations);

            $ticketServer = new TicketServer($this->lang, $this->timezone,$this->userinfo);
            if(!$ticketServer->checkPermission()) throw new Exception ('Do not have PERMISSION');
            $list = $ticketServer->getCsList($paramIn);
            $this->jsonReturn($list);
        } catch(Exception $e){
            $this->getDI()->get('logger')->write_log("getCsLis 异常信息:" . $e->getMessage(), 'info');
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }

    /**
     * 获取工单详情
     */
    public function getCsReplyAction()
    {
        try{
            $paramIn   = $this->paramIn;
            $validations = [
                "id" => "Required|IntGe:1",
            ];
            $this->validateCheck($paramIn, $validations);

            $ticketServer = new TicketServer($this->lang, $this->timezone,$this->userinfo);
            $data = $ticketServer->getCsReply($paramIn['id']);
            $this->jsonReturn($data);
        }catch(Exception $e){
            $this->logger->write_log("getCsListReply 异常信息:" . $e->getMessage(), 'info');
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }
}
