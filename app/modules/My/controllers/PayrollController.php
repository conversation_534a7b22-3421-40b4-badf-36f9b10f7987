<?php

namespace FlashExpress\bi\App\Modules\My\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\EaFormReportModel;
use FlashExpress\bi\App\Server\PayrollServer;
use FlashExpress\bi\App\Server\PersoninfoServer;

class PayrollController extends Controllers\PayrollController
{
    /**
     * my  有 旧工资条  新工资条包含( pcb ea form en my 两种语言， 新工资条)
     */
    public function loginAction()
    {
        try{

            $data = $this->paramIn;
            //Ea from 单独处理 不需要校验密码了
            if (isset($data['ea_form_token']) &&  $params = json_decode(base64_decode($data['ea_form_token']),true)) {
                $data = array_merge($params,$data);
                $data['user'] = $data['staff_info_id'];
            }
            if (!isset($data['ea_form_token'])) {
                if (!isset($data['user']) || !isset($data['pwd'])) {
                    $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('login_all')));
                }
                $_data['account'] = $data['user'];
                $_data['pwd'] = $data['pwd'];
                $return_data = (new PayrollServer())->verify_fle($_data);
                if (empty($return_data)) {
                    $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('login_all')));
                }
            }


            //获取invoice 文件
            if(!empty($data['source']) && $data['source'] == 'invoice') {
                $where['staff_id'] = $_data['account'];
                $where['start_date'] = $data['start_date'];
                $where['period_id'] = $data['period_id'] ?? '';
                $returnArr = (new PersoninfoServer($this->lang,$this->timezone))->getInvoiceInfoFromFbi($where);
                $res['url'] = !empty($returnArr['url']) ? 'https://vpdf.flashexpress.my/web/viewer.html?file=' . $returnArr['url'] : '';
                $this->jsonReturn(self::checkReturn(['data' => $res]));
            }
            //type 不存在则是旧工资条
            if (!isset($data['type'])) {
                if (!isset($data['date'])) {
                    $data['date'] =  date('Y-m',strtotime(date('Y-m') . '-1 month'));
                }
                $hcm_rpc = new ApiClient('hcm_rpc', '', 'get_payroll_pdf', 'en'); //MY强制使用en
                $hcm_rpc->setParams(
                    [
                        "staff_id" => $data['user'],
                        "month" => $data["date"],
                    ]
                );
                $return = $hcm_rpc->execute();
                if(isset($return['result']['code']) && $return['result']['code'] == 1) {
                    $this->jsonReturn(self::checkReturn(["data" => ["url" => $return['result']['data'] ?? '']]));
                } else {
                    $this->jsonReturn($this->checkReturn(-3, $return['result']['msg'] ?? 'Not found'));
                    $this->logger->write_log("get_pdfAction:get_payroll_pdf:" . json_encode($return ,true));
                }
            }
            //有type 为新工资条
            if (isset($data['type'])) {
                // 1 pcb 2 ea form 4 new 工资条
                if (isset($data['type']) && !in_array($data['type'],[1,2,4])) {
                    $this->jsonReturn($this->checkReturn(-2, 'parameter is invalid'));
                }
                if (in_array($data['type'],[1,2]) && !$data['year']) {
                    $this->jsonReturn($this->checkReturn(-2, 'parameter is invalid'));
                }
                if ($data['type'] == 4
                    && (
                        empty($data['month'])
                        || empty($data['run_type'])
                        || empty($data['cycle_start'])
                        || empty($data['cycle_end'])
                    )
                ) {
                    $this->jsonReturn($this->checkReturn(-2, 'parameter is invalid'));
                }
                $type = $data['type'] ?? 0;
                $year = $data['year'] ?? '';
                $month = $data['month'] ?? '';
                $run_type = $data['run_type'] ?? '';
                $cycle_start = $data['cycle_start'] ?? '';
                $cycle_end = $data['cycle_end'] ?? '';
                $staff_info_id = $data['user'];
                $lang_type = $data['lang_type'] ?? EaFormReportModel::LANG_EN; //1 英语文件 2 马来语文件
                $company_id = $data['company_id'] ?? 0;
                $hcm_rpc = new ApiClient('hcm_rpc', '', 'get_payroll_report',$this->lang);
                if ($type != 4) {
                    $setParams = [
                        'type'       => $type,
                        'staff_id'   => $staff_info_id,
                        'year'       => $year,
                        'lang_type'  => $lang_type,  //ea form 使用的参数
                        'company_id' => $company_id, //ea form 使用的参数
                    ];
                } else {
                    $setParams = [
                        'type'        => $type,
                        'run_type'    => $run_type,
                        'staff_id'    => $staff_info_id,
                        'month'       => $month,
                        'cycle_start' => $cycle_start,
                        'cycle_end'   => $cycle_end,
                        'company_id'  => $company_id,
                    ];
                }
                $hcm_rpc->setParams(
                    $setParams
                );
                $return = $hcm_rpc->execute();
                if(isset($return['result']['code']) && $return['result']['code'] == 1) {
                    $this->jsonReturn(self::checkReturn(["data" => ["url" => $return['result']['data'] ?? '']]));
                } else {
                    $this->jsonReturn($this->checkReturn(-3, $return['result']['msg'] ?? 'Not found'));
                    $this->logger->write_log("get_pdfAction:get_payroll_pdf:" . json_encode($return ,true));
                }

            }
            $this->jsonReturn($this->checkReturn(-3, 'Not Found data!'));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("getRoleList:异常信息" . $e->getMessage());
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }
}