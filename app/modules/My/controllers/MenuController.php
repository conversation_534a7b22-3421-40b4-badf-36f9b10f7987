<?php
namespace FlashExpress\bi\App\Modules\My\Controllers;

use Exception;
use FlashExpress\bi\App\Modules\My\Server\WmsPlanServer;
use FlashExpress\bi\App\Server\BackyardServer;
use FlashExpress\bi\App\Controllers\MenuController as BaseMenuController;
class MenuController extends BaseMenuController
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        //会带个_url参数
        unset($this->paramIn['_url']);
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }


    /**
     * by index
     * @api https://yapi.flashexpress.pub/project/93/interface/api/25806
     */
    public function indexAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['userinfo'] = $this->userinfo;
        $validations         = [
            "staff_id" => "Required|Int"
        ];
        $this->validateCheck($paramIn, $validations);

        //库存盘点数量
        $plan_wms_task_count =  (new WmsPlanServer($this->lang,$this->timezone))->setExpire(300)->planWmsTaskCountMsgFromCache($this->userinfo['staff_id']);

        //[2]业务处理ge
        $server = new BackyardServer($this->lang, $this->timezone);
        $waitAuditRes = $server->getWaitAuditData($paramIn);
        $return = [
            'total_audit_num'              => $waitAuditRes['un_audit_num'],
            'ProbationGetNum'              => $waitAuditRes['probation_data'],
            'waitAuditNum'                 => $waitAuditRes['wait_audit_data'],
            'TicketGetNum'                 => $waitAuditRes['ticket_data'],
            'interview_count'              => $waitAuditRes['interview_data'],
            'resume_filter'                => $waitAuditRes['resume_filter_data'],
            'resumeRecommendNum'           => $waitAuditRes['commitment_data'],
            'InventoryCheckTaskCount'      => ['num' => $waitAuditRes['inventory_check_task_count']],
            'planWmsTaskCount'             => ['num' => $plan_wms_task_count ?? 0],
            'kpiCount'                     => ['num' => $waitAuditRes['kpi_count']],//kpi目标
            'jobTransferConfirmNum'        => $waitAuditRes['transfer_data'],
            'oaFolderNum'                  => $waitAuditRes['oa_folder_num'] ?? 0,
            'probationTargetNum'           => ['num' => $waitAuditRes['probation_target_num']],  //试用期目标
            'vehicle_company_info'         => $waitAuditRes['vehicle_company_info'],
            'vehicleDeliveryAndInspection' => ['num' => $waitAuditRes['vehicle_delivery_and_inspection']],//交车&验车
            'reimbursementApplyNum'        => $waitAuditRes['reimbursement_apply_num'] ?? 0,
            'packageAllotNum'              => ['num' => $waitAuditRes['package_allot_num']],
        ];
        $this->getDI()->get('logger')->write_log("menu_index_{$this->userinfo['staff_id']}: ".json_encode($return,JSON_UNESCAPED_UNICODE),'info');
        return $this->jsonReturn(self::checkReturn(['data' =>$return]));
    }

}
