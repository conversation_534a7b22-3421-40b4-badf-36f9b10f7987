<?php
/**
 * Created by PhpStorm.
 * User: z<PERSON><PERSON>
 * Date: 2021/12/1
 * Time: 14:32
 */

namespace FlashExpress\bi\App\Modules\My\Controllers;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrStaffTp3Model;
use FlashExpress\bi\App\Modules\My\Server\HrStaffTp3Server;
use Exception;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\SettingEnvServer;

class Tp3Controller extends Controllers\ControllerBase
{
    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    /**
     * 前端-消息详情驳回充填原因获取
     */
    public function getRejectReasonAction()
    {
        $tp3_info = (new HrStaffTp3Server($this->lang, $this->timezone))->getTp3InfoByStaffId($this->userinfo['id']);
        $reject_reason = '';
        if($tp3_info && $tp3_info['state'] == 3){
            //当当前状态为待重填（已打回）时，返回打回原因
            $reject_reason = $tp3_info['reject_reason'];
        }
        //[3]数据返回
        $return_arr = ['code'=>1,'data'=>['reject_reason'=>$reject_reason]];
        $this->jsonReturn(self::checkReturn($return_arr));
    }

    /**
     * 客户端/前端-获取tp3状态信息接口
     */
    public function getStateInfoAction(){

        $tp3Instance = (new HrStaffTp3Server($this->lang, $this->timezone));
        $tp3_info = [];
        //判断当tp3状态是否符是待提交的状态
        $version = (new SettingEnvServer())->getSetVal('current_tp3_version');
        $tp3_state_can_submit = $tp3Instance->_validateStateIsCanSubmit($this->userinfo['id'],$tp3_info);
        if (!empty($tp3_info['tp3_version'])  && $tp3_info['state'] != HrStaffTp3Model::STATE_ALREADY_SUBMIT) {
            $tp3_info['tp3_version'] = $version;
        }
        //[3]数据返回
        //is_show:是否显示状态文案的标识
        $data = ['is_show'=>0,'state_text'=>'','is_show_tp3'=>$tp3_info ? 1: 0,'tp3_version'=>$tp3_info['tp3_version']??$version];

        if($tp3_state_can_submit){
            //当tp3状态为需要填写时 根据当前状态显示状态文案
            $data['is_show'] =1;
            $data['state_text'] = $this->getTranslation()->_('hr_staff_tp3_'.$tp3_info['state']);
        }
        //员工入职日期所在年份和点击时所属年份≠同一年份
        $info  = (new StaffRepository())->getStaffInfoOne($this->userinfo['id'],['hire_date']);
        $data['is_same_year'] = date('Y') == date("Y",strtotime($info['hire_date']));
        $this->jsonReturn(self::checkReturn(['code'=>1,'data'=>$data]));

    }

    /**
     * 获取配置信息
     */
    public function configInfoAction(){
        $config_data = (new HrStaffTp3Server($this->lang, $this->timezone))->getConfigInfo($this->userinfo['id']);

        $this->jsonReturn(self::checkReturn(['code'=>1,'data'=>$config_data]));
    }

    /**
     * tp3详情接口
     */
    public function getDetailAction(){

        $service =  (new HrStaffTp3Server($this->lang, $this->timezone));
        $tp3_info = $service->getTp3InfoByStaffId($this->userinfo['id']);
        $tp3_detail = $tp3_info['tp3_detail'] ?? null;
        $version = (new SettingEnvServer())->getSetVal('current_tp3_version');
        if (!empty($tp3_info['tp3_version']) &&  $tp3_info['state'] != HrStaffTp3Model::STATE_ALREADY_SUBMIT) {
            $tp3_info['tp3_version'] = $version;
        }
        $return_arr = ['code'=>1,'data'=>['tp3_detail'=>$tp3_detail,'tp3_state'=>$tp3_info['state'],'tp3_version'=>$tp3_info['tp3_version']]];
        $this->jsonReturn(self::checkReturn($return_arr));
    }

    /**
     * tp3信息提交提交
     * @return void
     * @throws BusinessException
     */
    public function submitDetailAction(){
        $validations = [
            "tp3_detail" => "Required",
            "step"       => "Required|IntIn:1,2,3,4",
        ];
        //验证传参
        $this->validateCheck($this->paramIn, $validations);

        $tp3Instance = (new HrStaffTp3Server($this->lang, $this->timezone));
        //无底薪员工&兼职个人代理不可提交
        $check = $tp3Instance->checkStaffHireType($this->userinfo['id']);
        if ($check) {
            throw new BusinessException($this->getTranslation()->_('tp3_not_base_salary_notice'));
        }
        $tp3Info = [];
        //验证tp3状态是否符合要求（已提交的不可在提交）
        $can_submit = $tp3Instance->_validateStateIsCanSubmit($this->userinfo['id'],$tp3Info);
        if(!$can_submit){
            throw new BusinessException("工号:{$this->userinfo['id']}的员工tp3数据异常：tp3已提交不可再次提交");

        }
        //处理提交模块信息
        switch ($this->paramIn['step']){
            case 1:
                $res =  $tp3Instance->createABInfo($this->paramIn,$this->userinfo);
                break;
            case 2:
                $res =  $tp3Instance->createCInfo($this->paramIn,$this->userinfo);
                break;
            case 3:
                $res =  $tp3Instance->createDInfo($this->paramIn,$this->userinfo);
                break;
            case 4:
                $res =  $tp3Instance->createEInfo($this->paramIn,$this->userinfo);
                break;
            default:
                $res = true;
                break;
        }

        $this->jsonReturn(self::checkReturn($res ? 1 : -3 ));
    }
}