<?php
namespace FlashExpress\bi\App\Modules\My\Controllers;


use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\EaFormReportModel;
use FlashExpress\bi\App\Modules\My\library\Enums\CommonEnums;
use FlashExpress\bi\App\Controllers\PersoninfoController as BasePersonInfoController;
use FlashExpress\bi\App\Modules\My\Server\PersoninfoServer;
use Exception;


class PersoninfoController extends BasePersonInfoController
{
    /**
     * 常量信息
     */
    public function enumAction()
    {
        //[1]业务处理
        $returnArr = CommonEnums::$bank_type;
        $data = array();
        foreach ($returnArr as $key=>$value){
            $item['label']=$value;
            $item['value']=$key;
            array_push($data,$item);
        }
        //[2]数据返回
        $this->jsonReturn(array('code' => 1,'message' => '','data' => $data));
    }

    /**
     * ai 身份证照片认证
     */
    public function ai_id_card_auditAction()
    {
        try {
            //[1]入参
            $paramIn = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];

            $model = new PersoninfoServer($this->lang,$this->timezone);
            $return = $model->ai_id_card_audit($paramIn);

            return $this->jsonReturn($return);
        } catch (\Exception $e) {
            $this->wLog("ai_id_card_auditAction:", $e->getMessage(), 'personinfo');
            return $this->jsonReturn(self::checkReturn(-3,'server error'));
        }
    }
    /**
     * ai 身份证审核提交
     */
    public function ai_id_card_submitAction()
    {
        try {
            //[1]入参
            $paramIn = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $redis_lock_key  = "lock_ai_id_card_submit_" . $paramIn['staff_id']; //锁 key
            $return = $this->atomicLock(function () use ($paramIn) {
                return  (new PersoninfoServer($this->lang,$this->timezone))->ai_id_card_submit($paramIn);
            }, $redis_lock_key, 10);
            if ($return === false) { //没有获取到锁
                return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('ticket_repeat_msg')));
            }
            return $this->jsonReturn($return);
        } catch (\Exception $e) {
            $this->wLog("ai_id_card_submitAction:", $e->getMessage(), 'personinfo');
            return $this->jsonReturn(self::checkReturn(-3,'server error'));
        }
    }

    public function save_epf_noAction(){
        $staffId = $this->userinfo['staff_id'];
        $paramIn = $this->paramIn;
        if (!empty($paramIn['epf_no']) && !preg_match('/^\d{8}$/', $paramIn['epf_no'])) {
            throw new ValidationException('Must be an 8-digit number') ;
        }
        $epfNo = $paramIn['epf_no'];
        $return = $this->atomicLock(function () use ($staffId,$epfNo) {
            return  (new PersoninfoServer($this->lang,$this->timezone))->save_epf_no($staffId,$epfNo);
        }, 'save_epf_no_'.$staffId, 10);
        if ($return === false) {
           $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('ticket_repeat_msg')));
        }
       $this->jsonReturn($this->checkReturn(1));
    }

    /**
     * 查看 EA form 文件
     */
    public function show_ea_formAction()
    {
        $staffId = $this->userinfo['staff_id'];

        $find = EaFormReportModel::find([
            'columns'    => 'year,url',
            'conditions' => 'staff_info_id=:staff_info_id: and lang=:lang: and is_deleted=0',
            'bind'       => [
                'staff_info_id'=>$staffId,
                'lang' => EaFormReportModel::LANG_MY,
            ],
            'order'      => 'year desc',
        ])->toArray();
        if (empty($find)) {
            $this->jsonReturn(self::checkReturn(['data' => []]));
        }
        $returnData = [];
        if ($find) {
            $returnData['year'] = current($find)['year'];
            foreach ($find as $item) {
                if ($returnData['year'] == $item['year']) {
                    $returnData['urls'][] = $item['url'];
                }
            }
        }
        $this->jsonReturn(self::checkReturn(['data' => $returnData]));
    }

    /**
     * 获取 付款凭证,代扣税 Invoice 周期枚举
     *
     *
     * @throws ValidationException
     */
    public function getPeriodAction()
    {
        //[1]入参
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $returnArr = (new PersoninfoServer($this->lang, $this->timezone))->getPeriodFromFbi($paramIn);

        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }
}
