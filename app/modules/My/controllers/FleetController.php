<?php


namespace FlashExpress\bi\App\Modules\My\Controllers;

use FlashExpress\bi\App\Controllers\FleetController as GlobalController;
use FlashExpress\bi\App\Modules\My\Server\FleetServer;

class FleetController extends GlobalController
{
    /**
     * 获取车辆类型
     * 获取加班车申请类型
     * 获取虚拟网点，真实网点枚举
     */
    public function getCarTypeAction()
    {
        //[1]入参校验
        $paramIn = $this->paramIn;

        //[2]业务处理
        $server = new FleetServer($this->lang, $this->timezone);
        $carTypeList    = $server->getCarTypeListV2($paramIn);
        $fleetTypeList  = $server->getFleetAuditType($paramIn);
        $storeTypeList  = $server->getStoreType($paramIn);
        $reasonTypeList = $server->getReasonType();

        $paramIn['organization_id']   = $this->userinfo['organization_id'];
        $paramIn['organization_type'] = $this->userinfo['organization_type'];
        [$isSpBdcType,$staffStoreCategory]      = $server->isSpBdcTypeAndStoreCategory($paramIn);

        //[3]数据返回
        $this->jsonReturn($this->checkReturn([
            'data' => [
                'dataList' => [
                    'car_type'             => $carTypeList,
                    'fleet_type'           => $fleetTypeList,
                    'store_type'           => $storeTypeList,
                    'is_sp_bdc_type'       => $isSpBdcType,//废弃
                    'staff_store_category' => $staffStoreCategory,
                    'reason_type'          => $reasonTypeList,
                ],
            ],
        ]));
    }
}