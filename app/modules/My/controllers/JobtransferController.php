<?php
namespace FlashExpress\bi\App\Modules\My\Controllers;

use FlashExpress\bi\App\Controllers\JobtransferController as BaseJobTransferController;
use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\Enums\JobTransferEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Modules\My\Server\JobTransferV2Server;
use FlashExpress\bi\App\Server\ApprovalServer;
use FlashExpress\bi\App\Server\StaffServer;

class JobtransferController extends BaseJobTransferController
{
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * @description 转岗验证审批人是否满足条件
     */
    public function checkTransferAction()
    {
        //[1]获取参数
        $paramIn                  = $this->paramIn;
        $paramIn['staff_info_id'] = $this->userinfo['id'] ?? '';

        //[2]校验
        $validations = [
            'staff_id' => 'Required|Int|>>>:' . $this->getTranslation()->_('please_entry_staff_no'),
        ];
        $this->validateCheck($this->paramIn, $validations);

        //校验被转岗人
        $data = (new JobTransferV2Server($this->lang, $this->timezone))->checkTransfer($paramIn);

        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

    /**
     * @description 提交转岗申请
     */
    public function addJobtransferAction()
    {
        $paramIn                 = $this->paramIn;
        $paramIn['submitter_id'] = $this->userinfo['staff_id'] ?? 0;

        //可选转岗日期为当日后1天-90天内的任意日期
        $startDate = date('Y-m-d', time() + 1 * 86400);
        //校验参数
        $validations = [
            "staff_id"              => "Required|Int",
            "after_date"            => "Required|DateFrom:{$startDate}",
            "job_handover_staff_id" => "Required|Int",
            "reason_type"           => "Required|IntIn:1,2,3,4,5,6,7,8,99",
            "reason"                => "IfIntEq:reason_type,99|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('job_transfer.reason_remark'),
        ];
        $this->validateCheck($paramIn, $validations);

        //是否一线职位
        $server = new JobTransferV2Server($this->lang, $this->timezone);
        $staffInfo = (new StaffServer())->getStaffInfoById($paramIn['staff_id']);
        $transferType = $server->getTransferType($staffInfo['node_department_id'], $staffInfo['job_title']);
        if ($transferType == JobTransferEnums::JOB_TRANSFER_TYPE_FRONT_LINE) {
            $validations = [
                "after_department_id"   => "Required|Int",
                "after_store_id"        => "Required|Str",
                "after_position_id"     => "Required|Int",
            ];
            $this->validateCheck($paramIn, $validations);
        }

        //[2]业务处理
        $return = $server->setExpire(10, true)->addJobtransferUseLock($paramIn);

        $this->jsonReturn($return);
    }

    /**
     * @description 转岗审批
     * @throws ValidationException
     * @throws \Exception
     */
    public function updateJobtransferAction()
    {
        //[1]必填入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $validations = [
            "audit_id"      => "Required|Int",
            "status"        => "Required|Int",
            "reject_reason" => "Required|StrLenGeLe:0,500",
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $server         = $this->class_factory('JobTransferV2Server', $this->lang, $this->timezone);
        $approvalServer = new ApprovalServer($this->lang, $this->timezone);
        if ($paramIn['status'] == enums::APPROVAL_STATUS_APPROVAL &&
            $approvalServer->isExistCanEditField($paramIn['audit_id'], AuditListEnums::APPROVAL_TYPE_JT)) {

            //开始校验提交字段
            $bpValidations = $server->getBpValidate($paramIn);
            $this->validateCheck($paramIn, array_merge($validations, $bpValidations));

            //提交审核
            $returnArr = $server->auditTransferUseLock($paramIn);
        } else {
            $this->validateCheck($paramIn, $validations);

            //提交审批
            $returnArr = $server->approvalTransferUseLock($paramIn);
        }

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 转岗获取网点下拉列表
     */
    public function getStoreListAction()
    {
        //[1]传入参数
        $paramIn        = $this->paramIn;
        $server         = $this->class_factory('JobTransferV2Server', $this->lang, $this->timezone);

        //[2]校验参数
        $validations = [
            'department_id' => 'Required|Int',
        ];
        $this->validateCheck($paramIn, $validations);

        //获取网点下拉列表
        $data = $server->getStoreList($paramIn);

        $this->jsonReturn(self::checkReturn(['data' => $data]));
    }

    /**
     * 转岗-部门下拉
     */
    public function getDepartmentListAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $data                = (new JobTransferV2Server($this->lang, $this->timezone))->getMyDepartmentTree($paramIn);
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

    /**
     * 获取下拉项
     * @throws ValidationException
     */
    public function getEditListAction()
    {
        $paramIn = $this->paramIn;
        //校验参数
        $validations = [
            'audit_id'      => 'Required|Int',
            'department_id' => 'Required|Int',
            'job_title_id'  => 'Required|Int',
        ];
        $this->validateCheck($paramIn, $validations);

        $data = (new JobTransferV2Server($this->lang, $this->timezone))->getEditList($paramIn);
        $this->jsonReturn($data);
    }

    /**
     * 转岗获取hc下拉列表
     */
    public function getHcListAction()
    {
        $paramIn = $this->paramIn;

        //获取hc列表
        $data = (new JobTransferV2Server($this->lang, $this->timezone))->getHcListV2($paramIn);
        $this->jsonReturn(self::checkReturn($data));
    }

    /**
     * 搜索员工
     */
    public function searchStaffAction()
    {
        $paramIn = $this->paramIn;
        $data    = (new JobTransferV2Server($this->lang, $this->timezone))->searchStaff($paramIn);
        $this->jsonReturn(self::checkReturn(['data' => $data]));
    }

    /**
     * @description 转岗原因
     */
    public function getJobTransferReasonAction()
    {
        //[1]业务处理
        $paramIn = $this->paramIn;

        //校验参数
        $validations = [
            'staff_id' => 'Required|Int',
        ];
        $this->validateCheck($paramIn, $validations);

        $data = (new JobTransferV2Server($this->lang, $this->timezone))->getTransferReason($paramIn);

        //[2]数据返回
        $this->jsonReturn(self::checkReturn(['data' => $data]));
    }

    /**
     * 获取车类型
     * @return void
     */
    public function getCarTypeAction()
    {
        $paramIn = $this->paramIn;
        //校验参数
        $validations = [
            'job_title_id'  => 'Required|Int',
        ];
        $this->validateCheck($paramIn, $validations);

        $result = JobTransferV2Server::getCarTypeByJobTitle($paramIn['job_title_id']);
        $this->jsonReturn(self::checkReturn(['data' => $result]));
    }
}