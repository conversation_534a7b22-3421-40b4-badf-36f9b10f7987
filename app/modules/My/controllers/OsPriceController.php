<?php

namespace FlashExpress\bi\App\Modules\My\Controllers;


use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Modules\My\Server\OsStaffServer;
use FlashExpress\bi\App\Server\OsPriceServer;
use Exception;


class OsPriceController extends BaseController
{
    protected $os;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 除了网点 其他枚举接口
     */
    public function get_enumAction(){
        $data['store_category'] = OsPriceServer::$store_category;
        $data['job_title'] = (new OsPriceServer())->get_enum();
        return $this->jsonReturn(self::checkReturn(array('data' => $data)));
    }
}