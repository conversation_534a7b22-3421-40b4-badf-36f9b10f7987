<?php
/**
 * Author: Bruce
 * Date  : 2022-06-16 18:02
 * Description:
 */

namespace FlashExpress\bi\App\Modules\My\Controllers;

use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Modules\My\Server\StaffServer;


class StaffController extends Controllers\ControllerBase
{
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    /**
     * 已入职员工DC officer, bike courier, van courier,boat courier 未有辅导员总数
     */
    public function getCourierInstructorCountAction() {
        try {
            $paramIn = $this->paramIn;
            $paramIn['user'] = $this->userinfo;
            $paramIn['store_id'] = $this->userinfo['organization_id'];
            $count = (new StaffServer($this->lang, $this->timezone))->getStaffJobTitleInstructorCount($paramIn);
            $result = ['count' => $count];
            return $this->jsonReturn($this->checkReturn(['data' => $result]));
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("StaffController:getCourierInstructorCountAction:" . $e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }
}