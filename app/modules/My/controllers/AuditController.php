<?php
namespace FlashExpress\bi\App\Modules\My\Controllers;


use Exception;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Modules\My\Server\AttendanceCalendarServer;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Modules\My\Server\AuditServer;
use FlashExpress\bi\App\Modules\My\Server\LeaveServer;
use FlashExpress\bi\App\Modules\My\Server\Vacation\MilitaryServer;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Controllers\AuditController as BaseAuditController;
use FlashExpress\bi\App\Server\BackyardServer;

class AuditController extends BaseAuditController
{

    /**
     * 获取审批列表权限
     */
    public function getAuditlistPermissionAction()
    {
        //[1]入参校验
        $paramIn                      = $this->paramIn;
        $paramIn['staff_id']          = $this->userinfo['staff_id'];
        $paramIn['positions']         = $this->userinfo['positions'];
        $paramIn['job_title']         = $this->userinfo['job_title'];
        $paramIn['organization_id']   = $this->userinfo['organization_id'];
        $paramIn['organization_type'] = $this->userinfo['organization_type'];
        $paramIn['department_id']     = $this->userinfo['department_id'];

        $validations = [
            "staff_id" => "Required|Int"
        ];
        $this->validateCheck($paramIn, $validations);

        $staffRe = new StaffRepository($this->lang);
        $paramIn['staffInfo'] = $staffRe->getStaffPosition($paramIn['staff_id']);
        $paramIn['staffInfo']['positions'] = $this->userinfo['positions'];
        //[2]业务处理
        $audit_server = (new AuditServer($this->lang, $this->timezone));
        $audit_server->setStaffInfo($paramIn['staffInfo']);
        $returnArr = $audit_server->getListPermissionFromCache($paramIn);
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 请假添加
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function leaveAddAction()
    {
        try {
            //[1]入参校验
            $paramIn             = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $validations         = [
                "staff_id"         => "Required|Int",
                "leave_type"       => "Required|Int",
                "leave_start_time" => "Required|Date|>>>:" . $this->getTranslation()->_('1023'),
                "leave_end_time"   => "Required|Date|>>>:" . $this->getTranslation()->_('1024'),
                'leave_start_type' => "IntIn:1,2",
                'leave_end_type'   => "IntIn:1,2",
                "audit_reason"     => "Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('1019')
            ];
            $this->validateCheck($paramIn, $validations);

            // [2.5] 校验是否有未完成的必填问卷消息 和 签字消息:
            // 若无则继续原业务逻辑；若有则组织提交请假，返回提示：您有未完成的问卷消息/签字消息，请完成后再请假。
            (new BackyardServer($this->lang, $this->timezone))->leaveCheckMsg($paramIn['staff_id']);


            //[2]业务处理
            $auditServer = new AuditServer($this->lang, $this->timezone);
            $newVersionType = $auditServer->newVersionType;
            if(!empty($newVersionType) && in_array($paramIn['leave_type'],$newVersionType)){
                $returnArr = (new LeaveServer($this->lang, $this->timezone))->saveVacationUseLock($paramIn);
            }else{
                $returnArr =  $auditServer->leaveAddUseLock($paramIn);
            }
            $this->jsonReturn($returnArr);
        } catch (ValidationException $e) {
            //请假时间跟班次判断二次确认弹窗
            if ($e->getCode() == 10086) {
                return $this->returnJson(1, $e->getMessage(), ['code' => -1,'message' => $e->getMessage(),'data' => ['param' => 'is_submit']]);//二次确认
            }
            throw $e;
        }
    }

//    /**
//     * 获取 补卡/请假类型 只用在 补卡 type 1类型
//     * @Access  public
//     * @Param   request
//     * @Return  jsonData
//     */
//    public function getTypeBookAction()
//    {
//        //[1]入参 参数校验
//        $paramIn     = $this->paramIn;
//        $validations = [
//            "type" => "Required|Int",
//        ];
//        $this->validateCheck($paramIn, $validations);
//        $paramIn['user_info'] = $this->userinfo;
//
//        //[2]业务处理
//        $returnArr = (new AuditServer($this->lang, $this->timezone))->getTypeBook($paramIn);
//
//        //[3]数据返回
//        $this->jsonReturn($returnArr);
//    }



    /**
     * 换算时间
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function conversionTimeAction()
    {
        try{
            //[1]入参校验
            $paramIn     = $this->paramIn;
            $validations = [
                "leave_start_time" => "Required|Date",
                'leave_start_type' => "Required|IntIn:1,2",
                'leave_end_type'   => "Required|IntIn:1,2",
                "leave_end_time"   => "Required|Date",
            ];
            $this->validateCheck($paramIn, $validations);
            $paramIn['staff_id'] = $this->userinfo['id'];

            //[2]业务处理
            $returnArr = (new AuditServer($this->lang, $this->timezone))->conversionTime($paramIn);

            //[3]数据返回
            $this->jsonReturn($returnArr);
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log('audit conversionTime' . $e->getMessage(), 'error');
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }
    }


    //调休假 详情 显示 每个人 的调休天数 和每天对应的失效日期
    public function lieuLeaveDetailAction(){
        try{
            $param = $this->paramIn;
            $param['user_info'] = $this->userinfo;
            $server = new AuditServer($this->lang,$this->timezone);
            $data = $server->lieuDetail($param);
            return $this->jsonReturn(self::checkReturn(array('data' => $data)));


        }catch (ValidationException $v){
            return $this->jsonReturn(self::checkReturn(-3, $v->getMessage()));
        }catch (\Exception $e){

            $this->logger->write_log("audit_lieu_detail {$this->userinfo['id']} " .json_encode($param), 'info');
            return $this->jsonReturn(self::checkReturn(-3, "server error"));
        }

    }


    /**
     * 补卡添加
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function reissueCardAddAction()
    {
        try { //[1]入参校验
            $paramIn             = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $validations         = [
                "staff_id"          => "Required|Int",
                "reissue_card_date" => "Required|DateTime",
                "attendance_type"   => "Required|IntIn:1,2,3,4",//1 第一次 上班 2 第一次 下班 3 第二次上 4 第二次下
                "audit_reason"      => "Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('1019'),
                'day_type'          => "Required|Int",//type 1 - 当天 2 - 次日
            ];
            $this->validateCheck($paramIn, $validations);


            //[2]业务处理
            $returnArr = (new AuditServer($this->lang, $this->timezone))->reissueCardAddUseLock($paramIn, $this->userinfo);

            //[3]数据返回
            $this->jsonReturn($returnArr);
        } catch (ValidationException $e) {
            //无需补卡提醒
            if ($e->getCode() == 10086) {
                return $this->returnJson(1, $e->getMessage(), ['code' => -1,'message' => $e->getMessage(),'data' => ['param' => 'is_submit']]);//二次确认
            }
            //时长不足提示
            if ($e->getCode() == 10088) {
                return $this->returnJson(1, $e->getMessage(), ['code' => -1,'message' => $e->getMessage(),'data' => ['param' => 'confirm_10088']]);//二次确认
            }
            throw $e;
        }




    }


    //根据 日期 判断 下拉菜单 显示 上午下午 还是 前半天后半天
    public function leaveBookByDateAction(){
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "staff_id"          => "Required|Int",
            "leave_date" => "Required|Date",
        ];
        $this->validateCheck($paramIn, $validations);


        //[2]业务处理
        $returnArr = (new AuditServer($this->lang, $this->timezone))->getLeaveDayType($paramIn, $this->userinfo);

        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }


    /**
     * 补卡获取打卡和班次信息
     * @return void
     * @throws \ReflectionException
     */
    public function attendanceShiftInfoAction()
    {
        $paramIn = $this->paramIn;
        $res     = (new AuditServer($this->lang, $this->timezone))->attendanceShiftInfo($this->userinfo, $paramIn);
        $this->jsonReturn($this->checkReturn(['data' => $res]));
    }

    //马来 国民假 获取时间区间接口
    public function getMilitaryDateAction(){
        $server = new MilitaryServer($this->lang, $this->timezone);
        $data = $server->getSettingDate();
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }



}
