<?php
/**
 * Created by PhpStorm.
 * User: z<PERSON><PERSON>
 * Date: 2021/12/1
 * Time: 14:32
 */

namespace FlashExpress\bi\App\Modules\My\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrStaffTp1Model;
use FlashExpress\bi\App\Modules\My\Server\HrStaffTp1Server;
use Exception;
use FlashExpress\bi\App\Server\SettingEnvServer;

class Tp1Controller extends Controllers\ControllerBase
{
    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    /**
     *客户端-是否显示tp1入口
     */
    public function isShowAction()
    {
        $is_show_tp1 = 0;//是否显示tp1入口：1-显示，0-不显示

        $tp1_staff_ids = (new SettingEnvServer())->getSetVal('my_tp1_staffids');
        if ($tp1_staff_ids && in_array($this->userinfo['id'], explode(',', $tp1_staff_ids))) {
            $is_show_tp1 = 1;
        }
        $tp1_version = HrStaffTp1Model::TP1_VERSION_V4;

        //[3]数据返回
        $return_arr = ['data' => ['is_show' => $is_show_tp1, 'tp1_version' => $tp1_version]];
        $this->jsonReturn(self::checkReturn($return_arr));
    }

    /**
     * tp1月份列表选项
     */
    public function getMonthOptionAction()
    {

        $month_option = (new HrStaffTp1Server($this->lang, $this->timezone))->getMonthOption($this->userinfo['id']);

        $return_arr = ['data' => ['month_option' => $month_option]];
        $this->jsonReturn(self::checkReturn($return_arr));
    }


    /**
     * tp1信息提交提交
     */
    public function submitDetailAction()
    {
        $paramIn  = $this->paramIn;
        $userinfo = $this->userinfo;

        $validations = [
            "tp1_detail" => "Required",
            "year_month" => "Required",
        ];
        //验证传参
        $this->validateCheck($paramIn, $validations);

        $resultData = $this->atomicLock(function () use ($paramIn, $userinfo) {
            (new HrStaffTp1Server($this->lang, $this->timezone))->saveTp1($paramIn, $userinfo['id'], $userinfo['name']);

        }, 'tp1_submit' . $userinfo['id']);

        if ($resultData === false) { //没有获取到锁
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('ticket_repeat_msg')));
        }

        $this->jsonReturn(self::checkReturn(1));
    }
}