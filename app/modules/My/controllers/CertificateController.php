<?php

namespace FlashExpress\bi\App\Modules\My\Controllers;

use FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\library\ApiClient;

class CertificateController extends Controllers\ControllerBase
{


    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
            $this->paramIn = filter_param($this->paramIn);
        }
    }

    /**
     * 在职证明 基础数据
     */
    public function on_jobAction()
    {
        $param             = $this->paramIn;
        $param['staff_id'] = $this->userinfo['id'];
        $param['type']     = 2;//在职证明
        $method            = 'proof_download_data';

        $client = (new ApiClient("hcm_rpc", '', $method, $this->lang));
        $client->setParams($param);
        $data = $client->execute();

        if (!empty($data['result'])) {
            $this->jsonReturn($data['result']);
        }
        $this->jsonReturn(self::checkReturn(-3, 'connect error'));
    }

    /**
     *  CIMB开户函 基础数据
     */
    public function cimb_accountAction()
    {
        $param             = $this->paramIn;
        $param['staff_id'] = $this->userinfo['id'];
        $param['type']     = 7;//CIMB开户函
        $method            = 'proof_download_data';

        $client = (new ApiClient("hcm_rpc", '', $method, $this->lang));
        $client->setParams($param);
        $data = $client->execute();

        if (!empty($data['result'])) {
            $this->jsonReturn($data['result']);
        }
        $this->jsonReturn(self::checkReturn(-3, 'connect error'));
    }

    /**
     * 在职证明 返回下载到本地的 url
     */
    public function on_job_downloadAction()
    {
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        $param['type']          = 2;//在职证明
        $param['is_send']       = 0;//不发送邮件

        $fle_rpc = (new ApiClient("hcm_rpc", '', 'by_download', $this->lang));
        $fle_rpc->setParams($param);
        $res = $fle_rpc->execute();

        if (!empty($res['result'])) {
            $this->jsonReturn($res['result']);
        }

        $this->jsonReturn(self::checkReturn(-3, 'sys error'));
    }

    /**
     * CIMB开户函 返回下载到本地的 url
     */
    public function cimb_downloadAction()
    {
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        $param['type']          = 7;//CIMB开户函
        $param['is_send']       = 0;//不发送邮件

        $fle_rpc = (new ApiClient("hcm_rpc", '', 'by_download', $this->lang));
        $fle_rpc->setParams($param);
        $res = $fle_rpc->execute();

        if (!empty($res['result'])) {
            $this->jsonReturn($res['result']);
        }

        $this->jsonReturn(self::checkReturn(-3, 'sys error'));
    }

    /**
     * 在职证明 发送到邮箱
     */
    public function on_job_sendAction()
    {
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        $param['type']          = 2;//在职证明
        //1 企业邮箱 2 个人邮箱 默认企业
        $param['mail_type'] = empty($this->paramIn['mail_type']) ? 1 : intval($this->paramIn['mail_type']);
        $fle_rpc            = (new ApiClient("hcm_rpc", '', 'by_download', $this->lang));
        $fle_rpc->setParams($param);
        $res = $fle_rpc->execute();

        if (!empty($res['result'])) {
            $this->jsonReturn($res['result']);
        }
        $this->jsonReturn(self::checkReturn(-3, 'sys error'));
    }

    /**
     * CIMB开户函 发送到邮箱
     */
    public function cimb_sendAction()
    {
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        $param['type']          = 7;//CIMB开户函
        //1 企业邮箱 2 个人邮箱 默认企业
        $param['mail_type'] = empty($this->paramIn['mail_type']) ? 1 : intval($this->paramIn['mail_type']);
        $fle_rpc            = (new ApiClient("hcm_rpc", '', 'by_download', $this->lang));
        $fle_rpc->setParams($param);
        $res = $fle_rpc->execute();

        if (!empty($res['result'])) {
            $this->jsonReturn($res['result']);
        }
        $this->jsonReturn(self::checkReturn(-3, 'sys error'));
    }
}