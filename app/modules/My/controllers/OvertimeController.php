<?php

namespace FlashExpress\bi\App\Modules\My\Controllers;

use FlashExpress\bi\App\Controllers\OvertimeController as BaseOvertimeController;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Modules\My\Server\OvertimeExtendServer;
use FlashExpress\bi\App\Modules\My\Server\OvertimeServer;
use Exception;

class OvertimeController extends BaseOvertimeController
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
        //记录访问日志
        $this->url_log($this->paramIn);
    }

    /**
     * 新建加班
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function addOvertimeAction()
    {
        try {
            $paramIn             = $this->paramIn;
            $paramIn['staff_id'] = $paramIn['staff_info_id'] = $this->userinfo['staff_id'];
            $paramIn['userinfo'] = $this->userinfo;
            $validations         = [
                "staff_id"  => "Required|Int",
                "reason"    => "Required|StrLenGeLe:1,1000|>>>:" . $this->getTranslation()->_('5110'),
            ];
            $this->validateCheck($paramIn, $validations);

            $returnArr = (new OvertimeServer($this->lang, $this->timezone, $this->userinfo))->setLockConf(60,true)->addOvertimeV3UseLock($paramIn);
            $this->jsonReturn($returnArr);
        } catch (ValidationException $e) {
            if ($e->getCode() == 10099) {
                return $this->returnJson(1,'', ['is_error' => 2,'message' => '','data' => json_decode($e->getMessage(), true)]);//二次确认
            }
            throw $e;
        } catch (\Exception $e) {
            if ($e->getCode() == 0) {
                $this->getDI()->get('logger')->write_log('addOvertime:'. $e->getMessage(), 'error');
            } else {
                $this->getDI()->get('logger')->write_log('addOvertime:'. $e->getMessage(), 'info');
            }
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 审批 操作
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function updateOvertimeAction()
    {
        try {
            $paramIn             = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];

            $validations         = [
                "staff_id"           => "Required|Int",
                "audit_id"           => "Required|Int",
                "reject_reason"      => "Required|StrLenGeLe:0,500|>>>:" . $this->getTranslation()->_('1020'),
            ];
            $this->validateCheck($paramIn, $validations);

            //审批加班
            $returnArr = (new OvertimeServer($this->lang, $this->timezone, $this->userinfo))->setLockConf(60,true)->updateOvertimeV3UseLock($paramIn);

            $this->jsonReturn($returnArr);
        } catch (\Exception $e) {
            if ($e->getCode() == 0) {
                $this->getDI()->get('logger')->write_log('updateOvertime:'. $e->getMessage(), 'error');
            } else {
                $this->getDI()->get('logger')->write_log('updateOvertime:'. $e->getMessage(), 'info');
            }
            $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 获取加班类型
     */
    public function getTypeOvertimeAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        //获取请假类型数据
        $returnArr = (new OvertimeServer($this->lang, $this->timezone, $this->userinfo))->getTypeOvertime($paramIn,$this->userinfo);

        $this->jsonReturn($returnArr);
    }



    //马来新班次 获取 加班时长 根据员工班次 获取对应的固定时长 svc 也用
    public function getTimeDurationAction(){
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $validations = [
            "staff_id" => "Required|Int",
            "date_at"  => "Required|Date",
            "type" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        //获取请假类型数据
        $returnArr = (new OvertimeServer($this->lang, $this->timezone, $this->userinfo))->overtimeDurationByShift($paramIn);

        $this->jsonReturn($returnArr);

    }


    public function testApiAction(){
        //新接口的件数
        $storeId = 'MYA0000068';
        $date = '2024-07-15';
        //获取 接口数据 包裹量  java 接口
        $param['store_id']   = $storeId;
        $param['begin_date'] = date('Y-m-d', strtotime("{$date} -1 day"));
        $param['end_date']   = $date;
        $server = new OvertimeExtendServer($this->lang, $this->timezone);
        $data = $server->sendRequest($param, 'deliverycount.get_latest_delivery_by_store_date', 'ard_api');
        var_dump($data);exit;
        if(empty($data)){
            return 0;
        }
    }

}