<?php

namespace FlashExpress\bi\App\Modules\My\Controllers;


use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrShiftModel;
use FlashExpress\bi\App\Modules\My\enums\OsmEnums;
use FlashExpress\bi\App\Modules\My\Server\OsStaffServer;
use Exception;
use FlashExpress\bi\App\Server\SettingEnvServer;


class OsController extends BaseController
{
    protected $os;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 添加外协员工申请
     * @param int    job_id             外协职位
     * @param string employment_date    雇佣日期
     * @param int    employment_days    雇佣天数
     * @param int    shift_id           班次ID
     * @param int    demend_num         申请人数
     * @param string reason             申请原因
     * @return json
     */
    public function addOsStaffAction()
    {
        //[1]入参校验
        $paramIn                               = $this->paramIn;
        $paramIn['param']                      = $this->userinfo;
        $paramIn['param']['position_category'] = $this->userinfo['positions'];
        $paramIn['param']['store_id']          = $this->userinfo['organization_id'];
        $paramIn['staff_id']                   = $this->userinfo['staff_id'];
        $paramIn['organization_id']            = $this->userinfo['organization_id'];
        $paramIn['organization_type']          = $this->userinfo['organization_type'];
        $logger                                = $this->getDI()->get('logger');

        //[2]数据验证
        if (!isset($paramIn['os_type']) ||
            empty($paramIn['os_type']) ||
            !in_array($paramIn['os_type'], [
                enums::$os_staff_type['normal'],
                enums::$os_staff_type['long_term'],
                enums::$os_staff_type['motorcade'],
            ])) {
            throw new Exception("'os_type' invalid input");
        }
        $hubOsRoles = explode(',', (new SettingEnvServer())->getSetValFromCache('hub_os_roles'));

        switch ($paramIn['os_type']) {
            case enums::$os_staff_type['normal']:
                //hub 外协工单，分拨经理 提交申请校验规则
                if (array_intersect($hubOsRoles, $this->userinfo['positions'])) {
                    $validation_sub = [
                        "job_id"          => "Required|IntIn:".OsmEnums::$job_title_id['outsource']."|>>>:".$this->getTranslation()->_('3008'),
                        "employment_days" => "Required|IntIn:1",
                        "demend_num"      => "Required|IntGeLe:1,200|>>>:"."'demend_num' invalid input",
                    ];
                } else {
                    $validation_sub = [
                        "job_id"          => "Required|IntIn:98,111,473,13,452,110,271,1000,1199,37|>>>:" . $this->getTranslation()->_('3008'),
                        "employment_days" => "Required|IntGeLe:1,7",
                        "demend_num"      => "Required|IntGeLe:1,50|>>>:" . "'demend_num' invalid input",
                    ];
                }

                break;
            case enums::$os_staff_type['long_term']:
                //hub 外协工单，分拨经理 提交申请校验规则
                if (array_intersect($hubOsRoles, $this->userinfo['positions'])) {
                    $validation_sub = [
                        "job_id"          => "Required|IntIn:".OsmEnums::$job_title_id['security_outsource']."|>>>:".$this->getTranslation()->_('3008'),
                        "employment_days" => "Required|IntGeLe:15,90",
                        "demend_num"      => "Required|IntGeLe:1,200|>>>:"."'demend_num' invalid input",
                    ];
                } else {
                    $validation_sub = [
                        "job_id"          => "Required|IntIn:271,98,473|>>>:" . $this->getTranslation()->_('3008'),
                        "employment_days" => "Required|IntGeLe:90,365|>>>:" . $this->getTranslation()->_('err_msg_more_days'),
                        "demend_num"      => "Required|IntGeLe:1,50|>>>:" . "'demend_num' invalid input",
                    ];
                }
                break;
            case enums::$os_staff_type['motorcade']:
                $validation_sub = [
                    "job_id"          => "Required|IntIn:110|>>>:" . $this->getTranslation()->_('3008'),
                    "employment_days" => "Required|IntGeLe:1,7",
                    "demend_num"      => "Required|IntGeLe:1,100|>>>:" . "'demend_num' invalid input",
                ];
                break;
            default:
                $validation_sub = [];
                break;
        }

        //[3]数据验证
        //短期外协的可选雇用日期为从申请日期的第二天起+7天
        //长期外协的可选雇用日期为从申请日期的第二天起+90日
        $dateFrom = date("Y-m-d", time() + 86400);
        //$dateTo   = $paramIn['os_type'] == enums::$os_staff_type['normal'] ? date("Y-m-d", time() + 8 * 86400) : date("Y-m-d", time() + 91 * 86400);
        $dateTo   =  date("Y-m-d", time() + 91 * 86400);
        if($paramIn['os_type'] == enums::$os_staff_type['normal']) {
            $dateFrom = date("Y-m-d", time());
            $dateTo = date("Y-m-d", time() + 9 * 86400);
        }

        $validations = [
            "employment_date"   => "Required|DateFromTo:{$dateFrom},{$dateTo}|>>>:" . $this->getTranslation()->_('err_msg_invalid_date'),
            "shift_id"          => "Required|Int",
            "reason_type"       => "Required|IntGeLe:0,8",
        ];

        //hub 外协工单，分拨经理 提交申请校验规则
        if (array_intersect($hubOsRoles, $this->userinfo['positions'])) {
            //今日或次日
            $dateFrom                       = date("Y-m-d");
            $dateTo                         = date("Y-m-d", strtotime("+6 day"));
            $validations["employment_date"] = "Required|DateFromTo:{$dateFrom},{$dateTo}|>>>:employment_date invalid input";
            $validations['need_remark']     = "StrLenGeLe:0,500|>>>:" . $this->getTranslation()->_('os_need_remark_error');
        }

        if (in_array($paramIn['reason_type'], [0, 2])) { //离职或其他原因需验证reason字段[原因备注字段]
            $valid = [
                "reason" => "Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('1019'),
            ];
            $validations = array_merge($validations, $valid);
        }
        $this->validateCheck($this->paramIn, array_merge($validations, $validation_sub));

        //[4]业务处理
        $returnArr =  (new OsStaffServer($this->lang, $this->timezone))->addOsStaffUseLock($paramIn);
        //[3]数据返回
        return $this->jsonReturn($returnArr);
    }

    /**
     * 获取班次、申请职位、申请原因列表；所属部门；
     */
    public function getShiftListAction()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        //[2]业务处理
        try{
            $returnArr = (new OsStaffServer($this->lang, $this->timezone))->getRequestList($paramIn);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("OsController:getShiftList:" . $e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

        //[3]数据返回
        return $this->jsonReturn($this->checkReturn(['data' => ['dataList' => $returnArr]]));
    }

    /**
     * 班次列表
     * !!!接口废弃 班次数据从上面接口出
     * https://flashexpress.feishu.cn/wiki/VQG7wpkbLidnC0ku8YAclWqlnHg
     * @return void
     */
    public function getShiftListV2Action()
    {
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        $job_title       = $this->processingDefault($paramIn, 'job_id', 2);
        $employment_date = $this->processingDefault($paramIn, 'employment_date');
        $server = new OsStaffServer($this->lang, $this->timezone);
        $list = $server->getShiftList($job_title, $employment_date);
        //新增 半天班的
        $halfList = [];
        if($job_title == enums::$job_title['dc_officer']){
            $halfList =  $server->getShiftList($job_title, $employment_date, HrShiftModel::SHIFT_GROUP_HALF_DAY_SHIFT);
        }

        $this->jsonReturn($this->checkReturn(['data' => ['dataList' => $list,'halfList' => $halfList]]));
    }

    /**
     * 获取可申请职位下拉列表
     */
    public function getOsJobTitleListAction()
    {
        try {
            //[1]传入参数
            $paramIn                = $this->paramIn;
            $paramIn['userinfo']    = $this->userinfo;

            $validations = [
                "type"      => "Required|IntIn:1,2,3",
            ];
            $this->validateCheck($this->paramIn, $validations);

            $returnArr = (new OsStaffServer($this->lang, $this->timezone))->getOsJobTitleList($paramIn);
            return  $this->jsonReturn(self::checkReturn(['data' => $returnArr]));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("OsController:shortTermApplyAction:" . $e->getMessage());
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

}