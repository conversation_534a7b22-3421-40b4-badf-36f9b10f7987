<?php
namespace FlashExpress\bi\App\Modules\My\Controllers;

use FlashExpress\bi\App\Enums\InventoryCheckEnums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Modules\My\Server\WmsPlanServer;
use Exception;
/**
 * 物料盘点控制器
 *
 */
class WmsPlanController extends BaseController
{

    /**
     * 初始化
     */
    public function initialize()
    {
        parent::initialize();
        $this->planWmsServer = new WmsPlanServer($this->lang, $this->timezone);
        $method = $this->request->getMethod();
        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }


    /**
     * 包材盘点待处理已结束列表
     * @Date:2022-05-15 15:16
     * @return:
     * @author: peak pan
     *
     */
    public function planListAction()
    {
        try {
            $validations = [
                "page_num" => "Int|>>>:" . $this->getTranslation()->_('miss_args'),
                "page_size" => "Int|>>>:" . $this->getTranslation()->_('miss_args'),
                "type" => "IntIn:1,2|>>>:" . $this->getTranslation()->_('miss_args'),
            ];
            //验证
            $this->validateCheck($this->paramIn, $validations);

            $params['type'] = $this->paramIn['type'];//1 待处理 2 已结束
            $params['page_num'] = $this->processingDefault($this->paramIn, 'page_num', 2);
            $params['page_size'] = $this->processingDefault($this->paramIn, 'page_size', 2, InventoryCheckEnums::PAGE_SIZE);
            $params['staff_id'] = $this->userinfo['staff_id'];

            $list = $this->planWmsServer->getMyplanList($params);

            $this->jsonReturn($this->checkReturn(array('data' => $list)));

        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("singleListAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 根据电话和网点id返回这个人的电话
     * @Token
     * @Date: 2022-05-15 22:42
     * @return:
     **@author: peak pan
     */
    public function getStaffStoreInfoAction()
    {
        $paramIn['store_id'] = $this->paramIn['store_id'];
        $paramIn['department_id'] = $this->paramIn['department_id'];
        $paramIn['staff_id'] = $this->paramIn['staff_id'];

        $paramIn['store_name'] = $this->paramIn['store_name'];
        $paramIn['department_name'] = $this->paramIn['department_name'];

        $res = $this->planWmsServer->getStaffStoreInfo($paramIn);

        if ($res['code'] == InventoryCheckEnums::SUCCESS) {
            $this->jsonReturn($this->checkReturn(array('data' => $res['data'])));
        }
        return $this->jsonReturn(self::checkReturn(-3, $res['message']));
    }


    /**
     * 包材盘点详情列表
     * @Date:2022-05-15 15:16
     * @return:
     * @author: peak pan
     *
     */
    public function planInfoListAction()
    {
        try {
            $validations = [
                "page_num" => "Int|>>>:" . $this->getTranslation()->_('miss_args'),
                "page_size" => "Int|>>>:" . $this->getTranslation()->_('miss_args'),
                "type" => "Required|IntIn:1,2|>>>:" . $this->getTranslation()->_('miss_args'),
                "id" => "Required|Int|>>>:" . $this->getTranslation()->_('miss_args'),
            ];
            //验证
            $this->validateCheck($this->paramIn, $validations);
            $params['type'] = $this->paramIn['type'];//1盘点id
            $params['id'] = $this->paramIn['id'];//1盘点id
            $params['category'] = $this->paramIn['category'] ?? '';
            $params['specs_model'] = $this->paramIn['specs_model'] ?? '';
            $params['goods_name'] = $this->paramIn['goods_name'] ?? '';

            $params['page_num'] = $this->processingDefault($this->paramIn, 'page_num', 2);
            $params['page_size'] = $this->processingDefault($this->paramIn, 'page_size', 2, InventoryCheckEnums::PAGE_SIZE);

            $list = $this->planWmsServer->getMyplanInfoList($params);

            $this->jsonReturn($this->checkReturn(array('data' => $list)));

        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("planInfoListAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

    }


    /**
     * 包材盘点详情列表
     * @Date:2022-05-15 15:16
     * @return:
     * @author: peak pan
     *
     */
    public function planInfoDetailAction()
    {
        try {
            $validations = [
                "page_num" => "Int|>>>:" . $this->getTranslation()->_('miss_args'),
                "page_size" => "Int|>>>:" . $this->getTranslation()->_('miss_args'),
                "id" => "Required|Int|>>>:" . $this->getTranslation()->_('miss_args'),
            ];
            //验证
            $this->validateCheck($this->paramIn, $validations);
            $params['id'] = $this->paramIn['id'];//1盘点id
            $params['page_num'] = $this->processingDefault($this->paramIn, 'page_num', 2);
            $params['page_size'] = $this->processingDefault($this->paramIn, 'page_size', 2, InventoryCheckEnums::PAGE_SIZE);
            $list = $this->planWmsServer->getMyplanInfoDetail($params);

            $this->jsonReturn($this->checkReturn(array('data' => $list)));

        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("planInfoListAction 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }


    /**
     * 筛选分类和数据枚举
     * @Token
     * @Date: 2022-05-16 09:39
     * @return:
     **@author: peak pan
     */
    public function getPlanSkuEnumsAction()
    {
        $res = $this->planWmsServer->getPlanSkuEnums();

        if ($res['code'] == InventoryCheckEnums::SUCCESS) {
            return $this->returnJson(InventoryCheckEnums::SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }


    /**
     * 资产盘点 转单任务提交接口
     * @Date: 2022-05-15 15:20
     * @return:
     **@author: peak pan
     */

    public function relayAddAction()
    {
        try {
            $validations = [
                'plan_id' => 'Required|IntGe:1|>>>:plan_id check not null',
                "staff_id" => "Required|IntGe:1|>>>:staff_id check not null",
            ];
            //验证
            $this->validateCheck($this->paramIn, $validations);

            $params['plan_id'] = $this->paramIn['plan_id'];//转单的盘点id
            $params['staff_id'] = $this->paramIn['staff_id'];//转单人的id
            $list = $this->planWmsServer->relaySave($params);

            return $this->returnJson(InventoryCheckEnums::SUCCESS, $list['message'], $list['data']);

        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("relaySave 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }


    /**
     * 物料盘点 单个盘点提交接口
     * @Date: 2022-05-15 15:20
     * @return:
     **@author: peak pan
     */

    public function staPlanAction()
    {
        try {
            $validations = [
                'id' => 'Required|IntGe:1|>>>:inventory check id',
                "plan_nub" => "IntLt:10000000000|>>>: Cannot plan_nub exceed 10000000000 or cannot be empty!"
            ];
            //验证
            $this->validateCheck($this->paramIn, $validations);

            $params['id'] = $this->paramIn['id'];//转单的盘点id
            $params['plan_nub'] = $this->paramIn['plan_nub'];//转单的盘点id
            $params['plan_image_path'] = $this->paramIn['plan_image_path'];//转单的盘点id
            $list = $this->planWmsServer->staPlanSubmits($params);

            return $this->returnJson(InventoryCheckEnums::SUCCESS, $list['message'], $list['data']);

        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("relayAdd 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

    /**
     * 总盘点提交
     * @Date: 2022-05-15 15:20
     * @return:
     **@author: peak pan
     */

    public function endPlanWmsSubmitAction()
    {
        try {
            $validations = [
                'task_id' => 'Required|IntGe:1|>>>:task_id check not null'
            ];
            //验证
            $this->validateCheck($this->paramIn, $validations);

            $params['task_id'] = $this->paramIn['task_id'];
            $params['staff_id'] = $this->userinfo['staff_id'];
            $list = $this->planWmsServer->endPlanWmsSubmit($params);

            return $this->returnJson(InventoryCheckEnums::SUCCESS, $list['message'], $list['data']);

        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("relayAdd 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }

    }


    /**
     * 从消息点击获取到开始时间和数量统计及状态接口
     * @Token
     * @Date: 2022-05-21 11:22
     * @return:
     **@author: peak pan
     */

    public function planWmsDetailAction()
    {
        try {
            $validations = [
                'plan_id' => 'Required|IntGe:1|>>>:inventory check plan_id not null', //盘点单ID
            ];
            //验证
            $this->validateCheck($this->paramIn, $validations);

            $params['plan_id'] = $this->paramIn['plan_id'];//转单的盘点id
            $params['staff_id'] = $this->userinfo['staff_id'];//转单人的id
            $list = $this->planWmsServer->getplanWmsDetail($params);

            return $this->returnJson($list['code'], $list['message'], $list['data']);

        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("relayAdd 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }


    /**
     * 从消息点击获取到开始时间和数量统计及状态接口
     * @Token
     * @Date: 2022-05-21 11:22
     * @return:
     **@author: peak pan
     */

    public function planWmsStaChekAction()
    {
        try {
            $validations = [
                'plan_id' => 'Required|IntGe:1|>>>:inventory check plan_id not null', //盘点单ID
            ];
            //验证
            $this->validateCheck($this->paramIn, $validations);

            $params['plan_id'] = $this->paramIn['plan_id'];//转单的盘点id
            $params['staff_id'] = $this->userinfo['staff_id'];//转单人的id
            $list = $this->planWmsServer->getplanWmsStaChek($params);

            return $this->returnJson($list['code'], $list['message'], $list['data']);

        } catch (ValidationException $e) {
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("relayAdd 异常信息:" . $e->getMessage(), 'error');
            return $this->jsonReturn(self::checkReturn(-3, $e->getMessage()));
        }
    }

}
