<?php
namespace FlashExpress\bi\App\Modules\My\Controllers;

use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Modules\My\library\Enums\VehicleInfoEnums;
use FlashExpress\bi\App\Modules\My\Server\VehicleServer;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\HrStaffInfoServer;
use FlashExpress\bi\App\Server\SettingEnvServer;

class VehicleController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 车辆信息，枚举列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function enumVehicleAction()
    {
        $returnArr['data'] = [];

        // 非van/bike/car职位，无权限访问数据 MY:Van courier[110]、Bike Courier[13]、Car Courier[1199]
        $vehicleJobTitle = explode(',',(new SettingEnvServer())->getSetVal('job_title_vehicle_type')) ;

        $staff_info_id = $this->userinfo['id'];
        $checkStaff = (new StaffRepository())->checkoutStaffBi($staff_info_id);
        if ($checkStaff['is_sub_staff'] == 1){
            // 子账号需切到主账号 获取信息
            $supportStaffInfo = (new AttendanceRepository($this->lang,$this->timezone))->getSupportInfoBySubStaff($staff_info_id);
            if (empty($supportStaffInfo)){
                $this->jsonReturn(self::checkReturn($returnArr));
            }
            $staff_info_id = $supportStaffInfo['staff_info_id'];
            $checkStaff = (new StaffRepository())->checkoutStaffBi($staff_info_id);
        }

        if (in_array($checkStaff['job_title'], $vehicleJobTitle)) {
            $infoArr = (new VehicleServer($this->lang, $this->timezone))->getVehicleInfoS(["id"=>$staff_info_id,"job_title"=>$checkStaff['job_title']]);

            //如果车辆信息不存在输出类型(判断废弃，同时返回)
            $enumArr = (new VehicleServer($this->lang, $this->timezone))->enumVehicleS(["job_title"=>$checkStaff['job_title']]);

            $returnArr['data'] = $infoArr + $enumArr;
        }

        $this->jsonReturn(self::checkReturn($returnArr));
    }

    /**
     * 创建车辆信息
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addVehicleInfoAction()
    {
        //[1]入参和验证
        $paramIn  = trim_array($this->paramIn);
        $userinfo = $this->userinfo;

        $this->getDI()->get('logger')->write_log(['kit_add_vehicle_info'=>$paramIn,'staff_info'=>$userinfo], 'info');


        $checkStaff = (new StaffRepository())->checkoutStaffBi($userinfo['id']);
        $staff_info_id = $userinfo['id'];
        if ($checkStaff['is_sub_staff'] == 1){
            // 子账号需切到主账号 获取信息
            $supportStaffInfo = (new AttendanceRepository($this->lang,$this->timezone))->getSupportInfoBySubStaff($staff_info_id);
            if (empty($supportStaffInfo)){
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4008')));
            }
            $staff_info_id = $supportStaffInfo['staff_info_id'];
            $checkStaff = (new StaffRepository())->checkoutStaffBi($staff_info_id);
        }

        // 职位权限校验: 仅限 VehicleInfoEnums::JOB_TITLE_ITEM 列出的职位可操作
        if (!array_key_exists($checkStaff['job_title'], VehicleInfoEnums::JOB_TITLE_ITEM)) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4009')));
        }


        if (!is_array($paramIn['driving_licence_img_item']) && !empty($paramIn['driving_licence_img_item'])) {
            $paramIn['driving_licence_img_item'] = json_decode($paramIn['driving_licence_img_item']);
        }

        // 公共验证字段
        $validations = [
            'vehicle_source'                       => 'Required|IntIn:1,2,3|>>>:vehicle_source' . $this->getTranslation()->_('miss_args'),
            //"plate_number" => "Required|StrLenGeLe:1,50|>>>:" . $this->getTranslation()->_('7130'),
            "engine_number"                        => "Required|StrLenGeLe:10,17|>>>:" . $this->getTranslation()->_('vehicle_info_0002'),
            'license_location'                     => 'Required|StrLenGeLe:1,512|>>>:license_location' . $this->getTranslation()->_('miss_args'),
            "registration_certificate_img"         => "Required|StrLenGeLe:1,255|>>>:registration_certificate_img" . $this->getTranslation()->_('miss_args'),
            "vehicle_img"                          => "Required|Arr|ArrLen:3|>>>:" . $this->getTranslation()->_('vehicle_img_validation'),
            "insurance_policy_number"              => "Required|StrLenGeLe:1,50|>>>:insurance_policy_number" . $this->getTranslation()->_('miss_args'),
            "insurance_start_date"                 => "Required|Date|>>>:insurance_start_date" . $this->getTranslation()->_('miss_args'),
            "insurance_end_date"                   => "Required|Date|>>>:insurance_end_date" . $this->getTranslation()->_('miss_args'),
            "vehicle_tax_expiration_date"          => "Required|Date|>>>:vehicle_tax_expiration_date" . $this->getTranslation()->_('miss_args'),
            "vehicle_tax_certificate_img"          => "Required|StrLenGeLe:1,255|>>>:vehicle_tax_certificate_img" . $this->getTranslation()->_('miss_args'),
            "driver_license_type"                  => "Required|IntIn:1,2,3,4,5|>>>:driver_license_type" . $this->getTranslation()->_('miss_args'),
            "driver_license_type_other_text"       => "IfIntEq:driver_license_type,100|Required|StrLenGeLe:1,200|>>>:driver_license_type_other_text" . $this->getTranslation()->_('miss_args'),
            "driver_license_number"                => "Required|StrLenGeLe:1,128|>>>:driver_license_number" . $this->getTranslation()->_('miss_args'),
            "driver_license_start_date"            => "Required|Date|>>>:driver_license_start_date" . $this->getTranslation()->_('miss_args'),
            "driver_license_end_date"              => "Required|Date|>>>:driver_license_end_date" . $this->getTranslation()->_('miss_args'),
            "driving_licence_img_item"             => "Required|ArrLen:2|>>>:" . $this->getTranslation()->_('vehicle_info_0008'),
            //new
            'vehicle_registration_date'            => 'Date|>>>:vehicle_registration_date' . $this->getTranslation()->_('error_message'),
            'vehicle_registration_number'          => 'StrLenGeLe:1,50|>>>:vehicle_registration_number ' . $this->getTranslation()->_('error_message'),
            'driving_license_vehicle_restrictions' => 'Arr|ArrLenLe:16|>>>:driving_license_vehicle_restrictions' . $this->getTranslation()->_('error_message'),
        ];

        $other_validations_2 = [];

        // van职位 特有字段验证
        if (in_array($checkStaff['job_title'], VehicleInfoEnums::VAN_JOB_GROUP_ITEM)) {
            $vehicle_size          = implode(',', array_keys(VehicleServer::getVehicleSize(false)));
            $vehicle_type_category = implode(',', array_keys(VehicleInfoEnums::VEHICLE_TYPE_CATEGORY_LIST));

            $other_validations = [
                "vehicle_brand"      => "Required|IntIn:1,2,3,4,5,6,7,8,9,10,11,12,13,14,100|>>>:vehicle_brand" . $this->getTranslation()->_('miss_args'),
                "vehicle_brand_text" => "IfIntEq:vehicle_brand,100|Required|StrLenGeLe:1,200|>>>:vehicle_brand_text" . $this->getTranslation()->_('miss_args'),
                "vehicle_model"      => "Required|IntIn:1,2,3,4,5,6,7,8,9,10,100|>>>:vehicle_model" . $this->getTranslation()->_('miss_args'),
                "vehicle_model_text" => "IfIntEq:vehicle_model,100|Required|StrLenGeLe:1,200|>>>:vehicle_model_text" . $this->getTranslation()->_('miss_args'),
                "vehicle_size"       => "Required|IntIn:$vehicle_size|>>>:vehicle_size" . $this->getTranslation()->_('miss_args'),
                'buy_date'           => 'Date|>>>:buy_date' . $this->getTranslation()->_('error_message'),
                "oil_type"              => "Required|IntIn:1,2,3|>>>:oil_type" . $this->getTranslation()->_('miss_args'),
                "oil_company"           => "IntIn:0,1,2,3|>>>:oil_company" . $this->getTranslation()->_('miss_args'),
                'oil_img'               => 'StrLenGeLe:0,255|>>>:oil_img' . $this->getTranslation()->_('error_message'),
                "oil_number"            => "IfIntNe:oil_company,0|Required|Regexp:/^\d{10,20}$/|>>>:" . $this->getTranslation()->_('fuel_manage_is_oil_number'),
            ];

            if (!empty($paramIn['vehicle_type_category'])) {
                $other_validations['vehicle_type_category'] = 'Required|IntIn:' . $vehicle_type_category . '|>>>:vehicle_type_category' . $this->getTranslation()->_('error_message');
            }
            
            $validations = array_merge($validations, $other_validations);
        }

        //Bike类型的个人代理
        if ($checkStaff['job_title'] == VehicleInfoEnums::JOB_BIKE_TITLE_ID &&
            in_array($checkStaff['hire_type'],HrStaffInfoModel::$agentTypeTogether)) {

            $other_validations = [
                "oil_type"              => "Required|IntIn:1,2,3|>>>:oil_type" . $this->getTranslation()->_('miss_args'),
                "oil_company"           => "IntIn:0,1,2,3|>>>:oil_company" . $this->getTranslation()->_('miss_args'),
                'oil_img'               => 'StrLenGeLe:0,255|>>>:oil_img' . $this->getTranslation()->_('error_message'),
                "oil_number"            => "IfIntNe:oil_company,0|Required|Regexp:/^\d{10,20}$/|>>>:" . $this->getTranslation()->_('fuel_manage_is_oil_number'),
            ];
            $validations = array_merge($validations, $other_validations);
        }

        $this->validateCheck($paramIn, $validations);


        // 验证车牌号码的规则：允许英文字母+数字
        if (!preg_match("/^[a-zA-Z0-9]{1,50}$/u", $paramIn['plate_number'])) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('plate_number_other_error')));
        }

        if ($paramIn['insurance_end_date'] < $paramIn['insurance_start_date']) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0004')));
        }

        if ($paramIn['driver_license_end_date'] < $paramIn['driver_license_start_date']) {
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('vehicle_info_0005')));
        }
        if (empty($checkStaff) || $checkStaff['formal'] != 1) {
            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('staff_vehicle_notice_1')));
        }

        $params = [
            "id"        => $staff_info_id,
            "job_title" => $checkStaff['job_title'],
            "hire_type" => $checkStaff['hire_type'],
        ];
        $returnArr = (new VehicleServer($this->lang, $this->timezone))->addVehicleInfoS($paramIn, $params,$this->userinfo['id']);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }
    
}
