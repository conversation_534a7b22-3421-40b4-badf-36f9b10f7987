<?php
namespace FlashExpress\bi\App\Modules\My\Controllers;

use FlashExpress\bi\App\Controllers\IndexController as BaseIndex;
use FlashExpress\bi\App\Server\OtherServer;

class IndexController extends BaseIndex
{
    /**
     * 上班打卡提示未回款金额
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function remittanceDialogAction()
    {

        //[1]入参
        $paramIn = $this->paramIn;
        $userinfo = $this->userinfo;
        $validations = [
            "shift_type"      => "IntIn:1,2|>>>:shift_type".$this->getTranslation()->_('miss_args'),
            "shift_index"     => "IntIn:1,2,3,4|>>>:shift_index".$this->getTranslation()->_('miss_args'),
            "attendance_date" => "Date|>>>:attendance_date".$this->getTranslation()->_('miss_args'),
            "clock_type"      => "IntIn:1,2|>>>:clock_type".$this->getTranslation()->_('miss_args'),

        ];
        $this->validateCheck($paramIn, $validations);
        //[2]业务处理
        //1、3上班 2、4下班
        //clock_type 1 下班  2上班
        if(empty($paramIn['clock_type']) && !empty($paramIn['shift_index'])){
            $paramIn['clock_type'] = in_array($paramIn['shift_index'],[1,3])?2:1;
        }

        //历史逻辑马来调用外层
        $returnArr = (new OtherServer($this->lang , $this->timezone))->remittanceDialogS($paramIn, $userinfo);
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }
}