<?php

namespace FlashExpress\bi\App\Modules\My\Controllers;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Modules\My\Server\ReinstatementServer;
use FlashExpress\bi\App\Server\StaffServer;

class ReinstatementController extends BaseController
{
    //登录之后点击验证 并且返回是否已经存在申请记录
    public function checkAction(){

        $server = new ReinstatementServer($this->lang,$this->timezone);
        $suspension = $server->getLatestSuspensionLog($this->userinfo['staff_id']);
        $request = $server->getRequestByStaffId($this->userinfo['staff_id'], $suspension['id']??0);
        $returnData =[];
        $returnData['reason'] = $suspension['stop_duty_reason'] ?? '0';
        $returnData['has_apply'] = boolval($request);
        $returnData['staff_info_id'] = $this->userinfo['staff_id'];
        $returnData['staff_name'] = $this->userinfo['name'];
        //前置验证
        $server->checkCanApply($this->userinfo['staff_id']);
        $staffServer = new StaffServer($this->lang,$this->timezone);
        $staffInfo   = $staffServer->getStaffInfo(['staff_info_id'=>$this->userinfo['staff_id']],'hire_type');
        $returnData['hire_type'] = intval($staffInfo['hire_type']);

        $this->jsonReturn($this->checkReturn(['data' => $returnData]));
    }


    /**
     * 提交复职申请
     * @return void
     */
    public function addRequestAction()
    {
        $params = $this->paramIn;
        $this->validateCheck($params, [
            'reason'             => "Required|Int",
            'reason_explanation' => "Required|StrLenGeLe:10,500",
            'attach'             => "ArrLenGeLe:0,5",
            'attach[*]'          => "Url",
        ]);
        $params['staff_info_id'] = $this->userinfo['staff_id'];
        $params['user_info'] = $this->userinfo;

        $server = new ReinstatementServer($this->lang, $this->timezone);
        $result = $server->addRequestUseLock($params);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    /**
     * 审批操作
     * @return void
     */
    public function auditAction()
    {
        $params = $this->paramIn;
        $validations = [
            "status"   => "Required|IntIn:2,3",
            "audit_id" => "Required|Int",
        ];
        $this->validateCheck($params, $validations);
        if ($params['status'] == enums::APPROVAL_STATUS_REJECTED && (empty($params['reject_reason']))) {
            $this->jsonReturn($this->checkReturn('-1', 'reject_reason'));
        }
        if ($params['status'] == enums::APPROVAL_STATUS_REJECTED) {
            if (mb_strlen($params['reject_reason']) > 500) {
                $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('1020')));
            }
        }
        $returnArr = (new ReinstatementServer($this->lang, $this->timezone))->auditUseLock($params, $this->userinfo['staff_id']);
        $this->jsonReturn($returnArr);
    }


    /**
     * 获取停职恢复在职员工工号信息
     * @return void
     * @throws \FlashExpress\bi\App\library\Exception\BusinessException
     */
    public function getStaffInfoAction()
    {
        $data['staff_info_id'] = $this->userinfo['id'];
        $data['staff_name'] = $this->userinfo['name'];
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

}