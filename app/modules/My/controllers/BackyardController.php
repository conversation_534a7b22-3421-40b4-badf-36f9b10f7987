<?php
namespace FlashExpress\bi\App\Modules\My\Controllers;
use FlashExpress\bi\App\Modules\My\Server\MessageServer;
use FlashExpress\bi\App\Modules\My\Server\BackyardServer;
use FlashExpress\bi\App\Controllers\BackyardController as BaseBackyardController;


class BackyardController extends BaseBackyardController
{

    public $staff_id;
    public $miss_args;
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
        $this->paramIn = filter_param($this->paramIn);
    }

    public function onConstruct()
    {
        parent::onConstruct();

    }



    //警告书签名保存接口
    public function sign_warningAction(){
        $param = $this->paramIn;
        $param['staff_info_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "msg_id" => "Required|StrLenGeLe:1,100",
            "url"    => 'Required|StrLenGeLe:1,500|>>>:' . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($param, $validations);
        //[2]业务处理\
        $returnArr = $this->atomicLock(function () use ($param) {
            $message_server = new MessageServer($this->lang,$this->timezone);
            $message_server->sign_for_warning($param);
        }, 'sign_warning_msg_id_' . $param['msg_id']);
        if ($returnArr === false) { //没有获取到锁
            return $this->jsonReturn($this->checkReturn(-3,$this->getTranslation()->_('ticket_repeat_msg')));
        }
        //[3]数据返回
        $this->jsonReturn($this->checkReturn(1));
    }



    public function un_read_and_auditAction(){
        //[1]入参校验
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['userinfo'] =  $this->userinfo;
        $validations         = [
            "staff_id" => "Required|Int"
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $server = new BackyardServer($this->lang, $this->timezone);
        $return = $server->getRedDotsNum($paramIn);
        $this->getDI()->get('logger')->write_log("un_read_and_audit_{$this->userinfo['staff_id']}: ".json_encode($return,JSON_UNESCAPED_UNICODE),'info');
        return $this->jsonReturn($this->checkReturn(array('data' => $return)));
    }

}
