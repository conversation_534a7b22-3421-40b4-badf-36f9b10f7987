<?php

namespace FlashExpress\bi\App\Modules\My\enums;

use FlashExpress\bi\App\Enums\BaseEnums;

/**
 * 常用枚举数据 用于数据导入字符对比
 * Class enums
 */
class CompanyEnums  extends BaseEnums
{
    public static function getCodeTxtMap($lang = '', string $code = '')
    {
        $txt = self::$code ?? '';
        return $txt;
    }

    /**
     * 马来真实公司ID
     */
    const FLASH_EXPRESS_COMPANY_ID = 315;
    const F_COMMERCE_COMPANY_ID = 15006;
    const FULFILLMENT_COMPANY_ID = 20001;
    const FLASH_PAY_ID = 60001;
    const FLASH_MONEY_ID = 15050;
    const LNT_ID = '-1';

    public static $realCompanyMap = [
        self::FLASH_EXPRESS_COMPANY_ID => 'Flash Express',
        self::FULFILLMENT_COMPANY_ID   => 'Flash Fulfillment',
        self::FLASH_MONEY_ID           => 'Flash Money',
        self::FLASH_PAY_ID             => 'Flash Pay',
        self::F_COMMERCE_COMPANY_ID    => 'Flash Commerce',
        self::LNT_ID                   => 'LNT',
    ];

}
