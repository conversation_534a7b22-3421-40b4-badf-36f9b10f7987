<?php
/**
 * Author: Bruce
 * Date  : 2023-12-04 20:52
 * Description:
 */

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\HikvisionSyncTaskModel;

class HikvisionSyncTaskRepository extends BaseRepository
{
    public $timezone;

    public function __construct($timezone)
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    /**
     * 获取 hik 同步任务 结果。
     * @param $staff_hikvision_ids
     * @return array
     */
    public static function getHikvisionSyncTaskList($staff_hikvision_ids)
    {
        if (empty($staff_hikvision_ids)) {
            return [];
        }
        return HikvisionSyncTaskModel::find([
            'conditions' => 'staff_hikvision_id in ({hikvision_ids:array})',
            'bind'       => [
                'hikvision_ids' => $staff_hikvision_ids,
            ],
        ])->toArray();
    }

}