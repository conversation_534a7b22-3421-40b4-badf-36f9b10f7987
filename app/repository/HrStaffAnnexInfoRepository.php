<?php
/**
 * Author: Bruce
 * Date  : 2024-04-25 14:37
 * Description:
 */

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\HrStaffAnnexInfoModel;

class HrStaffAnnexInfoRepository extends BaseRepository
{
    public function initialize()
    {
        parent::initialize();
    }

    public static function getStaffAnnexInfoInfo($columns = '*', $conditions = '', $bind = [])
    {
        $result = HrStaffAnnexInfoModel::findFirst([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);

        return empty($result) ? [] : $result->toArray();
    }

}