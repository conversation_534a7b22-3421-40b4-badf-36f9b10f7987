<?php
/**
 * Author: Bruce
 * Date  : 2023-05-24 17:49
 * Description:
 */

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\backyard\HrOrganizationDepartmentStoreRelationModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Server\StaffServer;

class HrOrganizationDepartmentRelationStoreRepository extends BaseRepository
{
    public $timezone;

    public function __construct($timezone)
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    /**
     * 查询部门关联的网点
     * @param $departmentIds
     * @return array
     */
    public function getDepartmentStoreRelationInfo($departmentIds)
    {
        $storeRelate = HrOrganizationDepartmentStoreRelationModel::find([
            'conditions' => "department_id in ({department_ids:array}) and is_deleted = :deleted: and state = :state: and level_state = :level_state:",
            'bind' => [
                'department_ids'  => $departmentIds,
                'deleted'   => HrOrganizationDepartmentStoreRelationModel::IS_DELETED_NO,
                'state'   => HrOrganizationDepartmentStoreRelationModel::STATE_YES,
                'level_state'   => HrOrganizationDepartmentStoreRelationModel::LEAVE_STATE_YES,
            ]
        ])->toArray();

        return !empty($storeRelate) ? array_column($storeRelate, 'store_id') : [];
    }


    /**
     * 查找组织架构上大区片区负责人
     * @param $store_id
     * @return int[]
     */
    public function getOrganizationRegionPieceManagerId($store_id)
    {
        $data = [
            'region_manager_id' => 0,
            'piece_manager_id'  => 0,
            'region_name'       => '',
            'piece_name'        => '',
            'region_id'         => 0,
            'piece_id'          => 0,
        ];

        $relation = HrOrganizationDepartmentStoreRelationModel::findFirst([
            'conditions' => "store_id = :store_id: and is_deleted = :deleted: and state = :state: and level_state = :level_state:",
            'bind'       => [
                'store_id'    => $store_id,
                'deleted'     => HrOrganizationDepartmentStoreRelationModel::IS_DELETED_NO,
                'state'       => HrOrganizationDepartmentStoreRelationModel::STATE_YES,
                'level_state' => HrOrganizationDepartmentStoreRelationModel::LEAVE_STATE_YES,
            ],
        ]);

        if (!empty($relation)) {
            if (!empty($relation->region_id)) {
                $region_detail             = SysManageRegionModel::findFirst([
                    'conditions' => "id = :region_id: ",
                    'bind'       => ['region_id' => $relation->region_id],
                ]);
                $data['region_manager_id'] = !empty($region_detail) ? $region_detail->manager_id : 0;
                $data['region_name']       = !empty($region_detail) ? $region_detail->name : '';
                $data['region_id']         = !empty($region_detail) ? $region_detail->id : '';
            }

            if (!empty($relation->piece_id)) {
                $piece_detail             = SysManagePieceModel::findFirst([
                    'conditions' => "id = :piece_id: ",
                    'bind'       => ['piece_id' => $relation->piece_id],
                ]);
                $data['piece_manager_id'] = !empty($piece_detail) ? $piece_detail->manager_id : 0;
                $data['piece_name']       = !empty($piece_detail) ? $piece_detail->name : 0;
                $data['piece_id']         = !empty($piece_detail) ? $piece_detail->id : 0;
            }
        }
        return $data;
    }

    /**
     * 查询部门关联的大区、片区、网点
     * @param $departmentIds
     * @return array
     */
    public function getDepartmentRelateRegionPieceStore($departmentIds)
    {
        $result = [
            'region_id' => [],
            'piece_id'  => [],
            'store_id'  => [],
        ];

        $data = HrOrganizationDepartmentStoreRelationModel::find([
            'conditions' => "department_id in ({department_ids:array}) and is_deleted = :deleted: and state = :state: and level_state = :level_state:",
            'bind'       => [
                'department_ids' => $departmentIds,
                'deleted'        => HrOrganizationDepartmentStoreRelationModel::IS_DELETED_NO,
                'state'          => HrOrganizationDepartmentStoreRelationModel::STATE_YES,
                'level_state'    => HrOrganizationDepartmentStoreRelationModel::LEAVE_STATE_YES,
            ],
        ])->toArray();
        if (empty($data)) {
            return $result;
        }
        foreach ($data as $item) {
            if (!in_array($item['region_id'], $result['region_id'])) {
                $result['region_id'][] = $item['region_id'];
            }
            if (!in_array($item['piece_id'], $result['piece_id'])) {
                $result['piece_id'][] = $item['piece_id'];
            }
            if (!in_array($item['store_id'], $result['store_id'])) {
                $result['store_id'][] = $item['store_id'];
            }
        }
        return $result;
    }


    /**
     * @description 查询部门关联的网点
     * @param $items
     * @param $type
     * @return array
     */
    public function getManageOrganizationByRelationInfo($items, $type): array
    {
        if (empty($items)) {
            return [];
        }

        $bind = [
            'deleted'        => HrOrganizationDepartmentStoreRelationModel::IS_DELETED_NO,
            'state'          => HrOrganizationDepartmentStoreRelationModel::STATE_YES,
            'level_state'    => HrOrganizationDepartmentStoreRelationModel::LEAVE_STATE_YES,
            'items_ids'      => $items
        ];
        $conditions = 'is_deleted = :deleted: and state = :state: and level_state = :level_state: and ';

        switch ($type) {
            case StaffServer::MANAGE_ORG_TYPE_DEPARTMENT:
                $conditions .= 'department_id in ({items_ids:array})';
                break;
            case StaffServer::MANAGE_ORG_TYPE_REGION:
                $conditions .= 'region_id in ({items_ids:array})';
                break;
            case StaffServer::MANAGE_ORG_TYPE_PIECE:
                $conditions .= 'piece_id in ({items_ids:array})';
                break;
            default:
                $conditions .= 'store_id in ({items_ids:array})';
                break;
        }

        return HrOrganizationDepartmentStoreRelationModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();
    }

    /**
     * @param $store_id
     * @return mixed
     */
    public function getOrganizationRegionPiece($store_id)
    {
        $bind = [
            'deleted'        => HrOrganizationDepartmentStoreRelationModel::IS_DELETED_NO,
            'state'          => HrOrganizationDepartmentStoreRelationModel::STATE_YES,
            'level_state'    => HrOrganizationDepartmentStoreRelationModel::LEAVE_STATE_YES,
            'store_id'       => $store_id
        ];
        return HrOrganizationDepartmentStoreRelationModel::findFirst([
            'conditions' => 'is_deleted = :deleted: and state = :state: and level_state = :level_state: and store_id = :store_id:',
            'bind'       => $bind,
        ]);
    }

    /**
     * @description 获取管辖的全部子部门
     * @param $department_ids
     * @return array
     */
    public function getManageSubDepartmentList($department_ids): array
    {
        $result = [];
        $model  = new SysDepartmentModel();
        foreach ($department_ids as $item) {
            $tmpDepartmentIds = $model->getSpecifiedDeptAndSubDept($item);
            $result           = array_merge($result, $tmpDepartmentIds);
        }
        return array_values(array_unique($result));
    }

    /**
     * 根据部门id,获取该部门下的大区片区网点负责人。
     * @param $departmentIds
     * @return array
     */
    public function getManagerByDepartmentId($departmentIds)
    {
        if(empty($departmentIds) || !is_array($departmentIds)) {
            return [];
        }

        $orgList = (new HrOrganizationDepartmentRelationStoreRepository($this->timezone))->getDepartmentRelateRegionPieceStore($departmentIds);

        $manager = [];
        if(!empty($orgList['region_id'])) {
            $regionList = SysManageRepository::getRegionList(['ids' => $orgList['region_id']]);
            if(!empty($regionList)) {
                $manager = array_merge($manager, array_column($regionList, 'manager_id'));
            }
        }

        if(!empty($orgList['piece_id'])) {
            $pieceList = SysManageRepository::getPieceList(['ids' => $orgList['piece_id']]);
            if(!empty($pieceList)) {
                $manager = array_merge($manager, array_column($pieceList, 'manager_id'));
            }

        }

        if(!empty($orgList['store_id'])) {
            $storeList = SysStoreRepository::getStoreListById($orgList['store_id'], ['manager_id']);
            if(!empty($storeList)) {
                $manager = array_merge($manager, array_column($storeList, 'manager_id'));
            }
        }

        return array_values(array_unique($manager));
    }

    /**
     * 根据组织负责人，获取负责网点
     * @param $staffInfoId
     * @return array
     */
    public function getManageStoreByOaOrg($staffInfoId)
    {
        $regionList = SysManageRepository::getRegionList(['manager_id' => $staffInfoId]);
        if(!empty($regionList)) {
            $regionIds = array_column($regionList, 'id');
            $storeRelation = $this->getManageOrganizationByRelationInfo($regionIds, StaffServer::MANAGE_ORG_TYPE_REGION);
            if(!empty($storeRelation)) {
                return array_column($storeRelation, 'store_id');
            }
        }

        $pieceList = SysManageRepository::getPieceList(['manager_id' => $staffInfoId]);
        if(!empty($pieceList)) {
            $pieceIds = array_column($pieceList, 'id');
            $storeRelation = $this->getManageOrganizationByRelationInfo($pieceIds, StaffServer::MANAGE_ORG_TYPE_PIECE);
            if(!empty($storeRelation)) {
                return array_column($storeRelation, 'store_id');
            }
        }

        $pieceList = SysStoreRepository::getManageStoreList($staffInfoId, 'id');
        if(!empty($pieceList)) {
            $pieceIds = array_merge($pieceList, array_column($pieceList, 'id'));
            $storeRelation = $this->getManageOrganizationByRelationInfo($pieceIds, StaffServer::MANAGE_ORG_TYPE_PIECE);
            if(!empty($storeRelation)) {
                return array_column($storeRelation, 'store_id');
            }
        }

        return [];
    }
}