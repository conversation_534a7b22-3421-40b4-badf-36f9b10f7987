<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2020/4/8
 * Time: 下午2:38
 */
namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\backyard\SettingEnvModel;

class BySettingRepository extends BaseRepository{

    public function initialize(){
        parent::initialize();
    }

    /**
     * 获取多个配置项
     * @param array $codes
     * @return array
     */
    public static function getMultiEnvByCode(array $codes = []): array
    {
        if (empty($codes) || !is_array($codes)) {
            return [];
        }

        $result = SettingEnvModel::find([
            'conditions' => "code in ({codes:array})",
            'bind'       => ['codes' => $codes],
            'columns'    => ['set_val', 'code'],
        ])->toArray();
        return array_column($result, 'set_val', 'code');
    }

    /**
     *
     * 根据code 获取对应配置
     * @param $code
     */
    public function get_setting($code){
        $sql = "select set_val from setting_env where code = '{$code}'";
        $db = $this->getDI()->get('db_rby');
        return $db->fetchColumn($sql);
    }


    /**
     *
     * fle 获取 对应的系统配置信息 考勤迁移用
     * @param $code
     * @return mixed
     */
    public function get_fle_setting($code){
        $redis = $this->getDI()->get('redisLib');
        //优先缓存
        $key = 'fle_config_'.$code;
        $cache_data = $redis->get($key);
        if(empty($cache_data)){
            $sql = "select cfg_value from sys_configuration where cfg_key = '{$code}'";
            $db = $this->getDI()->get('db_fle');
            $cache_data = $db->fetchColumn($sql);
            $redis->set($key, $cache_data, 10 * 60);
        }
        return $cache_data;
    }

}