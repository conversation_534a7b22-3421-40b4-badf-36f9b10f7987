<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2019/6/3
 * Time: 下午8:07
 */

namespace FlashExpress\bi\App\Repository;

class StaffAuditToolLog extends BaseRepository{

    public function initialize(){
        parent::initialize();
    }

    /**
     * 所有申请 操作 日志工作流记录表
     * @param array $insert  操作记录
     * @param string $action  操作木块
     * @return bool|string
     */
    public function add_log($insert,$action){
        if(empty($insert) || empty($action))
            return false;

        try {
            $insertSql  = $this->getInsertDbSql('staff_audit_tool_log', $insert);
            $this->getDI()->get('db')->query($insertSql);
            $last_id = $this->getDI()->get('db')->lastInsertId();
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("staffAuditToolLog:add_log-" . $e->getMessage());
            $last_id = '';
        }
        return $last_id;
    }

    /**
     * 所有申请 操作 日志工作流记录表
     * @param array $insert  操作记录
     * @param string $action  操作木块
     * @return bool
     */
    public function batch_add_log($insert, $action){
        if(empty($insert) || empty($action))
            return false;

        try {
            $this->batch_insert('staff_audit_tool_log', $insert);
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("StaffAuditToolLog:batch_add_log-" . $e->getMessage());
        }
        return true;
    }

    /**
     * 获取审批记录
     */
    public function getAuditRecords($paramIn = [])
    {
        $id       = $paramIn['id'];
        $type     = $paramIn['type'];

        $sql = "
            --
            select 
                staff_id
                ,type
                ,original_type
                ,to_status_type
                ,original_id
                ,operator
                ,operator_name
                ,CONVERT_TZ(created_at, '+00:00', '{$this->timezone}' ) AS created_at
            from staff_audit_tool_log
            where original_id = {$id} 
            and type = {$type} order by created_at asc";

        $data = $this->getDI()->get('db')->query($sql);
        return $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }
}
