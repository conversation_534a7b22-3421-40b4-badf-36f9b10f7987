<?php
/**
 * Author: Bruce
 * Date  : 2021-11-18 15:27
 * Description:
 */

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\ExtinguisherBaseInfoModel;
use FlashExpress\bi\App\Models\backyard\ExtinguisherCheckListModel;
use FlashExpress\bi\App\Models\backyard\ExtinguisherStaffRelationModel;
use FlashExpress\bi\App\Models\backyard\ExtinguisherTaskFileModel;
use FlashExpress\bi\App\Models\backyard\ExtinguisherTaskModel;
use FlashExpress\bi\App\Models\backyard\ExtinguisherTaskStaffRelationModel;
use FlashExpress\bi\App\library\enums;

class ExtinguisherRepository extends BaseRepository
{
    public $timezone;

    public function __construct($timezone)
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    /**
     * 查询未检查任务信息
     * @param $where
     * @return mixed
     */
    public function getNotCheckTaskInfo($where)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['et.id', 'et.status', "DATE_FORMAT(CONVERT_TZ(et.created_at, '+00:00', '{$this->timezone}'), '%Y-%m-%d %H:%i:%s') AS created_at"]);
        $builder->from(['et' => ExtinguisherTaskModel::class]);
        $builder->leftJoin(ExtinguisherTaskStaffRelationModel::class, 'etsr.extinguisher_task_id = et.id', 'etsr');
        $builder->where('et.check_month = :check_month: and et.deleted = :deleted: and et.status = 1 and  etsr.staff_id = :staff_id:', ['check_month' => $where['check_month'], 'staff_id' => $where['staff_id'], 'deleted' => enums::IS_DELETED_NO]);
        $builder->groupby('et.extinguisher_code');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 查询任务列表信息（如果灭火器信息删除，已检查的任务信息不展示）
     * @param $where
     * @return array
     */
    public function getCheckTaskListInfo($where)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['et.id as task_id', 'et.extinguisher_info_id', 'et.extinguisher_code', 'et.status', "DATE_FORMAT(CONVERT_TZ(et.created_at, '+00:00', '{$this->timezone}'), '%Y-%m-%d %H:%i:%s') AS created_at"]);
        $builder->from(['et' => ExtinguisherTaskModel::class]);
        $builder->leftJoin(ExtinguisherTaskStaffRelationModel::class, 'etsr.extinguisher_task_id = et.id', 'etsr');
        $builder->where('et.check_month = :check_month: and et.deleted = :deleted:', ['check_month' => $where['check_month'], 'deleted' => enums::IS_DELETED_NO]);
        if(!empty($where['staff_id'])){
            $builder->andwhere('etsr.staff_id = :staff_id:', ['staff_id' => $where['staff_id']]);
        }
        if(!empty($where['extinguisher_code'])){
            $builder->andwhere('et.extinguisher_code = :extinguisher_code:', ['extinguisher_code' => $where['extinguisher_code']]);
        }
        $builder->groupby('et.extinguisher_code');
        $builder->orderBy('et.id desc');
        $result = $builder->getQuery()->execute();
        return $result ? $result->toArray() : [];
    }

    /**
     * 查询灭火器信息
     * @param $where
     * @return array
     */
    public function getExtinguisherInfo($where)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['et.id as task_id', 'photo_url', 'ebi.store_id', 'ebi.type', 'coordinate', 'longitude', 'latitude', 'check_time as last_check_time']);
        $builder->from(['ebi' => ExtinguisherBaseInfoModel::class]);
        $builder->join(ExtinguisherTaskModel::class, 'ebi.id = et.extinguisher_info_id', 'et');
        $builder->where('ebi.id = :extinguisher_id:', ['extinguisher_id' => $where['extinguisher_info_id']]);
        $builder->andwhere('check_month = :check_month:', ['check_month' => $where['check_month']]);
        $builder->andwhere('et.deleted = 0');
        $result = $builder->getQuery()->getSingleResult();
        return $result ? $result->toArray() : [];
    }

    /**
     * 查询灭火器信息根据ID
     * @param $where
     * @return array
     */
    public function getExtinguisherInfoById($where)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['id', 'store_id', 'extinguisher_code', 'asset_code', 'type', 'coordinate', 'longitude', 'latitude', 'weight', 'photo_url', 'operator_id', 'deleted']);
        $builder->from(ExtinguisherBaseInfoModel::class);
        $builder->where('id = :extinguisher_id:', ['extinguisher_id' => $where['extinguisher_id']]);
        $result = $builder->getQuery()->getSingleResult();
        return $result ? $result->toArray() : [];
    }

    /**
     * 按照任务id查询任务信息
     * @param $where
     * @return mixed
     */
    public function getTaskInfoById($where)
    {
        $taskInfo = ExtinguisherTaskModel::findFirst([
            'conditions' => 'id = :task_id:',
            'bind' => [
                'task_id' =>  $where['task_id']
            ],
            'columns' => ['id', 'extinguisher_info_id', 'check_time as last_check_time', 'remark']
        ]);
        return empty($taskInfo) ? [] : $taskInfo->toArray();
    }

    /**
     * 按照任务id查询检查单文件信息
     * @param $where
     * @return mixed
     */
    public function getTaskFileInfo($where)
    {
        return ExtinguisherTaskFileModel::find([
            'conditions' => 'extinguisher_task_id = :task_id: and deleted = 0',
            'bind' => [
                'task_id' =>  $where['task_id'],
            ],
            'columns' => ['bucket_name', 'object_key', 'file_name', 'type']
        ])->toArray();
    }

    /**
     * 按照任务id查询检查单信息
     * @param $where
     * @return mixed
     */
    public function getTaskCheckList($where)
    {
        return ExtinguisherCheckListModel::find([
            'conditions' => 'extinguisher_task_id = :task_id: and deleted = 0',
            'bind' => [
                'task_id' =>  $where['task_id'],
            ],
            'columns' => ['problem_key', 'content', 'type']
        ])->toArray();
    }

    /**
     * 查询指定任务未删除文件信息
     * @param $where
     * @return mixed
     */
    public function getTaskFileInfoByTaskId($where)
    {
        return ExtinguisherTaskFileModel::find([
            'conditions' => 'extinguisher_task_id = :task_id: and deleted = 0',
            'bind' => [
                'task_id' =>  $where['task_id']
            ],
            'columns' => ['id']
        ])->toArray();
    }

    /**
     * 查询灭火器管理人员与灭火器关联信息
     * @param $where
     * @return mixed
     */
    public function getExtinguisherStaffRelation($where)
    {
        return ExtinguisherStaffRelationModel::find([
            'conditions' => 'extinguisher_info_id = :extinguisher_id: and deleted = 0',
            'bind' => [
                'extinguisher_id' =>  $where['extinguisher_id'],
            ],
            'columns' => ['staff_id']
        ])->toArray();
    }
    /**
     * 查询灭火器管理人员与任务关联信息。
     * @param $where
     * @return mixed
     */
    public function getExtinguisherTaskStaffRelation($where)
    {
        return ExtinguisherTaskStaffRelationModel::find([
            'conditions' => 'extinguisher_task_id = :task_id: and deleted = 0',
            'bind' => [
                'task_id' =>  $where['task_id'],
            ],
            'columns' => ['staff_id']
        ])->toArray();
    }


    /**
     * 查询灭火器管理人员与任务关联信息。
     * @param $where  条件
     * @return bool
     */
    public function getExtinguisherTaskStaffRelationExist(array $where)
    {

        $bool     = false;
        $relation = ExtinguisherTaskStaffRelationModel::findFirst([
            'conditions' => 'staff_id = :staff_id: and deleted = 0',
            'bind'       => [
                'staff_id' => $where['staff_id'],
            ]
        ]);
        if (!empty($relation)) {
            $bool = true;
        }
        return $bool;
    }




}
