<?php

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\HubOutsourcingOvertimeDetailModel;

class HubOutsourcingOvertimeDetailRepository extends BaseRepository
{
    public $timezone;

    public function __construct($timezone)
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    /**
     * 插入外协员工加班数据
     * @Access  public
     * @param array $paramIn
     * @return int
     */
    public function addOutsourcingOTDetail(array $paramIn = []): int
    {
        $insertSql = $this->getInsertDbSql('hub_outsourcing_overtime_detail', $paramIn);
        $this->getDI()->get('db')->query($insertSql);
        return $this->getDI()->get('db')->lastInsertId();
    }

    /**
     * 根据ot_id获取详细信息
     * @param int $ot_id
     * @return array
     */
    public function getOutsourcingOTDetailByOtId(int $ot_id = 0): array
    {
        if (0 >= $ot_id) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(
            "main.id,
            main.serial_no,
            main.hub_outsourcing_overtime_id,
            main.staff_id,
            CONVERT_TZ(created_at,'+00:00', '".$this->timezone."' ) AS created_at, 
            CONVERT_TZ(updated_at,'+00:00', '".$this->timezone."' ) AS updated_at");
        $builder->from(['main' => HubOutsourcingOvertimeDetailModel::class]);
        $builder->andWhere("main.hub_outsourcing_overtime_id = :hub_outsourcing_overtime_id:",
            ['hub_outsourcing_overtime_id' => $ot_id]);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 翻页查询
     * @param array $params
     * @param array $columns
     * @param bool $isCount
     * @return mixed
     */
    public function getOutsourcingOTDetailQuery(array $params, array $columns = [], bool $isCount = false)
    {
        $column_str = empty($columns) ? "*" : implode(',', $columns);
        $builder    = $this->modelsManager->createBuilder();
        $builder->columns($column_str);
        $builder->from(HubOutsourcingOvertimeDetailModel::class);

        $whereString = 'hub_outsourcing_overtime_id = :hub_outsourcing_overtime_id: ';
        if (is_array($params['hub_outsourcing_overtime_id'])) {
            $whereString = 'hub_outsourcing_overtime_id IN ({hub_outsourcing_overtime_id:array}) ';
        }
        $builder->where($whereString, ['hub_outsourcing_overtime_id' => $params['hub_outsourcing_overtime_id']]);

        if ($isCount) {
            $builder->columns($column_str);
            return $builder->getQuery()->getSingleResult()->toArray();
        }

        if (empty($params['is_all'])) {
            $builder->limit($params['page_size'], $params['page_size'] * ($params['page'] - 1));
        }
        $builder->orderBy('id DESC');
        return $builder->getQuery()->execute()->toArray();
    }

}