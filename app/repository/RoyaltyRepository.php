<?php

namespace FlashExpress\bi\App\Repository;

use function GuzzleHttp2\Psr7\str;

class RoyaltyRepository extends BaseRepository
{
    public $timezone;

    public function __construct($timezone)
    {
        parent::__construct();
        $this->timezone = $timezone;
        $this->db_rbi = $this->getDI()->get('db_rbi');
    }

    public function getRoyaltyInfo($paramIn = []){

        $returnData = array();

        //员工id
        $staffId  = $paramIn['staff_id'];

        //今日
        $today = date("Y-m-d");
        //近60天
        $oldday = date("Y-m-d",strtotime("-60 day"));
        //昨天
        $yesterday = date("Y-m-d",strtotime("-1 day"));
        //本月
        $month = date("Y-m");

        $sql = "--
                SELECT
                    user_referer_stat.stat_date,
                    user_referer_stat.total_extract,
                    user_referer_stat.amount,
                    user_referer_stat.per_extract,
                    user_referer.staff_info_id,
                    user_referer.user_mobile,
                    user_referer.user_type,
					user_referer.user_id
                FROM
                    user_referer_stat 
                    right join user_referer on 
                    user_referer_stat.user_id = user_referer.user_id AND 
                    user_referer_stat.stat_date >= '".$oldday."' AND 
                    user_referer_stat.stat_date <= '".$today."'
                WHERE
                    user_referer.staff_info_id = '".$staffId."'";
        $obj     = $this->db_rbi->query($sql);
        $royaltyData    = $obj->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        $todayRoyaltyData = $yesterdayRoyaltyData = $monthRoyaltyData = $userRoyaltyList = array();
        foreach ($royaltyData as $k=>$v){
            //类型
            $userRoyaltyList[$v['user_id']]['user_type'] = $v['user_type'];
            //用户电话
            $userRoyaltyList[$v['user_id']]['user_mobile'] = $v['user_mobile'];
            //用户id
            $userRoyaltyList[$v['user_id']]['user_id'] = $v['user_id'];
            //用户今日提成数
            $userRoyaltyList[$v['user_id']]['todayRoyaltyNum'] = isset($userRoyaltyList[$v['user_id']]['todayRoyaltyNum'])?$userRoyaltyList[$v['user_id']]['todayRoyaltyNum']:0;
            //用户昨日发货量
            $userRoyaltyList[$v['user_id']]['yesterdayRoyaltyNum'] = isset($userRoyaltyList[$v['user_id']]['yesterdayRoyaltyNum'])?$userRoyaltyList[$v['user_id']]['yesterdayRoyaltyNum']:0;
            //用户本月累积发货量
            $userRoyaltyList[$v['user_id']]['monthRoyaltyNum'] = isset($userRoyaltyList[$v['user_id']]['monthRoyaltyNum'])?$userRoyaltyList[$v['user_id']]['monthRoyaltyNum']:0;
            if ($v['stat_date'] == $today) {
                //今日提成数组
                $todayRoyaltyData[]=$v['total_extract'];
                $userRoyaltyList[$v['user_id']]['todayRoyaltyNum'] += $v['amount'];
            }
            if ($v['stat_date'] == $yesterday){
                //昨日提成数组
                $yesterdayRoyaltyData[]=$v['total_extract'];
                $userRoyaltyList[$v['user_id']]['yesterdayRoyaltyNum'] += $v['amount'];
            }
            if (strpos($v['stat_date'],$month) !== false && $v['stat_date'] !=$today){
                //本月提成数组
                $monthRoyaltyData[]=$v['total_extract'];
                $userRoyaltyList[$v['user_id']]['monthRoyaltyNum'] += $v['amount'];
            }

        }
        $returnData['UserRoyaltyList'] = array_values($userRoyaltyList);
        //今日提成
        $todayRoyaltyNum = array_sum($todayRoyaltyData);
        unset($todayRoyaltyData);

        //昨日提成
        $yesterdayRoyaltyNum = array_sum($yesterdayRoyaltyData);
        unset($yesterdayRoyaltyData);

        //本月累积发货提成
        $monthRoyaltyNum = array_sum($monthRoyaltyData);
        unset($monthRoyaltyData);

        //累积推荐客户数
        $sql = "--
                SELECT
                     count( 1 ) AS num 
                FROM
                    user_referer 
                WHERE
                    staff_info_id = '".$staffId."'";
        $obj     = $this->db_rbi->query($sql);
        $data    = $obj->fetch(\Phalcon\Db::FETCH_ASSOC);
        $returnData['userRefererCountNum'] = $data['num']?$data['num']:0;

        //累积发货推荐客户数
        $sql = "--
                SELECT
                    count( 1 ) AS num 
                FROM
                    user_referer 
                WHERE
                    user_id IN ( SELECT user_id FROM user_referer_stat WHERE staff_info_id = '".$staffId."' )";
        $obj     = $this->db_rbi->query($sql);
        $data    = $obj->fetch(\Phalcon\Db::FETCH_ASSOC);
        $returnData['userRefererStatCountNum'] = $data['num']?$data['num']:0;

        //今日提成数
        $returnData['todayRoyaltyNum'] = $todayRoyaltyNum?$todayRoyaltyNum:0;
        //昨日提成数
        $returnData['yesterdayRoyaltyNum'] = $yesterdayRoyaltyNum?$yesterdayRoyaltyNum:0;
        //本月累积发货提成数
        $returnData['monthRoyaltyNum'] = $monthRoyaltyNum?$monthRoyaltyNum:0;

        return $returnData;
    }
}