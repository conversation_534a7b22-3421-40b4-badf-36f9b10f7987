<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrJobDepartmentRelationModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrProbationModel;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\SysCityModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysProvinceModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\ToolStaffInfoModel;
use FlashExpress\bi\App\Models\fle\FleSysDepartmentModel;
use FlashExpress\bi\App\Models\oa\SettingEnvModel;
use FlashExpress\bi\App\Server\StaffServer;

class StaffRepository extends BaseRepository
{

    //批量查询 和 单个查询员工信息用到的字段
    protected $staffColumns = "
                s.staff_info_id
                ,s.staff_info_id id
                ,s.`name`
                ,s.`name_en`
                ,s.`middle_name`
                ,s.`first_name`
                ,s.`last_name`
                ,s.`suffix_name`
                ,s.nick_name
                ,s.job_title
                ,j.id job_id
                ,j.job_name
                ,s.sys_department_id 
                ,s.node_department_id 
                ,node_dep.name depart_name
                ,node_dep.ancestry_v3
                ,node_dep.type dep_type
                ,node_dep.company_id        
                ,s.sys_store_id
                ,if(s.sys_store_id='-1',2,1) as organization_type
                ,s.hire_date
                ,s.leave_date
                ,s.sex
                ,s.bank_no
                ,s.identity
                ,s.mobile
                ,s.hire_type
                ,s.staff_type
                ,s.signing_date
                ,s.mobile_company
                ,s.formal
                ,s.manger
                ,s.email
                ,s.personal_email
                ,s.bank_type
                ,s.state
                ,s.week_working_day
                ,s.job_title_grade
                ,s.job_title_grade_v2
                ,s.job_title_level
                ,pro.status
                ,pro.status as probation_status
                ,pro.id as probation_id
                ,pro.formal_at
                ,s.fund_num
                ,s.social_security_num
                ,s.medical_insurance_num
                ,s.wait_leave_state
                ,s.is_sub_staff           
                ,s.rest_type
                ,s.nationality
                ,s.working_country
                ,s.contract_company_id
                ,store.category
                ,store.name as store_name
                ,s.contract_company_id
                ";


    public function initialize()
    {

        parent::initialize();
    }

    /**
     * @var array
     * 职等1.Staff 2.Supervisor 3.Manager 4.Executive
     * “职等”
     */

    const JOB_TITLE_LEVEL_STAFF = 1;
    const JOB_TITLE_LEVEL_SUPER = 2;
    const JOB_TITLE_LEVEL_MANAGER = 3;
    const JOB_TITLE_LEVEL_EXECUTIVE = 4;
    const GRADE_19 = 19;
    const GRADE_18 = 18;//老挝的是 18以上的
    public static $level = array(
        self::JOB_TITLE_LEVEL_STAFF => 'Staff',
        self::JOB_TITLE_LEVEL_SUPER => 'Supervisor',
        self::JOB_TITLE_LEVEL_MANAGER => 'Manager',
        self::JOB_TITLE_LEVEL_EXECUTIVE => 'Executive',
    );


    public static $month_th = array(
        1 => 'มกราคม',
        2 => 'กุมภาพันธ์',
        3 => 'มีนาคม',
        4 => 'เมษายน',
        5 => 'พฤษภาคม',
        6 => 'มิถุนายน',
        7 => 'กรกฎาคม',
        8 => 'สิงหาคม',
        9 => 'กันยายน',
        10  => 'ตุลาคม',
        11  => 'พฤศจิกายน',
        12  => 'ธันวาคม',
    );
    public static $month_en = array(
        1 => 'January',
        2 => 'February',
        3 => 'March',
        4 => 'April',
        5 => 'May',
        6 => 'June',
        7 => 'July',
        8 => 'August',
        9 => 'September',
        10  => 'October',
        11  => 'November',
        12  => 'December',
    );

    /**
     * 员工校验
     * @Access  public
     * @Param   $staffId 员工ID
     * @Return  array 员工信息
     */
    public function checkoutStaff($staffId = '', $returnType = 0)
    {
        $returnData = [];
        if (!empty($staffId)) {
            $staffInfoSql  = "SELECT * FROM `staff_info` WHERE `id` = :id" ;
            $data          = $this->getDI()->get('db_fle')->query($staffInfoSql,['id'=>$staffId]);
            $staffInfoData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
            $returnData    = $staffInfoData;
        } else {
            $staffInfoSql  = "SELECT id,`name` FROM `staff_info`";
            $data          = $this->getDI()->get('db_fle')->query($staffInfoSql);
            $staffInfoData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if ($returnType == 1) {
                $returnData = $staffInfoData;
            } else {
                $staffArr = [];
                foreach ($staffInfoData as $k => $v) {
                    $staffArr[$v['id']] = $v['name'];
                }
                $returnData = $staffArr;
            }
        }
        return $returnData;
    }

    /**
     * 员工校验
     * @Access  public
     * @Param   $staffId 员工ID
     * @Return  array 员工信息
     */
    public function checkoutStaffBi($staffId = '')
    {
        $returnData = [];
        if (!empty($staffId)) {
            $staffInfoSql  = "SELECT * FROM `hr_staff_info` WHERE `staff_info_id` ='" . $staffId . "'";
            $data          = $this->getDI()->get('db_rby')->query($staffInfoSql);
            $staffInfoData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
            $returnData    = $staffInfoData;
        }
        return $returnData;
    }

    /**
     * @description 获取员工信息
     * @param $staffId
     * @return array
     */
    public function checkoutStaffById($staffId = '')
    {
        if (empty($staffId)) {
            return [];
        }
        $staffInfoData = HrStaffInfoModel::findFirst([
            "staff_info_id = :staff_info_id:",
            "bind" => [
                "staff_info_id" => $staffId
            ]
        ]);

        return $staffInfoData ? $staffInfoData->toArray(): [];
    }

    /**
     * @param $data
     * @return mixed
     */
    public  function getstaffList($data){
         $sql = "SELECT
            hrs.staff_info_id ,
            hrs.name_en,
            hrs.name,
            ss.name as store_name
        FROM
            hr_staff_info AS hrs
            LEFT JOIN sys_store AS ss ON hrs.sys_store_id = ss.id
            where hrs.staff_info_id = :staff_id or hrs.name like :name limit 10";
         $stmt= $this->getDI()->get('db_rby')->prepare($sql);
         $stmt->bindValue(":staff_id", $data['staff_id']);
         $stmt->bindValue(":name", '%'.$data['staff_id'].'%');
         $stmt->execute();
         $rs = $stmt->fetchAll(\PDO::FETCH_ASSOC);
         return $rs;
    }

    /**
     * 检查员工
     * 是否在职
     *
     * @param $staffId
     * @return bool
     *
     */
    public function isOnJob($staffId)
    {
        $staffInfo = $this->checkoutStaffBi($staffId);
        return isset($staffInfo['state']) && $staffInfo['state'] == HrStaffInfoModel::STATE_ON_JOB;
    }

    /**
     * @description 检查员工是否在职
     * @param $staffId
     * @return bool
     */
    public function isStaffStateOnJob($staffId): bool
    {
        $staffInfo = $this->checkoutStaffById($staffId);
        return isset($staffInfo['state']) && $staffInfo['state'] == HrStaffInfoModel::STATE_ON_JOB;
    }

    /**
     * @description 检查员工是否离职
     * @param $staffId
     * @return bool
     */
    public function isStaffStateResign($staffId): bool
    {
        $staffInfo = $this->checkoutStaffById($staffId);
        return isset($staffInfo['state']) && $staffInfo['state'] == HrStaffInfoModel::STATE_RESIGN;
    }

    /**
     * 检查多个员工是否离职
     * @param $staffIds
     * @return bool
     */
    public function isMultiStaffsOnJob($staffIds): bool
    {
        $returnData = false;
        if (!empty($staffIds) && is_array($staffIds)) {

            //停职不转交
            $info = HrStaffInfoModel::findFirst([
                'conditions' => "staff_info_id IN({staff_info_id:array}) and state IN(1)",
                'bind' => [
                    'staff_info_id' => $staffIds
                ],
                'columns' => 'count(staff_info_id) as cou'
            ]);
            if (!empty($info)) {
                $returnData = $info->cou == 0; //全部离职
            }
        }
        return $returnData;
    }


    /**
     * 获取上级
     *
     * @param $staffId
     * @return \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model|string
     *
     */
    public function getHightManager($staffId)
    {
        $staffItem = HrStaffItemsModel::findFirst([
            'conditions' => "staff_info_id = ?1 and item = 'MANGER' ",
            'bind' => [
                1 => $staffId
            ]
        ]);
        if ($staffItem) {
            return $staffItem->value;
        }

        return '';
    }

    /**
     * 获取上级
     *
     * @param $id
     * @return array
     */
    public function getHigherDeptManager($id): array
    {
        //获取部门信息
        $deptInfo = FleSysDepartmentModel::findFirst([
            'conditions' => "id = ?1 and deleted = 0",
            'bind' => [
                1 => $id
            ]
        ]);
        if ($deptInfo && !in_array($deptInfo->type, [4, 5])) {

            //获取部门上级部门的部门信息
            $ancestryInfo = FleSysDepartmentModel::findFirst([
                'conditions' => "id = ?1 and deleted = 0",
                'bind' => [
                    1 => $deptInfo->ancestry
                ]
            ]);
            return [!empty($ancestryInfo) ? $ancestryInfo->manager_id : '', $deptInfo->ancestry, $ancestryInfo->id];
        }
        return ['', '', ''];
    }

    /**
     * 获取员工职位
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getStaffPosition($staffId = '')
    {
        $returnData = [];
        if(!empty($staffId))
        {
                $staffInfoSql  = "SELECT 
                              {$this->staffColumns}
                              FROM `hr_staff_info` s
                              left join sys_department node_dep on s.node_department_id = node_dep.id
                              left join hr_probation pro on s.staff_info_id = pro.staff_info_id
                              left join hr_job_title j on s.job_title = j.id
                              left join sys_store store on s.sys_store_id = store.id
                              WHERE s.`staff_info_id` = :staff_info_id ";
            $query_param['staff_info_id'] = $staffId;
            $data          = $this->getDI()->get('db_rby')->query($staffInfoSql,$query_param);
            $staffInfoData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
            $returnData    = $staffInfoData;
        }

        return $returnData;
    }


    /**
     * 获取员工职位
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getStaffsPosition($staffIds)
    {
        $returnData = [];
        if(!empty($staffIds))
        {
            $staffInfoSql  = "SELECT 
                              {$this->staffColumns}
                              FROM `hr_staff_info` s
                              left join hr_job_title j on s.job_title = j.id
                              left join sys_department node_dep on s.sys_department_id = node_dep.id
                              left join hr_probation pro on s.staff_info_id = pro.staff_info_id
                              left join sys_store store on s.sys_store_id = store.id
                              WHERE s.`staff_info_id` in (" . implode(',', $staffIds) . ") ";
            $data          = $this->getDI()->get('db_rby')->query($staffInfoSql);
            $staffInfoData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $returnData    = $staffInfoData;
        }
        return $returnData;
    }
    /**
     * 获取员工职位详情
     * @param string $staffId
     * @return array
     */
    public function getStaffpositionV2($staffId = '')
    {
        $returnData = [];
        if (!empty($staffId)) {
            $staffInfoSql  = "
                --
                SELECT 
                    si.id,
                    si.id as staff_info_id,
                    si.name name                -- 姓名
                    ,sd.name department_name    -- 部门
                    ,sij.name job_name          -- 职位
                    ,si.hire_date
                    ,si.state
                    ,organization_type
                    ,organization_id
                    ,job_title
                    ,formal
                    ,si.department_id
                    ,si.is_sub_staff
                    ,si.hire_type
                FROM `staff_info` si
                LEFT JOIN staff_info_job_title sij on sij.id = si.job_title
                LEFT JOIN sys_department sd on si.department_id = sd.id
                WHERE `si`.`id` = {$staffId}";
            $data          = $this->getDI()->get('db_fle')->query($staffInfoSql);
            $staffInfoData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
            $returnData    = $staffInfoData;
        }
        return $returnData;
    }

    /**
     * 获取员工所在网点
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getStaffStore($staffId = '')
    {
        $returnData = [];
        if (!empty($staffId)) {
            $staffInfoSql  = "SELECT id,organization_id as store_id FROM `staff_info` WHERE `id` =" . $staffId . " and `organization_type`='1'";
            $data          = $this->getDI()->get('db_fle')->query($staffInfoSql);
            $staffInfoData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
            $returnData    = $staffInfoData ?? '';
        }
        return $returnData;
    }

    /**
     * 批量查询staff name hire_type
     */
    public function checkoutStaffBatch($staffIds)
    {
        if (empty($staffIds)) {
            return [];
        }
        $data = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id,name,hire_type',
            'conditions' => 'staff_info_id in ({ids:array})',
            'bind'       => ['ids' => $staffIds],
        ])->toArray();

        return $data;
    }

    /**
     * 获取员工所在网点对应的省市区等信息
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getStaffStoreInfo($store = '')
    {
        $returnData = [];
        if (empty($store)) {
            return $returnData;
        }

        $storeInfo = SysStoreModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $store],
        ]);
        if (empty($storeInfo)) {
            return $returnData;
        }
        $storeInfo = $storeInfo->toArray();

        $provinceInfo  = SysProvinceModel::findFirst([
            'conditions' => 'code = :code:',
            'bind'       => ['code' => $storeInfo['province_code']],
        ]);
        $storeInfo['province_manage_geography_code'] = null;
        if ($provinceInfo) {
            $storeInfo['province_manage_geography_code'] = $provinceInfo->manage_geography_code;
        }
        return $storeInfo;
    }

    /**
     * 获取员工所在网点对应的省的大区[sorting_no]
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getProvince($proveinceCode = '')
    {
        $returnData = [];
        if (!empty($proveinceCode)) {
            $sql           = " SELECT * FROM sys_province WHERE  `code`='{$proveinceCode}'";
            $data          = $this->getDI()->get('db_fle')->query($sql);
            $staffInfoData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
            $returnData    = $staffInfoData ?? '';
        }
        return $returnData;
    }

    /**
     * 获取网点下所有的员工[在职人数]
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getStaffByOrganization($organization_id = '')
    {
        $returnData = [];
        if (!empty($organization_id)) {
            //$sql           = " SELECT * FROM staff_info WHERE  `organization_id`='{$organization_id}' and  state = 1";
            $sql           = " SELECT * FROM hr_staff_info WHERE  `sys_store_id`='{$organization_id}' and  state = 1 and `is_sub_staff`  = 0 and `formal`  =1";
            $data          = $this->getDI()->get('db_rby')->query($sql);
            $staffInfoData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $returnData    = $staffInfoData ?? '';
        }
        return $returnData;
    }

    /**
     * @param $staff_ids
     * @param $date_at
     * 获取某一天员工是否休息日
     */
    public function getWorkdays($staff_ids, $date_at)
    {

        if (empty($staff_ids))
            return false;

        $id_str = implode(',', $staff_ids);
        $sql    = " select * from hr_staff_work_days where staff_info_id in ({$id_str}) and date_at = '{$date_at}'";
        $data   = $this->getDI()->get('db_rby')->query($sql);
        $data   = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $data;
    }

    public function get_work_days_between($staff_id, $start, $end){

        $sql = " select * from hr_staff_work_days where staff_info_id = {$staff_id} and date_at >= '{$start}' and date_at <= '{$end}'";
        $data = $this->getDI()->get('db_rby')->query($sql);
        $data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $data;
    }


    //删除轮休表
    public function del_workday($staff_id, $date_at){

        $sql = " delete from hr_staff_work_days where staff_info_id = {$staff_id} and date_at = '{$date_at}'";
        return $this->getDI()->get('db')->execute($sql);

    }

    //删除备份轮休表

    /**
     * 获取员工班次
     * @param $staff_id
     */
    public function get_staff_shift($staff_id)
    {
        $sql  = "select * from hr_staff_shift where staff_info_id = {$staff_id}";
        $data = $this->getDI()->get('db')->query($sql);
        $data = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $data;
    }

    /**
     * 根据日期 取班次固化数据表
     * @param $staff_id
     * @param $date
     * @return mixed
     */
    public function get_staff_shift_date($staff_id,$date)
    {
        $sql  = "select * from hr_staff_shift_history where staff_info_id = {$staff_id} and shift_day = '{$date}'";
        $data = $this->getDI()->get('db')->query($sql);
        $data = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $data;
    }

    /**
     * 获取员工职位
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getStaffPositionv3($staffId = '')
    {
        $returnData = [];
        if (!empty($staffId)) {
            $staffInfoSql  = "--
                            SELECT
                                s.id,
                                s.job_title,
                                s.organization_id,
                                s.organization_type,
                                s.department_id,
                                s.name,
                                d.name as store_name,
                                s.job_title,
                                s.state,
                                d.category,
                                d.manager_id
                            FROM
                                staff_info s
                            left join
                                sys_store d
                            on
                                s.organization_id = d.id
                            WHERE
                                s.id = {$staffId} ";
            $data          = $this->getDI()->get('db_fle')->query($staffInfoSql);
            $staffInfoData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
            $returnData    = $staffInfoData;
        }
        return $returnData;
    }

    /**
     * 获取员工详情
     * @Access  public
     * @Param   request
     * @Return  array
     * @return array
     */
    public function getStaffInfo($staffId = ''): array
    {
        $returnData = [];
        if (!empty($staffId)) {
            $staffInfoSql  = "--
                            SELECT
                                staff.id AS staff_id,
                                staff.organization_id AS store_id,
                                staff.job_title,
                                staff.department_id,
                                staff.company_name,
                                staff.`name`,
                                staff.formal,
                                staff.state,
                                staff.is_sub_staff,
                                store.`name` AS store_name,
                                store.`manage_region`,
                                store.`manage_piece`,
                                store.`category`,
                                store.`manager_id`,
                                staff.state,
                                staff.department_id,
                                GROUP_CONCAT( position.position_category ) position_category,
                                job_title.`name` AS job_title_name,
                                department.`name` AS department_name, 
                                department.`company_id` AS department_company_id, 
                                region.`name` AS manage_region_name,
                                piece.name as manage_piece_name
                            FROM
                                staff_info AS staff
                                JOIN staff_info_position AS position ON staff.id = position.staff_info_id
                                LEFT JOIN sys_store store ON staff.organization_id = store.id
                                LEFT JOIN sys_department department ON department.id = staff.department_id
                                LEFT JOIN staff_info_job_title job_title ON staff.job_title = job_title.id 
                                LEFT JOIN sys_manage_region region ON region.id = store.manage_region                                
                                LEFT JOIN sys_manage_piece piece ON piece.id = store.manage_piece and store.manage_region = piece.manage_region_id                           
                            WHERE
                                staff.id = ?";
            $data          = $this->getDI()->get('db_fle')->query($staffInfoSql,[$staffId]);
            $staffInfoData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
            $returnData    = $staffInfoData;

            $jobTitleSql = "--
                SELECT
                    job_title_level,
                    job_title_grade,
                    job_title_grade_v2,
                    hire_date,
                    sys_store_id AS hr_staff_sys_store_id,
                    node_department_id AS hr_staff_node_department_id,
                    sys_department_id AS hr_staff_sys_department_id
                FROM hr_staff_info
                WHERE staff_info_id = ? ";
            $data          = $this->getDI()->get('db_rby')->query($jobTitleSql, [$staffId]);
            $jobTitleInfoData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
            if (!empty($jobTitleInfoData)) {
                $returnData = array_merge($returnData, $jobTitleInfoData);
            }
        }
        return $returnData;
    }

    /**
     * 获取员工基本信息
     * @param $staff_info_id
     * @return array
     */
    public function getStaffInfoAllOne($staff_info_id)
    {
        $staffInfo = HrStaffInfoModel::findFirst([
            'columns'    => '
            staff_info_id,
            name,
            name_en,
            nick_name,
            state,
            wait_leave_state,
            job_title,
            sys_store_id,
            job_title_grade_v2,
            node_department_id,
            manger,
            hire_date',
            'conditions' => ' staff_info_id = :staff_id: ',
            'bind'       => [
                'staff_id' => $staff_info_id,
            ],
        ]);

        if (!$staffInfo) {
            return [];
        }

        $staffInfo = $staffInfo->toArray();

        $departmentInfo = SysDepartmentModel::findFirst([
            'columns'    => 'name',
            'conditions' => ' id = :id: ',
            'bind'       => ['id' => $staffInfo['node_department_id']],
        ]);

        $jobInfo = HrJobTitleModel::findFirst([
            'columns'    => 'job_name',
            'conditions' => ' id = :id: ',
            'bind'       => ['id' => $staffInfo['job_title']],
        ]);

        if ($staffInfo['sys_store_id'] == -1) {
            $storeName = Enums::HEAD_OFFICE;
        } else {
            $storeInfo = SysStoreModel::findFirst([
                'columns'    => 'name',
                'conditions' => ' id = :id: ',
                'bind'       => ['id' => $staffInfo['sys_store_id']],
            ]);

            $storeName = $storeInfo->name ?? '';
        }
        return [
            'staff_info_id'      => $staffInfo['staff_info_id'],
            'name'               => $staffInfo['name'] ?? '',
            'name_en'            => $staffInfo['name_en'] ?? '',
            'nick_name'          => $staffInfo['nick_name'] ?? '',
            'department_name'    => $departmentInfo->name ?? '',
            'node_department_id' => $staffInfo['node_department_id'],
            'manger'             => $staffInfo['manger'] ?? '',
            'job_title_name'     => $jobInfo->job_name ?? '',
            'store_name'         => $storeName,
            'state'              => $staffInfo['state'],
            'wait_leave_state'   => $staffInfo['wait_leave_state'],
            'sys_store_id'       => $staffInfo['sys_store_id'],
            'job_title_grade_v2' => $staffInfo['job_title_grade_v2'],
            'hire_date'          => date('Y-m-d',strtotime($staffInfo['hire_date'])),
        ];
    }

    /**
     * 批量获取员工列表
     *
     * @param $ids
     * @return array
     */
    public function getStaffNameByIds($ids)
    {
        $data = [];
        if ($ids) {
            $stmt = $this->getDI()->get('db_rby')->query("select staff_info_id, `name` from hr_staff_info where staff_info_id in (" . implode(', ', $ids) .")");
            $data = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        }
        return $data;
    }

    /**
     * 获取员工信息
     * @param $staffId
     * @return array
     */
    public function getStaffInfoById($staffId)
    {
        if ($staffId) {
            $sql = "
            --
            select
                hsi.staff_info_id,
                hsi.sys_store_id,
                ss.category,
                hsi.job_title,
                ss.name,
                hjt.job_name,
                hsi.sys_department_id,
                hsi.node_department_id,
                hsi.nick_name,
                hsi.state
            from
                hr_staff_info hsi
            left join
                hr_job_title hjt
            on
                hsi.job_title = hjt.id
            left join
                sys_store ss
            on
                hsi.sys_store_id = ss.id
            where 
                hsi.staff_info_id = " . (int)$staffId;

            return $this->getDI()->get("db_rby")->query($sql)->fetch(\PDO::FETCH_ASSOC);
        }

        return [];
    }

    public function getAvatar($staffId,$key){
        if($staffId){
            $sql = "select `value` from hr_staff_items where staff_info_id =".intval($staffId)." and item=:key";
            $arr = $this->getDI()->get("db_rby")->fetchOne($sql,\PDO::FETCH_ASSOC,["key"=>$key]);
            if(!empty($arr)){
                return $arr['value'];
            }
        }
        return "";
    }

    public function getAvatarByArgs ($staffId,$fields = []) {
        if($staffId){
            $result = HrStaffItemsModel::find([
                'conditions' => "staff_info_id = :staff_info_id: AND item in ({items:array})",
                'bind'       => array(
                    'staff_info_id' => $staffId,
                    'items'         => $fields
                ),
            ])->toArray();
            return $result;
        }
        return "";
    }

    //修改是否同意pdpa
    public function setStaffPdpaItems($staff_info_id, $key, $value = 0) {
        $sql = "INSERT INTO hr_staff_items (staff_info_id, item, `value`) VALUES(?,?,?) ON DUPLICATE KEY UPDATE `value`=? ";
        return $this->getDI()->get("db")->execute($sql,[$staff_info_id, $key , $value, $value]);
    }

    public function update_name_en($staff_id, $name_en){
        $sql = "update hr_staff_info set name_en = '{$name_en}' where staff_info_id = {$staff_id}";
        return $this->getDI()->get("db")->execute($sql);
    }

    /**
     * 获取网点负责人
     * @param $store_id
     * @return array
     */
    public function getStoreManager($store_id)
    {
        if (empty($store_id)) {
            return [];
        }
        $sql = "--
            SELECT category, manager_id FROM sys_store where id = '{$store_id}'";
        return $this->getDI()->get('db_rby')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
    }

    /**
     * 获取员工的薪资
     * @param int $staff_info_id 员工工号
     * @return array
     */
    public function getStaffSalary($staff_info_id) :array
    {
        if (empty($staff_info_id)) {
            return [];
        }

        $sql = "--
            SELECT 
                *
            FROM hr_staff_salary
            WHERE staff_info_id = :staff_info_id ";
        $arr = $this->getDI()->get("db_rby")->fetchOne($sql,\PDO::FETCH_ASSOC,["staff_info_id" => $staff_info_id]);
        return empty($arr) ? []: $arr;
    }


    //登陆用的 验证密码 接口
    public function login_info($staff_id){
        $sql = " select si.id,si.name , si.organization_id , si.organization_type , si.mobile , si.encrypted_password, si.hire_type
                                , GROUP_CONCAT(sip.position_category) position_category
                                , si.state 
                                , si.department_id
                                , si.job_title
                                , si.is_sub_staff
                                , si.formal
                                , si.error_login_num
                                , si.allowed_login_at
                 from staff_info  as si
                 join staff_info_position as sip on si.id=sip.staff_info_id
                 where si.id = :id ;
                ";
        $model = $this->getDI()->get('db_fle')->query($sql,['id' => $staff_id]);
        $info = $model->fetch(\Phalcon\Db::FETCH_ASSOC);

        if (empty($info)) {
            return null;
        }

        //查询 网点 或者总部 组织信息
        if ($info['organization_type'] == 1 && !empty($info['organization_id'])) {//网点
            $sql                       = "select lat,lng,category ,`name` from sys_store where id = '{$info['organization_id']}'";
            $res                       = $this->getDI()->get('db_fle')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
            $info['store_category']    = empty($res) ? '' : $res['category'];
            $info['store_lat']         = empty($res) ? '' : $res['lat'];
            $info['store_lng']         = empty($res) ? '' : $res['lng'];
            $info['organization_name'] = empty($res) ? '' : $res['name'];
        }

        if ($info['organization_type'] == 2 && !empty($info['department_id'])) {//总部
            $sql                       = " select `name` from sys_department where id = {$info['department_id']}";
            $res                       = $this->getDI()->get('db_fle')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
            $info['organization_name'] = empty($res) ? '' : $res['name'];
        }

        return $info;

    }

    /**
     * 获取指定部门的对应的hrbp
     * @param $department_id
     * @return
     */
    public function getDepartmentManager($department_id)
    {
        $sql = "SELECT manager_id as staff_info_id
                FROM sys_department 
                WHERE id = {$department_id}";
        $model = $this->getDI()->get('db_fle')->query($sql);
        $info = $model->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $info;
    }


    /**
     * 返回 该日期 是否是 工作日
     * @param $bi_staff_info
     * @param $date
     * @return int 0 不是  1 是  修改为 1，2，3，4 枚举 默认0
     */
    public function get_is_working_day($bi_staff_info, $date)
    {
        $working_day = StaffWorkAttendanceModel::IS_NOT_WORKING_DAY;
        if (empty($bi_staff_info)) {
            return $working_day;
        }
        //是否是工作日
        $staff_id = $bi_staff_info['staff_info_id'];

        $audit_server = $this->class_factory('AuditServer');
        $holidays     = $audit_server->get_holidays($bi_staff_info);
        $ot_re        = new OvertimeRepository($this->timezone);
        $isRest  = $ot_re->get_workdays($staff_id, $date);

        if(in_array($date, $holidays) && $isRest){//ph 和 off
            return StaffWorkAttendanceModel::WORK_DAY_PH_REST;
        }else if(!in_array($date, $holidays) && empty($isRest)){//非ph  非off
            return StaffWorkAttendanceModel::WORK_DAY_UN_PH_UN_REST;
        }else if(in_array($date, $holidays) && empty($isRest)){//ph 非 off
            return StaffWorkAttendanceModel::WORK_DAY_PH_UN_REST;
        }else if(!in_array($date, $holidays) && $isRest){//非ph  off
            return StaffWorkAttendanceModel::WORK_DAY_UN_PH_REST;
        }
        //啥也不是 返回默认
        return StaffWorkAttendanceModel::IS_NOT_WORKING_DAY;
    }


    //通过工号 获取 员工居住地址
    public function get_staff_address($staff_id){
        if(empty($staff_id))
            return '';
        //拼接顺序  门牌号 RESIDENCE_HOUSE_NUM 村号 RESIDENCE_VILLAGE_NUM 村庄 RESIDENCE_VILLAGE
        // 巷 RESIDENCE_ALLEY 街道 RESIDENCE_STREET 乡 RESIDENCE_DISTRICT 市 RESIDENCE_CITY 省 RESIDENCE_PROVINCE
        $code_list = HrStaffItemsModel::find([
            'conditions' => "staff_info_id = {$staff_id} and item in ({codes:array})",
            'bind'       => array('codes' => array(
                'RESIDENCE_PROVINCE','RESIDENCE_CITY','RESIDENCE_DISTRICT',
                'RESIDENCE_STREET','RESIDENCE_HOUSE_NUM','RESIDENCE_VILLAGE_NUM','RESIDENCE_VILLAGE',
                'RESIDENCE_ALLEY'
            )),
        ])->toArray();
        if(empty($code_list))
            return '';
        $code_list = array_column($code_list,'value','item');

        $return = empty($code_list['RESIDENCE_HOUSE_NUM']) ?? '' ;
        $return .= empty($code_list['RESIDENCE_VILLAGE_NUM']) ?? '' ;
        $return .= empty($code_list['RESIDENCE_VILLAGE']) ?? '' ;
        $return .= empty($code_list['RESIDENCE_ALLEY']) ?? '' ;
        $return .= empty($code_list['RESIDENCE_STREET']) ?? '' ;
        //找对应 省
        if(!empty($code_list['RESIDENCE_DISTRICT'])){
            $info = SysProvinceModel::findFirst("code = '{$code_list['RESIDENCE_DISTRICT']}'");
            if(!empty($info))
                $return .= $info->name;
        }

        //找对应市
        if(!empty($code_list['RESIDENCE_CITY'])){
            $info = SysCityModel::findFirst("code = '{$code_list['RESIDENCE_CITY']}'");
            if(!empty($info))
                $return .= $info->name;
        }
        //找对应乡
        if(!empty($code_list['RESIDENCE_PROVINCE'])){
            $info = SysProvinceModel::findFirst("code = '{$code_list['RESIDENCE_PROVINCE']}'");
            if(!empty($info))
                $return .= $info->name;
        }
        return $return;

    }

	/**
	 * @description: 获取员工信息  部门 和职位
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/9/26 19:26
	 */
	public function getStaffListDepartmentJob($ids=[]){
		$data = [];
		try{
			if (!empty($ids)) {
				$builder = $this->modelsManager->createBuilder();
				$builder->columns('staff.staff_info_id,staff.sys_department_id,staff.name AS staff_name,staff.job_title,job.job_name,dep.name');
				$builder->from(['staff' => HrStaffInfoModel::class]);
				$builder->leftJoin(SysDepartmentModel::class, 'dep.id = staff.node_department_id', 'dep');
				$builder->leftJoin(HrJobTitleModel::class, 'job.id = staff.job_title', 'job');
				$builder->where('staff.staff_info_id in ({staffs:array})',
				                ['staffs' => $ids]);
				$data = $builder->getQuery()->execute()->toArray();
			}
			return $data;
		}catch (\Exception $e){
			$this->getDI()->get('logger')->write_log('getStaffListDepartmentJob =>' . json_encode([
				                                                                                      'Err_Msg' => $e->getMessage(),
				                                                                                      'Err_File' => $e->getFile(),
				                                                                                      'Err_Line' => $e->getLine(),
				                                                                                      'Err_Code' => $e->getCode(),
			                                                                                      ], JSON_UNESCAPED_UNICODE), 'error');
			return $data;
		}

	}

    /**
     * 获取员工信息
     */
    public function getStaffInfoV2($staffId): array
    {
        $returnData = [];
        if (empty($staffId)) {
            return $returnData;
        }
        $staffInfoSql  = "--
                SELECT
                    staff.staff_info_id AS staff_id,
                    staff.sys_store_id AS store_id,
                    staff.job_title,
                    staff.formal,
                    staff.sex,
                    staff.state,
                    staff.is_sub_staff,
                    staff.job_title_grade_v2,
                    staff.state,
                    staff.node_department_id as department_id,
                    store.name AS store_name,
                    store.manage_region,
                    store.manage_piece,
                    store.category,
                    store.manager_id,
                    GROUP_CONCAT( position.position_category ) position_category,
                    (CASE WHEN probation.status != 4 THEN 0
                    ELSE 1 END) AS probation
                FROM
                    hr_staff_info AS staff
                    JOIN hr_staff_info_position AS position ON staff.staff_info_id = position.staff_info_id
                    LEFT JOIN sys_store store ON staff.sys_store_id = store.id
                    LEFT JOIN sys_department department ON department.id = staff.node_department_id
                    Left JOIN hr_probation AS probation ON probation.staff_info_id = staff.staff_info_id
                WHERE
                    staff.staff_info_id = {$staffId}";
        $data = $this->getDI()->get('db_rby')->query($staffInfoSql);
        $staffInfo = $data->fetch(\Phalcon\Db::FETCH_ASSOC);


        //国籍
        $staffNation = HrStaffItemsModel::findFirst([
            'conditions' => "staff_info_id = :staff_id: and item = 'NATIONALITY'",
            'bind' => [
                'staff_id'  => $staffId,
            ]
        ]);
        if (empty($staffNation)) {
            $staffInfo['nationality'] = 0;
        } else {
            $staffInfo['nationality'] = $staffNation->value ?? 0;
        }

        return $staffInfo;
    }

    /**
     * 获取员工所属国家
     * @param $staffIds
     * @return array
     */
    public function getStaffCountryInfo($staffIds): array
    {
        $model = HrStaffItemsModel::class;
        $sql = "SELECT staff_info_id,value FROM {$model} WHERE staff_info_id IN ({staff_info_id:array}) and item='NATIONALITY'";
        $data = $this->modelsManager->executeQuery($sql, ['staff_info_id' => $staffIds])->toArray();
        return array_column($data,'value','staff_info_id');

    }

	/**
	 * 获取员工信息
	 */
	public function getStaffInfoV3($staffId): array
	{
		$returnData = [];
		if (empty($staffId)) {
			return $returnData;
		}
		$staffInfoSql  = "--
                SELECT
                    staff.staff_info_id AS w_f_condition_staff_id,
                    staff.sys_store_id AS w_f_condition_store_id,
                    staff.job_title AS w_f_condition_job_title,
                    staff.formal AS  w_f_condition_formal,
                    staff.sex AS  w_f_condition_sex,
                    staff.state AS  w_f_condition_state,
                    staff.is_sub_staff AS  w_f_condition_is_sub_staff,
                    staff.job_title_grade_v2 AS  w_f_condition_job_title_grade_v2,
                    staff.node_department_id AS  w_f_condition_node_department_id,
                    staff.hire_type AS w_f_condition_hire_type,
                    store.name AS  w_f_condition_store_name,
                    store.manage_region AS  w_f_condition_manage_region,
                    store.manage_piece AS  w_f_condition_manage_piece,
                    store.category AS  w_f_condition_category,
                    store.manager_id AS  w_f_condition_manager_id,
                    staff.wait_leave_state AS  w_f_condition_wait_leave_state
                FROM
                    hr_staff_info AS staff
                    LEFT JOIN sys_store store ON staff.sys_store_id = store.id
                    LEFT JOIN sys_department department ON department.id = staff.node_department_id
                WHERE
                    staff.staff_info_id = :staff_info_id ";
		$data = $this->getDI()->get('db_rby')->query($staffInfoSql,['staff_info_id'=>$staffId]);
		$staffInfo = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        //判断是否转正
        $probation = HrProbationModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id: ',
            'bind'       => [
                'staff_id' => $staffId,
            ],
            'columns' => 'status'
        ]);
        $staffInfo['w_f_condition_probation'] =  (!empty($probation) && $probation->status == HrProbationModel::STATUS_FORMAL) ? 1 : 0;
		//查询角色
        $position_category = HrStaffInfoPositionModel::find([
                                                                'conditions' => "staff_info_id = :staff_id: ",
                                                                'bind' => [
                                                                    'staff_id'  => $staffId,
                                                                ],
                                                                'columns' => 'position_category'
                                       ])->toArray();
        $staffInfo['position_category'] =  $staffInfo['w_f_condition_position_category']  =  implode(',',  array_column($position_category, 'position_category'));

		//国籍
		$staffNation = HrStaffItemsModel::findFirst([
			                                            'conditions' => "staff_info_id = :staff_id: and item = 'NATIONALITY'",
			                                            'bind' => [
				                                            'staff_id'  => $staffId,
			                                            ]
		                                            ]);
		if (empty($staffNation)) {
			$staffInfo['w_f_condition_nationality'] = $staffInfo['nationality'] = 0;
		} else {
			$staffInfo['w_f_condition_nationality'] =  $staffInfo['nationality'] = $staffNation->value ?? 0;
		}

        //是否为负责人
        $isStoreManager = SysStoreModel::findFirst([
            'conditions' => "manager_id = :staff_id: and state = 1",
            'bind' => [
                'staff_id'  => $staffId,
            ]
        ]);
        $staffInfo['is_manager'] = !empty($isStoreManager);

        //是否为片区负责人
        $isPieceManager = SysManagePieceModel::findFirst([
            'conditions' => "manager_id = :staff_id: and deleted = 0",
            'bind' => [
                'staff_id'  => $staffId,
            ]
        ]);
        $staffInfo['is_piece_manager'] = !empty($isPieceManager);

        //是否为大区负责人
        $isRegionManager = SysManageRegionModel::findFirst([
            'conditions' => "manager_id = :staff_id: and deleted = 0",
            'bind' => [
                'staff_id'  => $staffId,
            ]
        ]);
        $staffInfo['is_region_manager'] = !empty($isRegionManager);

        //是否为部门负责人
        $staffInfo['is_org_manager'] = $this->checkSpecStaffOrgManagerByStaffId($staffId);
        //申请人职位性质
        $staffInfo['w_f_condition_position_type'] = HrJobDepartmentRelationRepository::getJobDepartmentRelationColumn(
            $staffInfo['w_f_condition_node_department_id'],
            $staffInfo['w_f_condition_job_title'], 'position_type');
        return $staffInfo;
	}

    /**
     * @description 指定员工是否为部门负责人
     * @param $staff_info_id
     * @return bool
     */
    public function checkSpecStaffOrgManagerByStaffId($staff_info_id): bool
    {
        //是否为部门负责人
        $isOrgManager = SysDepartmentModel::findFirst([
            'conditions' => "manager_id = :staff_id: and deleted = 0",
            'bind' => [
                'staff_id'  => $staff_info_id,
            ],
            'columns' => 'id'
        ]);
        return !empty($isOrgManager);
    }

    /**
     * 员工搜索 在职 正式员工 非子账号
     * @param $search_name
     * @param int $page_size
     * @return array
     */
	public function searchStaffList($search_name, $page_size = 10) {
        $staff_list = [];
        if(!empty($search_name)) {
            $staff_list = HrStaffInfoModel::find([
                'columns' => 'staff_info_id,name',
                'conditions' => 'state = 1  AND formal = 1 AND is_sub_staff = 0 and (staff_info_id like :search_name: or name like :search_name:)',
                'bind' => [
                    'search_name' => '%' . $search_name . '%'
                ],
                'limit' => $page_size,
            ])->toArray();
        }
        return $staff_list;
    }

    /**
     * 获取员工邮箱信息
     * @param $staffInfoId
     * @return array
     */
    public function getStaffEmailInfo($staffInfoId)
    {
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind'       => ['staff_info_id' => $staffInfoId],
            'columns'    => 'staff_info_id, name, email, personal_email',
        ]);

        return !empty($staffInfo) ? $staffInfo->toArray() : [];
    }

    /**
     * 获取员工 部门所属 公司id
     * @param $staffId
     * @return int
     */
    public function getStaffDeptCompanyId($staffId)
    {
        //获取个人信息
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => [
                'staff_id' => $staffId,
            ],
            "columns"    => "staff_info_id, node_department_id",
        ]);

        if (empty($staffInfo)) {
            return 0;
        }
        $staffInfo = $staffInfo->toArray();

        $deptInfo = SysDepartmentModel::findFirst([
            'conditions' => 'id = :dept_id:',
            'bind'       => [
                'dept_id' => $staffInfo['node_department_id'],
            ],
            "columns"    => "company_id, company_name",
        ]);

        if (empty($deptInfo)) {
            return 0;
        }
        $deptInfo = $deptInfo->toArray();

        $company_id = !empty($deptInfo['company_id']) ? $deptInfo['company_id'] : 0;

        return $company_id;
    }

    /**
     * @description 获取对应职组关联的职位
     * @param int $group_id
     * @return array
     */
    public function getJobTitleByGroupId($group_id)
    {
        if (empty($group_id)) {
            return [];
        }

        $groupList = HrJobDepartmentRelationModel::find([
            "group_id = :group_id:",
            "bind" => [
                "group_id" => $group_id
            ],
            "columns" => "distinct(job_id) as job_id"
        ])->toArray();
        return array_column($groupList, "job_id");
    }

    /**
     * 获取员工基本信息
     * @param $staffIds
     * @param $items
     * @return array
     */
    public function getStaffItemsInfo($staffIds, $items)
    {
        //员工基本信息
        $hr_staff_items = HrStaffItemsModel::find([
            'conditions' => 'staff_info_id in ({staff_info_ids:array}) and item in ({items:array})',
            'bind'       => [
                'staff_info_ids' => $staffIds,
                'items'          => $items,
            ],
        ])->toArray();
        $newStaffItems  = [];
        foreach ($hr_staff_items as $oneStaff) {
            $newStaffItems[$oneStaff['staff_info_id']][$oneStaff['item']] = $oneStaff['value'];
        }
        return $newStaffItems;
    }

    /**
     * 获取指定员工基本信息
     * @param $staff_info_id
     * @param $items
     * @return array
     */
    public function getSpecStaffItemsInfo($staff_info_id, $items): array
    {
        //员工基本信息
        $hrStaffItems = HrStaffItemsModel::find([
            'conditions' => 'staff_info_id = :staff_info_id: and item in ({items:array})',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
                'items'         => $items,
            ],
        ])->toArray();
        return array_column($hrStaffItems, 'value', 'item');
    }

    /**
     * @description 获取指定工号 姓名、昵称、职级等信息
     * @param array $staffInfoIds
     * @param string $order
     * @return array
     */
    public function getSpecifyStaffInfo(array $staffInfoIds = [], string $order = ''): array
    {
        if (empty($staffInfoIds)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('hsi.staff_info_id,
            hsi.name as staff_name,
            hsi.nick_name,
            hsi.job_title,
            hsi.job_title_grade_v2,
            hjt.job_name,
            hsi.hire_type,
            sd.name as department_name');
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftjoin(HrJobTitleModel::class, 'hjt.id = hsi.job_title', 'hjt');
        $builder->leftjoin(SysDepartmentModel::class, 'sd.id = hsi.node_department_id', 'sd');
        $builder->inWhere('hsi.staff_info_id', $staffInfoIds);

        if (!empty($order)) {
            $builder->orderBy($order);
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * @description 获取指定工号 姓名、昵称、职级等信息
     * @param int $staff_info_id
     * @param string $columns
     * @return array
     */
    public function getStaffInfoByStaffId(int $staff_info_id, string $columns = 'hsi.staff_info_id'): array
    {
        if (empty($staff_info_id)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftJoin(HrStaffInfoPositionModel::class,'hsip.staff_info_id = hsi.staff_info_id','hsip');
        $builder->where('hsi.staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
        $result = $builder->getQuery()->getSingleResult();

        return !empty($result) ? $result->toArray(): [];
    }


    /**
     * 获取oa库 配置入口权限数据
     * @param string $key  code
     * @return bool
     */
    public function getOaSettingEnvAuthority(string $key)
    {
        $setting       = SettingEnvModel::findFirst([
            'conditions' => 'code = :code:',
            'bind'       => ['code' => $key],
            'columns'    => ['val'],
        ]);
        return !empty($setting) ? $setting->val : '';

    }

    /**
     * 获取员工信息
     * @param $staffId
     * @param string $columns
     * @return array
     */
    public function getStaffInfoOne($staffId, $columns = '*')
    {
        $staffInfo = HrStaffInfoModel::findFirst([
            'columns'    => $columns,
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind'       => [
                'staff_info_id' => $staffId,
            ],
        ]);

        return !empty($staffInfo) ? $staffInfo->toArray() : [];
    }

    /**
     * 获取员工信息
     * @param $staffId
     * @param string $columns
     * @return array
     */
    public function getStaffInfoToolOne($staffId, $columns = '*')
    {
        $staffInfo = ToolStaffInfoModel::findFirst([
            'columns'    => $columns,
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind'       => [
                'staff_info_id' => $staffId,
            ],
        ]);

        return !empty($staffInfo) ? $staffInfo->toArray() : [];
    }
    /**
     * @param $staffInfoId
     * @return array
     */
    public function getStaffInfoV4($staffInfoId)
    {
        if (empty($staffInfoId)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("hsi.staff_info_id,hsi.name as staff_name,hsi.leave_type,hsi.leave_reason,jt.job_name,dep.name department_name");
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftJoin(HrJobTitleModel::class,'jt.id = hsi.job_title','jt');
        $builder->leftJoin(SysDepartmentModel::class,'dep.id=hsi.node_department_id','dep');
        $builder->where('hsi.staff_info_id = :staff_info_id:', ['staff_info_id' => $staffInfoId]);
        $result = $builder->getQuery()->getSingleResult();
        return !empty($result) ? $result->toArray(): [];
    }
    /**
     * 根据身份证号获取员工最近一次工号
     * @param $identity
     * @return \Phalcon\Mvc\Model
     */
    public function getLastStaffInfoByIdentity($identity)
    {
        return HrStaffInfoModel::findFirst(
            [
                'conditions' => "identity=:identity: and state=:state:",
                'bind' => ['identity'=>$identity,'state'=>HrStaffInfoModel::STATE_RESIGN],
                'order' => 'leave_date desc'
            ]
        );
    }

    /**
     * 按照特定条件搜索员工信息
     * @param array $params 请求参数组
     * @return mixed
     */
    public function searchStaff($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $columns = 'si.staff_info_id as staff_id, si.name as staff_name, si.state, si.wait_leave_state';
        $builder->columns($columns)
            ->from(['si' => HrStaffInfoModel::class])
            ->where('si.is_sub_staff = :is_sub_staff: and si.formal in ({formals:array})', ['is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_0, 'formals' => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN]]);
        //按照员工名或工号检索
        $staff_name_or_id = $params['name'] ?? '';
        if (!empty($staff_name_or_id)) {
            $builder->andWhere('si.staff_info_id LIKE :staff_name_or_id: OR si.name LIKE :staff_name_or_id:', ['staff_name_or_id' => '%' . $staff_name_or_id . '%']);
        }
        $limit = !empty($params['limit']) ? $params['limit'] : 20;
        $builder->limit($limit);
        return $builder->getQuery()->execute()->toArray();
    }


    /**
     * 获取指定员工信息
     * @param array $staff_info_ids
     * @param string $columns 字段名
     * @return array
     */
    public function getStaffListByStaffIds($staff_info_ids = [], $columns ='*')
    {
        $staff_info_list = [];
        if (!empty($staff_info_ids)) {
            $staff_info_list = HrStaffInfoModel::find([
                'columns' => $columns,
                'conditions' => 'staff_info_id in ({staff_info_ids:array})',
                'bind' => [
                    'staff_info_ids' => $staff_info_ids,
                ],
            ])->toArray();
            $staff_info_list = array_column($staff_info_list, null, 'staff_info_id');
        }
        return $staff_info_list;
    }

    public static function getMasterStaffIdBySubStaff($sub_staff_id)
    {
        $bind             = ['sub_staff_info_id' => $sub_staff_id];
        $supportStaffInfo = HrStaffApplySupportStoreModel::findFirst([
            'conditions' => 'sub_staff_info_id = :sub_staff_info_id: ',
            'bind'       => $bind,
        ]);
        if ($supportStaffInfo) {
            return $supportStaffInfo->staff_info_id;
        }
        return 0;
    }

    public function getStaffByStaffInfoId($staffId)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['hsi.staff_info_id as staff_id', 'hsi.name', 'ss.name as store_name', 'ss.manage_piece', 'ss.manage_region', 'hsi.sys_store_id', 'hsi.node_department_id', 'hsi.hire_type']);
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftJoin(SysStoreModel::class, 'ss.id = hsi.sys_store_id', 'ss');

        $builder->where('hsi.staff_info_id = :staff_id:', ['staff_id' => $staffId]);
        $staffInfo = $builder->getQuery()->getSingleResult();
        if(empty($staffInfo)) {
            return [];
        }
        $staffInfo = $staffInfo->toArray();
        if($staffInfo['sys_store_id'] == enums::HEAD_OFFICE_ID) {
            $staffInfo['store_name'] = enums::HEAD_OFFICE;
        }


        return $staffInfo;
    }

}
