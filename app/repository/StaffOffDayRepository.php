<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\backyard\HrStaffWorkDayModel;

class StaffOffDayRepository extends BaseRepository
{

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
    }

    /**
     * 获取员工个人的轮休
     * @param int $staff_info_id
     * @param string $start_date
     * @return array
     */
    public function getStaffData(int $staff_info_id, string $start_date = ''): array
    {
        $conditions = 'staff_info_id = :staff_info_id: AND date_at >= :date_at:';
        if (empty($start_date)) {
            $start_date = date('Y-m-d', strtotime('-1 years'));
        }
        $bind = ['staff_info_id' => $staff_info_id, 'date_at' => $start_date];
        $data = HrStaffWorkDayModel::find([
            'columns'    => 'date_at,staff_info_id',
            'conditions' => $conditions,
            'bind'       => $bind,

        ])->toArray();
        return $data ? array_column($data, 'date_at') : [];
    }

    /**
     * 获取多个员工个人的轮休
     * @param array $staff_info_ids
     * @param string $start_date
     * @return array
     */
    public function getMultiStaffData(array $staff_info_ids, string $start_date = ''): array
    {
        $conditions = 'staff_info_id in ({staff_info_id:array}) AND date_at >= :date_at:';
        if (empty($start_date)) {
            $start_date = date('Y-m-d', strtotime('-1 years'));
        }
        $bind = ['staff_info_id' => $staff_info_ids, 'date_at' => $start_date];
        $data = HrStaffWorkDayModel::find([
            'columns'    => 'date_at,staff_info_id',
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();

        $res = [];
        foreach ($data as $datum) {
            $res[$datum['staff_info_id']][] = $datum['date_at'];
        }
        return $res;
    }

    public function checkStaffIsOffRest($staff_info_id, string $start_date): int
    {
        $conditions = 'staff_info_id = :staff_info_id: AND date_at = :date_at:';

        $bind = ['staff_info_id' => $staff_info_id, 'date_at' => $start_date];
        $data = HrStaffWorkDayModel::findFirst([
            'columns'    => 'date_at,staff_info_id,type',
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
        if(empty($data)){
            return 0;
        }
        return intval($data->type);
    }

}
