<?php
/**
 * Author: Bruce
 * Date  : 2024-01-26 17:45
 * Description:
 */

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\BanklistModel;

class BankListRepository extends BaseRepository
{
    public static function getBankInfo($id)
    {
        $bankInfo = BanklistModel::findFirst([
            'conditions' => 'bank_id = :bank_id:',
            'bind'       => ['bank_id' => $id],
        ]);

        return !empty($bankInfo) ? $bankInfo->toArray() : [];
    }

    /**
     * 银行卡列表
     * @param array $params
     * @return array
     */
    public static function getBankList($params = [])
    {
        $bankList = BanklistModel::find();
        return !empty($bankList) ? $bankList->toArray() : [];
    }

    /**
     * 获取指定银行信息
     * @param array $ids
     * @param string $columns 字段名
     * @return array
     */
    public function getBankListByIds($ids = [], $columns ='*')
    {
        $bank_list = [];
        if (!empty($ids)) {
            $bank_list = BanklistModel::find([
                'columns' => $columns,
                'conditions' => 'bank_id in ({bank_ids:array})',
                'bind' => [
                    'bank_ids' => $ids,
                ],
            ])->toArray();
            $bank_list = array_column($bank_list, null, 'bank_id');
        }
        return $bank_list;
    }
}