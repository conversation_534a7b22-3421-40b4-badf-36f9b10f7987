<?php
/**
 * Author: Bruce
 * Date  : 2024-05-28 20:48
 * Description:
 */

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\WinHrAsyncImportTaskModel;

class WinHrAsyncImportTaskRepository extends BaseRepository
{
    /**
     * 获取单条数据
     * @param $params
     * @param array $columns
     * @return array
     */
    public static function getInfo($params, $columns = ['*'])
    {
        $conditions = 'is_deleted = :is_deleted: ';
        $bind['is_deleted'] = WinHrAsyncImportTaskModel::IS_NOT_DELETED;

        if(isset($params['is_deleted'])) {
            $bind['is_deleted'] = $params['is_deleted'];
        }

        if(!empty($params['operator_id'])) {
            $conditions .= ' and operator_id = :operator_id: ';
            $bind['operator_id'] = $params['operator_id'];
        }

        if(!empty($params['import_type'])) {
            $conditions .= ' and import_type = :import_type: ';
            $bind['import_type'] = $params['import_type'];
        }

        if(!empty($params['status'])) {
            $conditions .= ' and status = :status: ';
            $bind['status'] = $params['status'];
        }


        if(!empty($params['id'])) {
            $conditions .= ' and id = :id: ';
            $bind['id'] = $params['id'];
        }

        if(!empty($params['relate_id'])) {
            $conditions .= ' and relate_id = :relate_id: ';
            $bind['relate_id'] = $params['relate_id'];
        }

        $data =  WinHrAsyncImportTaskModel::findFirst([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);

        return empty($data) ? [] : $data->toArray();
    }
}