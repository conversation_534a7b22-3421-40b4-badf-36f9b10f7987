<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\backyard\HeadquartersAddressModel;
use FlashExpress\bi\App\Models\fle\FleSysDepartmentModel;
use FlashExpress\bi\App\Models\fle\StaffInfoJobTitleModel;
use FlashExpress\bi\App\Models\oa\SysDepartmentPcCode;

class WmsRepository extends BaseRepository
{

    public $timezone;

    public function __construct($timezone = '')
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    public function initialize()
    {

        parent::initialize();
    }


    /**
     * 创建订单
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addOrderR($orderData, $orderDetailData)
    {
        $insetData = $orderData;
        $db = $this->getDI()->get('db');
        // 插入
        try {
            // 动态生成SQL语句(另一种语法)
            $success = $db->insertAsDict('wms_order', $insetData);
            if (!$success) {
                return false;
            }
            $resumeId = $db->lastInsertId();

            //订单物品详情
            $insetOrderDetail = $this->batch_insert('wms_order_detail', $orderDetailData);
            if (!$insetOrderDetail) {
                return false;
            }

        } catch (\Exception $e) {
            echo $e->getMessage();
            return false;
        }
        return $resumeId;
    }

    /**
     * [ 获取收货人信息]
     * @param string
     * @return
     */
    public function userInfo($info = array())
    {
        $di = $this->getDI();
        if ($info['organization_type'] == 1) {//网点
            $store_sql = "select name as consignee_name,province_code,city_code,district_code,postal_code,detail_address from sys_store where id = '{$info['organization_id']}'";
            $db = $di->get('db_fle')->query($store_sql);
            $infoArr = $db->fetch(\Phalcon\Db::FETCH_ASSOC);
        } else if ($info['organization_type'] == 2) {//部门
            $dep_sql = "select name as consignee_name,province_code,city_code,district_code,detail_address from sys_department where id = {$info['organization_id']}";
            $db = $di->get('db_fle')->query($dep_sql);
            $infoArr = $db->fetch(\Phalcon\Db::FETCH_ASSOC);
        }
        return $infoArr;
    }

    /**
     * 查询当前用户的部门
     * @Date: 2022-04-07 10:27
     * @author: peak pan
     * @return:
     **/

    public function getUserDepartment($organization_id)
    {
        $departmentInfo = FleSysDepartmentModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => [
                'id' => $organization_id
            ],
            'columns' => 'id,name as consignee_name,province_code,city_code,district_code,detail_address'
        ]);
        if($departmentInfo){
            return  $departmentInfo->toArray();
        }else{
            return  [];
        }
    }

    /**
     * [ 获取收货人信息]
     * @param string
     * @return
     */
    public function userStoreInfo($organization_id)
    {
        $di = $this->getDI();
        $store_sql = "--
            select a.name as consignee_name,b.province_code,b.city_code,b.code as district_code,b.postal_code,a.detail_address
            from sys_store a
            left join sys_district b
            on a.district_code = b.code
            where a.id = '{$organization_id}'";
        $db = $di->get('db_fle')->query($store_sql);
        $infoArr = $db->fetch(\Phalcon\Db::FETCH_ASSOC);
        if(!empty($infoArr)){
            $infoArr['postal_code'] = explode(',',$infoArr['postal_code'])[0];
        }
        return $infoArr??[];
    }


    /**
     * [userInfo code获取省市区名称]
     * @param string
     * @return
     */
    public function pdcInfo($code, $type)
    {
        $di = $this->getDI();
        if ($type == 1) {
            $sql = "select name,code  from sys_province where code = '{$code}'";
            $db = $di->get('db_fle')->query($sql);
            $infoArr = $db->fetch(\Phalcon\Db::FETCH_ASSOC);
        } else if ($type == 2) {
            $sql = "select name,code  from sys_city where code = '{$code}'";
            $db = $di->get('db_fle')->query($sql);
            $infoArr = $db->fetch(\Phalcon\Db::FETCH_ASSOC);
        } else {
            $sql = "select postal_code,name,code  from sys_district where code = '{$code}'";
            $db = $di->get('db_fle')->query($sql);
            $infoArr = $db->fetch(\Phalcon\Db::FETCH_ASSOC);
        }
        return $infoArr;
    }

    /**
     * [userInfo 通过条形码获取货物信息]
     * @param string
     * @return
     */
    public function goodsInfo($barCode)
    {
        if (is_array($barCode)) {
            $barCodeIn = implode("','", $barCode);
            $sql = "select * from wms_goods where bar_code in ('{$barCodeIn}') ";
            $db = $this->getDI()->get('db')->query($sql);
            $infoArr = $db->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        } else {
            $sql = "select * from wms_goods where bar_code = '{$barCode}' ";
            $db = $this->getDI()->get('db')->query($sql);
            $infoArr = $db->fetch(\Phalcon\Db::FETCH_ASSOC);
        }
        return $infoArr;
    }

    /**
     * 商品列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function wmsGoodsList($lang = 'th', $Manager = 1)
    {
        $barCodeArr = UC('wmsRole')['bar_code'];//仓管员允许申请的物品条码
        $barCodeStr = "'" . implode($barCodeArr, "','") . "'";
        $sql = $Manager ? "SELECT * FROM wms_goods   WHERE `deleted` = '0' ; " : "SELECT * FROM wms_goods   WHERE `deleted` = '0' AND bar_code IN ({$barCodeStr}); ";
        $info_data = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        $stock_flag = false;
        $list = array();
        if(!empty($info_data)){
            $params = [];
            //$needBarCodeArr = array_column($info_data,"bar_code");
            //$params['barCode'] = implode(",",$needBarCodeArr);
            $params['lang'] =$lang;
            $stock_data = $this->getDataFromWms(env("api_wms_goodsStock"),$params);
            if($stock_data['code']===1){
                $stock_flag = true;
            }

            foreach ($info_data as $key => $value) {
                $list[$key]['id'] = $value['id'];
                $list[$key]['bar_code'] = $value['bar_code'];
                $list[$key]['image_path'] = $value['image_path'];
                $list[$key]['nuit_detail'] = $value['nuit_detail'];
                $list[$key]['available_inventory'] = 0;
                if($stock_flag){
                    if(isset($stock_data['data'][$value['bar_code']])){
                        $list[$key]['available_inventory'] = intval($stock_data['data'][$value['bar_code']]['availableInventory']);
                    }
                }

                if ($lang == "en") {
                    $list[$key]['goods_name'] = $value['goods_name_en'];
                    $list[$key]['nuit'] = $value['nuit_en'];
                } else if ($lang == "th") {
                    $list[$key]['goods_name'] = $value['goods_name_th'];
                    $list[$key]['nuit'] = $value['nuit_th'];
                } else {
                    $list[$key]['goods_name'] = $value['goods_name_zh'];
                    $list[$key]['nuit'] = $value['nuit_zh'];
                }

            }
        }

        return $list;
    }

    /**
     * [ 获取订单信息]
     * @param string
     * @return
     */
    public function getWmsOrderMongoR($id)
    {
        $sql = "SELECT  id,order_id,organization_id,shipping_user,consignee_phone,consignee_address,province_code,city_code,district_code,postal_code,reason_application,serial_no,
reason,order_status,is_push,apply_user,approve_user,CONVERT_TZ( created_at, '+00:00', '" . $this->timezone . "' ) AS created_at,
CONVERT_TZ( updated_at, '+00:00', '" . $this->timezone . "' ) AS updated_at ,
CONVERT_TZ( deleted_at, '+00:00', '" . $this->timezone . "' ) AS deleted_at,wf_role from wms_order where id = '{$id}' ";
        $db = $this->getDI()->get('db')->query($sql);
        $infoArr = $db->fetch(\Phalcon\Db::FETCH_ASSOC);
        $detailSql = "select * from wms_order_detail where order_id = ('{$infoArr['order_id']}')  order by sort asc ";
        $db = $this->getDI()->get('db')->query($detailSql);
        $detailArr = $db->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $orderList = $infoArr;
        $orderList['dataList'] = $detailArr;
        return $orderList;
    }


    /**
     * [ 获取订单信息]
     * @param string
     * @return
     */
    public function getWmsOrderR($order_id)
    {
        $sql = "select * from wms_order where order_id = '{$order_id}' ";
        $db = $this->getDI()->get('db')->query($sql);
        $infoArr = $db->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $infoArr;
    }

    /**
     * [ 获取订单信息]
     * @param string
     * @return
     */
    public function getWmsOrderRById($id)
    {
        $sql = "select * from wms_order where id = '{$id}' ";
        $db = $this->getDI()->get('db')->query($sql);
        $infoArr = $db->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $infoArr;
    }

    /**
     * [ 获取订单商品信息]
     * @param string
     * @return
     */
    public function getWmsOrderDetailR($order_id)
    {
        $sql = "select * from wms_order_detail where order_id = ('{$order_id}') ";
        $db = $this->getDI()->get('db')->query($sql);
        $infoArr = $db->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $infoArr;
    }


    /**
     * [ 属于本网点的员工]
     * @param string
     * @return
     */
    public function notAllOrganizationR($uid, $organization_id)
    {
        $sql = "select * from staff_info where state = 1 and  id in ({$uid}) and organization_id = '{$organization_id}' ";
        $db = $this->getDI()->get('db_fle')->query($sql);
        return $db->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }


    /**
     * @param taff_audit_tool_log  日志表组装数据
     * @param wms_order更改状态 模型
     */
    public function cancel($wms_log, $wms_order)
    {
        $insetSql = $this->getInsertDbSql('staff_audit_tool_log', $wms_log);
        $up_sql = "update wms_order set order_status = {$wms_order['order_status']} ,reason = '{$wms_order['reason']}' , approve_user = {$wms_order['uid']}, updated_at =  now() where order_id = '{$wms_order['order_id']}' ";
        $db = $this->getDI()->get('db');
        try {
            $db->begin();
            $db->execute($insetSql);
            $db->execute($up_sql);
            $db->commit();
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("wmsRe:cancel:" . $e->getMessage());
            $db->rollback();
            return false;
        }
        return true;
    }

    /**
     * @param taff_audit_tool_log  日志表组装数据
     */
    public function insertLog($orderArr, $uinfo)
    {
        $wms_log['staff_id'] = $orderArr['apply_user'];     //申请人
        $wms_log['type'] = 9;
        $wms_log['original_type'] = $orderArr['order_status'];     //原始状态
        $wms_log['to_status_type'] = $orderArr['order_status'];     //修改后状态
        $wms_log['original_id'] = $orderArr['id'];     //关联id
        $wms_log['operator'] = $uinfo['id'];     //操作人
        $wms_log['operator_name'] = $uinfo['name'];     //操作人名称
        $insetSql = $this->getInsertDbSql('staff_audit_tool_log', $wms_log);
        $db = $this->getDI()->get('db');
        $res = $db->execute($insetSql);
        return $res;
    }

    /**
     * [ 通过编号获取员工信息 ]
     * @param string
     * @return
     */
    public function getUserByid($uid)
    {
        $sql = "select * from staff_info where  id = '{$uid}' ";
        $db = $this->getDI()->get('db_fle')->query($sql);
        $infoArr = $db->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $infoArr;
    }

    /**
     * [ 通过编号获取员工职位信息 ]
     * @param string
     * @return
     */
    public function getUserByids($uids = [])
    {
        $infoArr = [];
        $str_info = '';
        $sql = "select * from staff_info where  id in ({$uids}) ;";
        $db = $this->getDI()->get('db_fle')->query($sql);
        $info_data = $db->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if ($info_data) {
            $jobIds = array_column($info_data, 'job_title');
            $jobArr = $this->getJobByids($jobIds);
            $jobArrColumn = array_column($jobArr, 'name', 'id');
            foreach ($info_data as $k => $v) {
                $infoArr[$k]['id'] = $v['id'];
                $infoArr[$k]['name'] = $v['name'];
                $job_title_id = $v['job_title'];
                $jobName = isset($jobArrColumn[$job_title_id]) ? $jobArrColumn[$job_title_id] : '';
                $infoArr[$k]['job_title_name'] = $jobName;
                $str_info .= '[' . $v['id'] . '-' . $v['name'] . '-' . $jobName . ']';
            }
        }
        return $str_info;
    }

    /**
     * [ 通过job_ids获取员工职位信息 ]
     * @param string
     * @return
     */
    public function getJobByids($jobArrIds = [])
    {
        $jobTitles = [];
        if ($jobArrIds) {
            $jobTitles = StaffInfoJobTitleModel::find([
                'conditions' => ' id in ({id:array})',
                'bind' => ['id' => array_filter(array_values($jobArrIds))]
            ]);
            $jobTitles = $jobTitles ? $jobTitles->toArray() : [];
        }
        return $jobTitles;
    }


    /**
     * 更新最终审批和推荐数量
     * @param wms_order_detail
     */
    public function approvalOrderDtail($order_id, $detailArr)
    {
        $up_sql = '';
        foreach ($detailArr as $v) {
            $up_sql .= " update wms_order_detail set approval_num = {$v['approval_num']}  where order_id = '{$order_id}' and bar_code = '{$v['bar_code']}' ; ";
        }
        $db = $this->getDI()->get('db');
        try {
            $db->begin();
            $db->execute($up_sql);
            $db->commit();
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("wmsRe:approvalOrderDtail:" . $e->getMessage());
            $db->rollback();
            return false;
        }
        return true;
    }

    /**
     * [ 获取订单信息]
     * @param string
     * @return
     */
    public function getAssetOrder($order_id)
    {
        $sql = "--
        select * from assets_order where id = {$order_id} ";
        $db = $this->getDI()->get('db')->query($sql);
        $infoArr = $db->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $infoArr;
    }

    /**
     * [ 获取订单详情]
     * @param string
     * @return
     */
    public function getAssetOrderDetail($order_id,$where='')
    {
        $sql = "--
        select
            a.*,b.bar_code,b.goods_name_th
        from
            assets_order_detail as a
        left join
            assets_goods as b
        on
            a.goods_id = b.id
        where
            a.order_id = {$order_id} {$where}";
        $db = $this->getDI()->get('db')->query($sql);
        $infoArr = $db->fetchALL(\Phalcon\Db::FETCH_ASSOC);
        return $infoArr;
    }

    /**
     * 获取指定条件的物料id
     * @param array $paramIn 传入参数
     * @return array
     */
    public function getWmsIds($paramIn = [])
    {
        $searchWhere = $paramIn['searchWhere'] ?? '';
        $where       = "";
        if ($searchWhere) {
            foreach ($searchWhere as $column => $v) {
                switch ($column) {
                    case 'start_date':
                        $searchCondition[] = " wms.created_at >= '{$v} 17:00:00'";
                        break;
                    case 'end_date':
                        $searchCondition[] = " wms.created_at < '{$v} 17:00:00'";
                        break;
                    case 'serial_no':
                        $searchCondition[] = " wms.serial_no = '{$v}' ";
                        break;
                    case 'wms_id':
                        $searchCondition[] = " wms_detail.bar_code = '{$v}' ";
                        break;
                    case 'store_id':
                        $searchCondition[] = " wms.organization_id = '{$v}' ";
                        break;
                    default:
                        break;
                }
            }
            if (sizeof($searchCondition) > 1) {
                $where = implode(' AND ', $searchCondition);
            } else {
                $where = current($searchCondition);
            }
            $where = " WHERE " . $where;
        }

        $querySql = "--
            SELECT 
                wms.id,
                wms.serial_no,
                DATE_FORMAT(CONVERT_TZ(wms.created_at,  '+00:00', '{$this->timezone}'),'%Y-%m-%d') created_date,
                wms.reason as reject_reason,
                wms.apply_user as staff_id,
                wms.organization_id as store_id
            FROM
                wms_order wms
                JOIN wms_order_detail wms_detail ON wms.order_id = wms_detail.order_id
            {$where}
            GROUP BY wms.id
            ORDER BY wms.id DESC
        ";
        $returnArr = $this->getDI()->get('db')->query($querySql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        return $returnArr;
    }

    /**
     * 获取指定条件的物料id
     * @param array $paramIn 传入参数
     * @return array
     */
    public function getWmsIdsV2($paramIn = []): array
    {
        $searchWhere = $paramIn['searchWhere'] ?? '';
        $pageSize    = $paramIn['page_size'] ?? 20;
        $pageNum     = $paramIn['page_num'] ?? 1;
        $isExistWmsId= isset($paramIn['searchWhere']['wms_id']) && $paramIn['searchWhere']['wms_id'];

        $where       = "";
        if ($searchWhere) {
            foreach ($searchWhere as $column => $v) {
                switch ($column) {
                    case 'start_date':
                        $searchCondition[] = " wms.created_at >= '{$v} 17:00:00'";
                        break;
                    case 'end_date':
                        $searchCondition[] = " wms.created_at < '{$v} 17:00:00'";
                        break;
                    case 'serial_no':
                        $searchCondition[] = " wms.serial_no = '{$v}' ";
                        break;
                    case 'wms_id':
                        $searchCondition[] = " wms_detail.bar_code = '{$v}' ";
                        break;
                    case 'store_id':
                        $searchCondition[] = " wms.organization_id = '{$v}' ";
                        break;
                    case 'type':
                        $searchCondition[] = " aa.biz_type = {$v} ";
                        break;
                    case 'staff_approval_id':
                        $searchCondition[] = " aa.approval_id = {$v} ";
                        break;
                    case 'status':
                        $searchCondition[] = " aa.state = {$v} ";
                        break;
                    default:
                        break;
                }
            }
            if (sizeof($searchCondition) > 1) {
                $where = implode(' AND ', $searchCondition);
            } else {
                $where = current($searchCondition);
            }
            $where = " WHERE " . $where;
        }

        //[2]分页计算
        if (!empty($pageNum) && !empty($pageSize)) {
            $pageOffset = ($pageNum - 1) * $pageSize;
        } else {
            $pageOffset = 0;
        }
        $limit = "LIMIT {$pageSize} OFFSET {$pageOffset}";

        if ($isExistWmsId) {
            $querySql = "
                SELECT 
                    wms.id,
                    wms.serial_no,
                    DATE_FORMAT(CONVERT_TZ(wms.created_at,  '+00:00', '{$this->timezone}'),'%Y-%m-%d') created_date,
                    wms.reason as reject_reason,
                    wms.apply_user as staff_id,
                    wms.organization_id as store_id,
                    aa.state as status
                FROM
                    audit_approval aa
                    JOIN wms_order wms ON wms.id = CAST(aa.biz_value AS unsigned) and biz_type = 9
                    JOIN wms_order_detail wms_detail ON wms.order_id = wms_detail.order_id
                {$where}
                GROUP BY aa.biz_type,aa.biz_value
                ORDER BY aa.biz_value DESC
            ";
        } else {
            $querySql = "
                SELECT 
                    wms.id,
                    wms.serial_no,
                    DATE_FORMAT(CONVERT_TZ(wms.created_at,  '+00:00', '{$this->timezone}'),'%Y-%m-%d') created_date,
                    wms.reason as reject_reason,
                    wms.apply_user as staff_id,
                    wms.organization_id as store_id,
                    aa.state as status
                FROM
                    audit_approval aa
                    JOIN wms_order wms ON wms.id = CAST(aa.biz_value AS unsigned) and biz_type = 9
                {$where}
                ORDER BY aa.biz_value DESC
            ";
        }
        $returnArr = $this->getDI()->get('db_rby')->query($querySql . $limit)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        $totalSql = "select COUNT(1) from ({$querySql}) T";
        $totalCount = $this->getDI()->get('db_rby')->fetchColumn($totalSql);

        return [$returnArr, $totalCount];
    }


    /**
    * 获取当前国家的总部数据
    * @Date: 2022-03-05 15:44
    * @author: peak pan
    * @return:
    **/
    public function getHeadquartersAddress(){

        $headOffice = HeadquartersAddressModel::findFirst([
            'columns' => "office_name as name,address,province_code,city_code,postal_code,district_code",
            'conditions' => "id > :id:",
            'bind' => [
                'id'  => 0
            ],
            'limit' =>1,
            'order'      => 'id asc'
        ]);

        if(!empty($headOffice)){
            return  $headOffice->toArray();
        }else{
            return [];
        }
    }

    /**
     * 获取订单的最新地址信息
     * 12257【BY|TH】HUB与ASSET资产与耗材审批流变更P1 逻辑修改
     * https://l8bx01gcjr.feishu.cn/docs/doccneIwW7NFOD4MxPT51vDgU7b#
     * 在向wms同步库存的时候需要获取总部或网点的最新省、市、区、邮编信息
     * @param array $orderData 物料订单/资产订单信息组
     * @return array
     */
    public function getOrderNewAddressInfo($orderData)
    {
        $province = [];
        $city = [];
        $district = [];
        $postal_code = $orderData['postal_code'];
        $organization_id = $orderData['organization_id'];
        if (is_numeric($organization_id)) {
            //如果此值是个数字则表示总部
            $new_order_address = $this->getHeadquartersAddress();
        } else {
            //代表网点
            $new_order_address = $this->userStoreInfo($organization_id);
        }
        if ($new_order_address) {
            //获取新地址的省、市、区、信息
            $province = $this->pdcInfo($new_order_address['province_code'], 1);
            $city = $this->pdcInfo($new_order_address['city_code'], 2);
            $district = $this->pdcInfo($new_order_address['district_code'], 3);
            //如果网点邮编为空，取得市区邮编
            $district_postal_code = $district['postal_code'] ? explode(',', $district['postal_code']) : [];
            $postal_code = empty($orderData['postalCode']) ? ($district_postal_code ? $district_postal_code[0] : '') : $orderData['postalCode'];
        }
        return [$province, $city, $district, $postal_code];
    }

    /**
     * 获取员工所在部门或者一级部门的成本中心
     * @param array $userInfo 员工信息组
     * @return string
     */
    public function getPcCode($userInfo)
    {
        if (empty($userInfo)) return '';
        // 总部员工传入成本中心。 先用子部门查 没有的话 拿一级部门
        $pcCode = SysDepartmentPcCode::findFirst("department_id = {$userInfo['node_department_id']}");
        if (empty($pcCode) || empty($pcCode->pc_code)) {
            $pcCode = SysDepartmentPcCode::findFirst("department_id = {$userInfo['sys_department_id']}");
        }
        return empty($pcCode) ? '' : $pcCode->pc_code;
    }
}
