<?php

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\SysStoreModel;

class StoreRepository extends BaseRepository
{
    public function initialize()
    {
        parent::initialize();
    }
    /**
     * 按照特定条件搜索网点信息
     * @param array $params 请求参数组
     * @return mixed
     */
    public function searchStore($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id, name')
            ->from(SysStoreModel::class)
            ->where('state = :state:', ['state' => SysStoreModel::STATE_1]);
        //按照网点名称检索
        $name = $params['name'] ?? '';
        if (!empty($name)) {
            $builder->andWhere('name like :search_name:', ['search_name' => '%' . $name . '%']);
        }
        $limit = !empty($params['limit']) ? $params['limit'] : 20;
        $builder->limit($limit);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取指定网点id的网点数据
     * @param array $store_ids
     * @return array
     */
    public function getStoreListByIds($store_ids = [])
    {
        $store_list = [];
        if (!empty($store_ids)) {
            $store_list = SysStoreModel::find([
                'columns' => 'id,name,manager_id,manager_name,manager_phone,manager_position_state,manage_region,manage_piece,province_code,city_code,district_code,category',
                "conditions" => "id IN ({ids:array}) and state = 1",
                'bind' => [
                    "ids" => $store_ids,
                ],
            ])->toArray();

            $store_list = array_column($store_list, null, 'id');
        }
        return $store_list;
    }

    /**
     * 根据网点id获取网点信息
     * @param string $store_id 网点id
     * @return array
     */
    public function getStoreDetail(string $store_id = ''): array
    {
        $detail = [];
        if (!empty($store_id)) {
            $store_info = SysStoreModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $store_id]
            ]);
            $detail = !empty($store_info) ? $store_info->toArray() : [];
        }
        return $detail;
    }

}