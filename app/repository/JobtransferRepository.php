<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrJobDepartmentRelationModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\JobTransferModel;

class JobtransferRepository extends BaseRepository
{
    public $timezone;

    public function __construct($timezone)
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    /**
     * 转岗添加
     * @Access  public
     * @Param   array
     * @Return  int
     */
    public function addJobtransfer($paramIn = [])
    {
        $result        = $this->getDI()->get('db')->insertAsDict(
            'job_transfer', $paramIn
        );
        $jobTransferId = $this->getDI()->get('db')->lastInsertId();
        return $jobTransferId;
    }

    /**
     * 职位列表
     * @Access  public
     * @Param   array
     * @return mixed
     */
    public function getPositionList($paramIn = [])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'hjt.id',
            'hjt.status',
            'hjt.job_name',
            'hjdr.working_day_rest_type'
        ]);
        $builder->from(['hjdr' => HrJobDepartmentRelationModel::class]);
        $builder->leftJoin(HrJobTitleModel::class, "hjt.id = hjdr.job_id", 'hjt');

        $builder->where('hjt.status = :status: and hjdr.department_id = :department_id:', ['status' => HrJobTitleModel::STATUS_OPEN, 'department_id' => $paramIn['department_id']]);

        if(!empty($paramIn['job_title']) && is_array($paramIn['job_title'])) {
            $builder->andWhere('hjt.id in ({job_title:array})', ['job_title' => $paramIn['job_title']]);
        }

        $builder->orderBy('CONVERT(hjt.job_name USING gbk) ASC');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 转岗校验是否办理资产交接
     * @Access  public
     * @Param   array
     * @Return  array
     */
    public function getAssetsInfo($paramIn = [])
    {
        $staffId       = $paramIn['staff_id'];
        $transferState = $paramIn['transfer_state'];
        $sql           = "--
                        SELECT
                            * 
                        FROM
                            assets_info 
                        WHERE
                            staff_info_id = {$staffId}
                        AND
                            transfer_state in ({$transferState})";
        $obj           = $this->getDI()->get('db')->query($sql);
        $returnData    = $obj->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /**
     * 转岗校验回款
     * @Access  public
     * @Param   array
     * @Return  array
     */
    public function getReceivableDetail($paramIn = [])
    {
        $staffId    = $paramIn['staff_id'];
        $storeId    = $paramIn['store_id'];
        $state      = $paramIn['state'] ?? 0;
        $sql        = "--
                        SELECT
                            * 
                        FROM
                            store_receivable_bill_detail 
                        WHERE
                            store_id = '{$storeId}' AND staff_info_id = {$staffId}
                        AND
                            state in ({$state})";
        $obj        = $this->getDI()->get('db_fle')->query($sql);
        $returnData = $obj->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /**
     * 转岗校验工作是否完成
     * @Access  public
     * @Param   array
     * @Return  array
     */
    public function getTicketDelivery($paramIn = [])
    {
        $staffId    = $paramIn['staff_id'];
        $state      = $paramIn['state'];
        $sql        = "--
                        SELECT
                            * 
                        FROM
                            ticket_delivery 
                        WHERE
                            staff_info_id = {$staffId}
                        AND
                            state in ({$state})";
        $obj        = $this->getDI()->get('db_fle')->query($sql);
        $returnData = $obj->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /**
     * 转岗获取网点下拉列表
     * @Access  public
     * @Param   array
     * @Return  array
     */
    public function getStoreList($paramIn = [])
    {
        $category = $paramIn['category'] ?? "";
        if (!$category) {
            return [];
        }
        $sql        = "--
                        SELECT
                            id,
                            `name` 
                        FROM
                            sys_store 
                        WHERE
                            category in ({$category}) 
                        AND 
                            state=1";
        $obj        = $this->getDI()->get('db_fle')->query($sql);
        $returnData = $obj->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /**
     * 转岗获取网点负责人
     * @Access  public
     * @Param   array
     * @Return  array
     */
    public function getStoreManager($paramIn = [])
    {
        $storeIds = $paramIn['store_id'] ?? "";
        if (!$storeIds) {
            return [];
        }
        $sql        = "--
                        SELECT
                            id,
                            `name`,
                            manager_id 
                        FROM
                            sys_store 
                        WHERE
                            id = '{$storeIds}' 
                        AND 
                            state=1";
        $obj        = $this->getDI()->get('db_fle')->query($sql);
        $returnData = $obj->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /**
     * 转岗获取网点正主管
     * @Access  public
     * @Param   array
     * @Return  array
     */
    public function getStoreCharge($paramIn = [])
    {
        $storeIds = $paramIn['store_id'] ?? "";
        if (!$storeIds) {
            return [];
        }
        $sql        = "--
                        SELECT staff.id AS staff_info_id 
                        FROM staff_info AS staff 
                        WHERE staff.organization_id = '{$storeIds}' AND job_title = 16 AND `state` = 1
                            ";
        $obj        = $this->getDI()->get('db_fle')->query($sql);
        $returnData = $obj->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /**
     * 转岗获取网点信息
     * @Access  public
     * @Param   array
     * @Return  array
     */
    public function getStoreInfo($paramIn = [])
    {
        $storeIds = $paramIn['store_id'] ?? "";
        if (!$storeIds) {
            return [];
        }
        $sql        = "--
                        SELECT * FROM sys_store WHERE id = '{$storeIds}'";
        $obj        = $this->getDI()->get('db_fle')->query($sql);
        $returnData = $obj->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    public function getJobtransferInfo($paramIn = [])
    {
        $id      = $paramIn['id'] ?? "";
        $staffId = $paramIn['staff_id'] ?? "";
        $state   = $paramIn['state'] ?? "";
        if (!$id && !$staffId) {
            return [];
        }
        $sql = "--
                SELECT
                    jt.id,
                    jt.serial_no,
                    jt.staff_id,
                    jt.hc_id,
                    jt.state,
                    jt.`type`,
                    jt.current_department_id,
                    jt.current_store_id,
                    jt.current_position_id,
                    jt.current_role_id,
                    jt.current_manager_id,
                    jt.current_indirect_manger_id,
                    jt.after_department_id,
                    jt.after_store_id,
                    jt.after_position_id,
                    jt.after_manager_id,
                    DATE_FORMAT(jt.after_date,'%Y-%m-%d') as after_date,
                    jt.reason,
                    jt.job_handover_staff_id,
                    jt.is_pay_adjustment,
                    jt.approval_state,
                    jt.submitter_id,
                    jt.reject_reason,
                    DATE_FORMAT( CONVERT_TZ( jt.created_at, '+00:00', '{$this->timezone}' ), '%Y-%m-%d %H:%i:%s' ) AS created_at,
                    DATE_FORMAT( CONVERT_TZ( jt.updated_at, '+00:00', '{$this->timezone}' ), '%Y-%m-%d %H:%i:%s' ) AS updated_at,
                    jt.deleted,
                    jt.before_base_salary,
                    jt.after_base_salary,
                    jt.after_exp_allowance,
                    jt.after_position_allowance,
                    jt.after_car_rental,
                    jt.after_trip_payment,
                    jt.after_notebook_rental,
                    jt.after_recommended,
                    jt.after_food_allowance,
                    jt.after_dangerous_area,
                    jt.after_gasoline_allowance,
                    jt.after_house_rental,
                    jt.after_island_allowance,
                    jt.after_deminimis_benefits,
                    jt.after_performance_allowance,
                    jt.after_other_non_taxable_allowance,
                    jt.after_other_taxable_allowance,
                    jt.after_phone_subsidy,
                    jt.hc_expiration_date,
                    jt.after_role_ids,
                    jt.rental_car_cteated_at,
                    jt.car_owner,
                    jt.senior_auditor,
                    jt.reject_salary_detail,
                    jt.vehicle_source,
                    jt.before_working_day_rest_type,
                    jt.after_working_day_rest_type,
                    jt.transfer_reason
                FROM
                    job_transfer AS jt 
                WHERE
                    deleted=0 AND ";
        if ($id) {
            $sql .= "jt.id = {$id}";
        } elseif ($staffId) {
            $sql .= "jt.staff_id = {$staffId}";
        } else {
            return [];
        }
        if ($state) {
            $sql .= " AND jt.state = {$state}";
        }
        $obj  = $this->getDI()->get('db')->query($sql);
        $data = $obj->fetch(\Phalcon\Db::FETCH_ASSOC);


        $query_img_sql = "
                --
                select 
                    bucket_name
                    ,object_key
                from sys_attachment 
                where oss_bucket_key = ?
                    and oss_bucket_type = 'JOB_TRANSFER' 
                    and deleted = 0";
        $images = $this->getDI()->get('db')->query($query_img_sql, [$id])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (!empty($images)) {
            $data['image_path'] = [];
            foreach ($images as $image) {
                array_push($data['image_path'], convertImgUrl($image['bucket_name'], $image['object_key']));
            }
        }

        return $data;
    }

    /**
     * 基础-修改转岗表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function updateJobtransfer($paramIn = [])
    {
        $id         = $paramIn['id'] ?? "";
        $updateData = $paramIn['updateData'] ?? [];

        if (!$updateData || !$id)
            return false;

        $this->getDI()->get('db')->updateAsDict(
            'job_transfer',
            $updateData,
            'id = ' . $id
        );
        if ($this->getDI()->get('db')->affectedRows() < 1) {
            return false;
        }
        return true;
    }

    /**
     * 基础-转岗列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getJobtransferList($paramIn = [])
    {
        $date       = $paramIn["after_date"] ?? '';
        $staffIds   = $paramIn["staff_ids"] ?? '';
        $id         = $paramIn["id"] ?? 0;
        $state      = $paramIn["state"] ?? 1;
        $appState   = $paramIn["approval_state"] ?? 2;

        $sql = "--
                SELECT
                    * 
                FROM
                    job_transfer 
                WHERE deleted=0 AND approval_state = {$appState} AND `state` = {$state} AND";
        if ($date) {
            $sql .= " after_date = '{$date}'";
        } else if ($staffIds) {
            $sql .= " staff_id IN ({$staffIds})";
        } else if ($id) {
            $sql .= " id = {$id}";
        } else {
            $sql .= " 1 = 1";
        }
        $obj  = $this->getDI()->get('db')->query($sql);
        $data = $obj->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $data;
    }

    /**
     * 获取转岗审批、转岗状态
     * @param array $paramIn
     * @return array
     */
    public function getJobTransferAudit($paramIn = [])
    {
        $jtStaffId  = $paramIn['jt_staff_id'] ?? "";

        if (empty($jtStaffId))  {
            return [];
        }

        $params = [
            $jtStaffId,
        ];

        $sql = "--
            SELECT
                jtp.id,
                jtp.after_department_id as department_id,
                jtp.after_store_id as store_id,
                jtp.after_position_id as position_id,
                jtp.approval_state as step1_status, -- 一阶段审批状态
                jt.approval_state  as step2_status, -- 二阶段审批状态
                jt.state                            -- 转岗状态
            FROM job_transfer_prefix jtp
            LEFT JOIN job_transfer jt ON jtp.id = jt.job_transfer_id 
            WHERE
                jtp.deleted = 0 AND jtp.id = (
                    SELECT MAX(id)
                    FROM job_transfer_prefix
                    WHERE staff_id = ?
                )";

        $data = $this->getDI()->get('db')->query($sql, $params)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $data;
    }


    /**
     * 获取转岗审批、转岗状态
     * @param array $paramIn
     * @return array
     */
    public function getJobTransferAuditOptimization($paramIn = [])
    {
        $jtStaffId = $paramIn['jt_staff_id'] ?? "";

        if (empty($jtStaffId)) {
            return [];
        }

        $params = [
            $jtStaffId,
        ];

        $sql = "--
            SELECT
                staff_id,
                id,
                after_department_id as department_id,
                after_store_id as store_id,
                after_position_id as position_id,
                approval_state  as approval_state, -- 审批状态
                state                            -- 转岗状态
            FROM job_transfer
            WHERE
                deleted = 0 AND id = (
                    SELECT MAX(id)
                    FROM job_transfer
                    WHERE staff_id = ?
                )";

        $data = $this->getDI()->get('db')->query($sql, $params)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $data;
    }

    /**
     * 获取指定工号最新一次申请转岗
     * @param int $staffId
     * @return mixed
     */
    public function getLastJobTransfer($staffId)
    {
        if (empty($staffId)) {
            return false;
        }

        //最新一次审批通过的转岗前置申请
        $sql = "--
            SELECT MAX(id) as id FROM job_transfer_prefix WHERE staff_id = ? and approval_state = 2";
        $data = $this->getDI()->get('db')->query($sql, [$staffId])->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $data;
    }

    /**
     * 获取职位名字
     * @param $job_title
     * @return
     */
    public function getJobTitleName($job_title)
    {
        $sql = "--
            SELECT 
                id,
                job_name
             FROM hr_job_title WHERE id = ? AND status = 1";
        $data = $this->getDI()->get('db_rby')->query($sql, [$job_title])->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $data;
    }

    /**
     * sys_department_id
     * 获取部门id
     * @param $param
     */
    public function getSysDepartmentId($param)
    {
        $jtStaffId = $param['staff_id'] ?? "";

        if (empty($jtStaffId)) {
            return [];
        }

        $params = [
            $jtStaffId,
        ];

        $sql = "--
            SELECT
                sys_department_id
            FROM hr_staff_info
            WHERE
                staff_info_id = ?";

        $data = $this->getDI()->get('db_rby')->query($sql, $params)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $data;
    }


    /**
     * 员工信息
     * @param $staff_info_id
     * @return array
     */
    public function getBaseStaffInfo($staff_info_id) {
        $staff_info = HrStaffInfoModel::findFirst([
            'columns' => "staff_info_id,name,node_department_id,sys_store_id,job_title,week_working_day,rest_type,state,wait_leave_state,leave_date",
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind' => [
                'staff_info_id'  => $staff_info_id
            ]
        ]);

        return !empty($staff_info) ? $staff_info->toArray() : [];
    }

    /**
     * @description 获取转岗详情
     * @param $id
     * @return array
     */
    public function getJobTransferDetailById($id): array
    {
        if (empty($id)) {
            return [];
        }
        $detailInfo = JobTransferModel::findFirst($id);
        return $detailInfo ? $detailInfo->toArray(): [];
    }
}
