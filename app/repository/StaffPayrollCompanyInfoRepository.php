<?php
/**
 * Author: Bruce
 * Date  : 2025-04-14 11:19
 * Description:
 */

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\StaffPayrollCompanyInfoModel;

class StaffPayrollCompanyInfoRepository
{
    public static function getOne($params, $columns = ['*'])
    {
        $conditions = '1 = 1';
        $bind = [];

        if(empty($params)) {
            return [];
        }
        if(!empty($params['id'])) {
            $conditions .= ' and id = :id:';
            $bind['id'] = $params['id'];
        }

        if(!empty($params['company_id'])) {
            $conditions .= ' and company_id = :company_id:';
            $bind['company_id'] = $params['company_id'];
        }

        $data = StaffPayrollCompanyInfoModel::findFirst([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);

        return empty($data) ? [] : $data->toArray();
    }

    public static function allData($params, $columns = ['*'])
    {
        $conditions = '1 = 1';
        $bind = [];

        if(!empty($params['id'])) {
            $conditions .= ' and id = :id:';
            $bind['id'] = $params['id'];
        }

        if(!empty($params['company_id'])) {
            $conditions .= ' and company_id = :company_id:';
            $bind['company_id'] = $params['company_id'];
        }

        return StaffPayrollCompanyInfoModel::find([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();
    }
}