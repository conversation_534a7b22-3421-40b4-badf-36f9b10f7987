<?php
/**
 * Author: Bruce
 * Date  : 2024-11-14 00:40
 * Description:
 */

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\HrStaffContractBusinessBaseModel;

class HrStaffContractBusinessBaseRepository extends BaseRepository
{
    public function initialize()
    {
        parent::initialize();
    }

    public static function getOneById($id, $columns = ['*'])
    {
        $data = HrStaffContractBusinessBaseModel::findFirst(
            [
                'columns'    => $columns,
                'conditions' => "id = :id:",
                'bind'       => [
                    'id' => $id,
                ],
            ]
        );

        return empty($data) ? [] : $data->toArray();
    }

    public static function getOneByContractId($params, $columns = ['*'])
    {
        if(empty($params)) {
            return [];
        }

        $data = HrStaffContractBusinessBaseModel::findFirst(
            [
                'columns'    => $columns,
                'conditions' => "contract_id = :contract_id:",
                'bind'       => [
                    'contract_id' => $params['contract_id'],
                ],
            ]
        );

        return empty($data) ? [] : $data->toArray();
    }
}