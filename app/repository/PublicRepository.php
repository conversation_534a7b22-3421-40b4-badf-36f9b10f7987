<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\library\Particle;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use Ramsey\Uuid\Provider\Node\RandomNodeProvider;
use Ramsey\Uuid\Uuid;

class PublicRepository extends BaseRepository
{

    public $timezone;

    public function initialize($timezone = '+07:00')
    {
        parent::initialize();
        $this->timezone = $timezone;
    }

    /**
     * 推送消息给审批人跳转到待审批列表
     * @Access  public
     * @Param   array
     * @Return  array
     */
    public function pushMessage($paramIn = [])
    {
        try{
            //获取必须参数
            if (!$paramIn || !$paramIn['staff_info_id'] || !$paramIn['message_title']  ||
                (empty($paramIn['userinfo']) && empty($paramIn['message_content']))) {
                return false;
            }
            //接收push信息人id
            $staff_info_id      = $paramIn['staff_info_id'];
            //push标题
            $message_title      = $paramIn['message_title'];
            //当前登陆用户信息
            $userinfo           = $paramIn['userinfo'] ?? [];
            //模块名称
            $auditType          = $paramIn['audit_type'] ?? '';
            //是否静默消息
            $silence            = $paramIn['silence'] ?? CommonEnums::PUSH_MESSAGE_TYPE_NORMAL; //静默消息，0否，1是
            //push跳转地址
            $approvalDetailUrl  = $paramIn['approval_detail_url'] ?? '';
            //获取环境变量
            $approval_html_url  = env('approval_html_url','flashbackyard://fe/html?url=');
            $approval_detail_url= !empty($approvalDetailUrl) ? $approvalDetailUrl: env('approval_detail_url','http://192.168.0.222:90/#/ApprovalDetail/');

            if (isset($paramIn['message_content']) && $paramIn['message_content']) {
                $message_content = $paramIn['message_content'];
            } else {
                //获取语言内容
                $lang = $paramIn['lang'] ?? '';
                $lang = $lang ? : (new StaffServer())->getLanguage($staff_info_id);
                $getTranslation     = (new BaseServer())->getTranslation($lang);
                $tijiao             = $getTranslation->_('6001');
                $tijiao2            = $getTranslation->_('6002');

                //push内容： xxxx (工号) 提交了 (xxxx类型申请) 请你审批
                $message_content = $userinfo['name']."(".$userinfo['id'].")".$tijiao.'{'.$auditType."}".$tijiao2;
            }
            $message_scheme     = $approval_html_url . urlencode($approval_detail_url);

            //拼接push接口数据
            $data = [
                "staff_info_id"     => $staff_info_id,  //上级id
                "src"               => "backyard",      //1:'kit'; 2:'backyard','c';
                "message_title"     => $message_title,  //标题
                "message_content"   => $message_content,//内容
                "message_scheme"    => $message_scheme, //地址
                "message_priority"  => 1,               //push优先级: 0-普通; 1-优先
                'silence'           => $silence,
            ];
            $this->getDI()->get('logger')->write_log("publicRe:pushMessage-" . json_encode($data),'info');

            //TODO 延时rpc调用
            if(env('rmq_delayed_time_push',1)){
                $rid = (new RocketMQ('message-push'))->sendToMsg($data,1);//1秒延迟
                $this->getDI()->get('logger')->write_log("publicRe:pushMessage-return- " . $rid,'info');
            }else{

                $ret = (new ApiClient('bi_rpc','','push_to_staff'));
                $ret->setParams($data);
                $_data = $ret->execute();
                $this->getDI()->get('logger')->write_log("publicRe:pushMessage-return- " . json_encode($_data),'info');
            }
            
            return true;
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("publicRe:pushMessage-" . $e->getMessage(),'info');
            return false;
        }
    }

    /**
     * 推送消息给审批人跳转到待审批列表
     * @Access  public
     * @Param   array
     * @Return  array
     */
    public function pushMessageAndJumpToCC($paramIn = [])
    {
        try{
            //获取必须参数
            if (!$paramIn || !$paramIn['staff_info_id'] || !$paramIn['message_title'] || !$paramIn['message_content']){
                return false;
            }
            //接收push信息人id
            $staff_info_id      = $paramIn['staff_info_id'];
            //push标题
            $message_title      = $paramIn['message_title'];
            //push内容
            $message_content    = $paramIn['message_content'];

            //获取环境变量
            $setting_model      = new BySettingRepository();
            $cc_list_url        = $setting_model->get_setting('cc_list_url');
            $approval_tab_url   = $setting_model->get_setting('approval_tab_url');

            //获取语言内容
            $message_scheme    = $approval_tab_url . urlencode($cc_list_url);

            //拼接push接口数据
            $data = [
                "staff_info_id"     => $staff_info_id,  //推送人员工ID
                "src"               => "backyard",      //1:'kit'; 2:'backyard','c';
                "message_title"     => $message_title,  //标题
                "message_content"   => $message_content,//内容
                "message_scheme"    => $message_scheme, //地址
            ];

            //rpc调用push接口
            $bi_rpc = (new ApiClient('bi_rpc','','push_to_staff'));
            $bi_rpc->setParams($data);
            $bi_return = $bi_rpc->execute();
            $this->getDI()->get('logger')->write_log('发送push数据到BI:request:'.json_encode($data).';response:'.json_encode($bi_return), 'info');

            return true;
        }catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("publicRe:pushMessageToSubmitter-" . $e->getMessage(),'info');
            return false;
        }
    }

    /**
     * 推送消息给审批人并且跳转到审批人已完成列表
     * @Access  public
     * @param array $paramIn
     * @return bool
     */
    public function pushMessageAndJumpToFinish($paramIn = [])
    {
        try{
            //获取必须参数
            if (!$paramIn || !$paramIn['staff_info_id'] || !$paramIn['message_title'] || !$paramIn['message_content']){
                return false;
            }
            //接收push信息人id
            $staff_info_id      = $paramIn['staff_info_id'];
            //push标题
            $message_title      = $paramIn['message_title'];
            //push内容
            $message_content    = $paramIn['message_content'];
            //接收人是否是申请人 1-申请人 2-审批人
            $is_submitter       = $paramIn['is_submitter'] ?? 1;

            //获取环境变量
            $setting_model      = new BySettingRepository();
            if ($is_submitter == 1) {
                $approval_detail_url= $setting_model->get_setting('submitter_finish_url');
            } else {
                $approval_detail_url= $setting_model->get_setting('approver_finish_url');
            }
            $approval_tab_url  = $setting_model->get_setting('approval_tab_url');

            //获取语言内容
            $message_scheme    = $approval_tab_url . urlencode($approval_detail_url);

            //拼接push接口数据
            $data = [
                "staff_info_id"     => $staff_info_id,  //推送人员工ID
                "src"               => "backyard",      //1:'kit'; 2:'backyard','c';
                "message_title"     => $message_title,  //标题
                "message_content"   => $message_content,//内容
                "message_scheme"    => $message_scheme, //地址
            ];

            //rpc调用push接口
            $bi_rpc = (new ApiClient('bi_rpc','','push_to_staff'));
            $bi_rpc->setParams($data);
            $bi_return = $bi_rpc->execute();
            $this->getDI()->get('logger')->write_log('发送push数据到BI:request:'.json_encode($data).';response:'.json_encode($bi_return), 'info');

            return true;
        }catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("publicRe:pushMessageToSubmitter-" . $e->getMessage(),'info');
            return false;
        }
    }

    /**
     * 推送消息给提交人
     * @Access  public
     * @Param   array
     * @Return  array
     */
    public function pushMessageToSubmitter($paramIn = [])
    {
        try{
            //获取必须参数
            if (!$paramIn || !$paramIn['staff_info_id'] || !$paramIn['message_title']){
                return false;
            }
            //接收push信息人id
            $staff_info_id      = $paramIn['staff_info_id'];
            //push标题
            $message_title      = $paramIn['message_title'];
            //模块名称
            $auditType          = $paramIn['audit_type']?$paramIn['audit_type']:'';
            //获取环境变量
            $approval_html_url  = env('approval_html_url','flashbackyard://fe/html?url=');
            $approval_detail_url= env('approval_detail_url','http://192.168.0.222:90/#/ApprovalDetail/');

            //获取语言内容
            $lang = $paramIn['lang'];
            $getTranslation     = (new BaseServer())->getTranslation($lang);
            $response1          = $getTranslation->_('you_submit');
            $response2          = $getTranslation->_('please_check');

            $message_content    = $response1.'{'.$auditType."}".$response2;
            $message_scheme     = $approval_html_url . urlencode($approval_detail_url);

            //拼接push接口数据
            $data = [
                "staff_info_id"     => $staff_info_id,//上级id
                "src"               => "backyard", //1:'kit'; 2:'backyard','c';
                "message_title"     => $message_title,//标题
                "message_content"   => $message_content,//内容
                "message_scheme"    => $message_scheme,//地址
            ];
            $this->getDI()->get('logger')->write_log("publicRe:pushMessageToSubmitter-" . json_encode($data),'info');

            //Push消息到队列中等待消费,与bi解耦
            // (new \FlashExpress\bi\App\Server\BaseServer())->pushList('push_to_staff', $svc_call, $data);

            $ret = (new ApiClient('bi_rpc', '', 'push_to_staff'));
            $ret->setParams($data);
            $ret = $ret->execute();


            return true;
        }catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("publicRe:pushMessageToSubmitter-" . $e->getMessage(),'info');
            return false;
        }
    }

    /**
     * 推送push并且发送站内消息
     * @param array $paramIn
     * @return bool
     */
    public function pushAndSendMessageToSubmitter($paramIn = []): bool
    {

        try{
            //获取必须参数
            if (!$paramIn || !$paramIn['staff_info_id'] || !$paramIn['message_title']){
                return false;
            }
            //接收push信息人id
            $staff_info_id      = $paramIn['staff_info_id'];
            //push标题
            $message_title      = $paramIn['message_title'];
            //push标题
            $message_content    = $paramIn['message_content'];
            //模块名称
            $auditType          = $paramIn['type'];
            //获取环境变量
            $svc_call           = env('svc_call','http://192.168.0.230:8001/Svc/call');
            //BY调整页面地址
            $path               = $paramIn['path'] ?? "";
            //拼接push接口数据
            $data = [
                "staff_info_id"     => $staff_info_id,  //提交人ID
                "title"             => $message_title,  //标题
                "category"          => $auditType,      //类别
                "content"           => $message_content,//内容
            ];
            //调用push接口
            $fle_rpc = (new ApiClient('bi_rpc','','message_to_backyard', $this->lang));
            $fle_rpc->setParams($data);
            $fle_rpc->execute();

            //发送push
            switch ($path) {
                case "message_list":
                    //消息列表
                    $message_scheme     = "flashbackyard://fe/tab?index=message";
                    break;
                default:
                    //默认地址
                    $approval_html_url  = env('approval_html_url','flashbackyard://fe/html?url=');
                    $approval_detail_url= env('approval_detail_url','http://192.168.0.222:90/#/ApprovalDetail/');
                    $message_scheme     = $approval_html_url . urlencode($approval_detail_url);
                    break;
            }

            //拼接push接口数据
            $data = [
                "staff_info_id"     => $staff_info_id,  //上级id
                "src"               => "backyard",      //1:'kit'; 2:'backyard','c';
                "message_title"     => $message_title,  //标题
                "message_content"   => str_replace("<br/>", "  ",$message_content),//内容
                "message_scheme"    => $message_scheme, //地址
            ];
            $this->getDI()->get('logger')->write_log("publicRe:pushMessage-" . json_encode($data),'info');

            //rpc调用
            $ret = (new ApiClient('bi_rpc', '', 'push_to_staff'));
            $ret->setParams($data);
            $bi_return = $ret->execute();

            return true;
        }catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("publicRe:pushAndSendMessageToSubmitter-" . $e->getMessage(),'info');
            return false;
        }
    }

    /**
     * 推送Kit push并且发送站内消息
     * @param array $paramIn
     * @return bool
     */
    public function pushAndSendKitMessageToSubmitter($paramIn = []): bool
    {

        try{
            //获取必须参数
            if (!$paramIn || !$paramIn['staff_info_id'] || !$paramIn['message_title']){
                return false;
            }
            //接收push信息人id
            $staff_info_id      = $paramIn['staff_info_id'];
            //push标题
            $message_title      = $paramIn['message_title'];
            //push标题
            $message_content    = $paramIn['message_content'];
            //模块名称
            $auditType          = $paramIn['type'];
            //获取环境变量
            $svc_call           = env('svc_call','http://192.168.0.230:8001/Svc/call');
            //BY调整页面地址
            $path               = $paramIn['path'] ?? "";

            //拼接push接口数据
            $data = [
                "staff_users"       => [['id'=>$staff_info_id]],  //提交人ID
                "message_title"     => $message_title,  //标题
                "message_content"   => $message_content,//内容
                "category"          => $auditType,
            ];
            $this->getDI()->get('logger')->write_log("publicRe:pushMessageToSubmitter-" . json_encode($data),'info');

            //调用push接口
            $bi_rpc = (new ApiClient('bi_rpc','','add_kit_message', $this->lang));
            $bi_rpc->setParams($data);
            $bi_rpc->execute();

            //拼接push接口数据
            $data = [
                "staff_info_id"     => $staff_info_id,  //上级id
                "src"               => "kit",      //1:'kit'; 2:'backyard','c';
                "message_title"     => $message_title,  //标题
                "message_content"   => $message_content,//内容
                "message_scheme"    => '', //地址
            ];
            $this->getDI()->get('logger')->write_log("publicRe:pushMessage-" . json_encode($data),'info');

            //rpc调用
            $bi_rpc = (new ApiClient('bi_rpc','','push_to_staff', $this->lang));
            $bi_rpc->setParams($data);
            $bi_rpc->execute();

            return true;
        }catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("publicRe:pushAndSendKitMessageToSubmitter-" . $e->getMessage(),'info');
            return false;
        }
    }

    /**
     * 根据员工id获取员工名称
     * @param $staff_id
     */
    public function getStaffName($staff_id){

        $sql = "select `name` from hr_staff_info where staff_info_id = {$staff_id}";
        $name = $this->getDI()->get('db_rby')->fetchColumn($sql);
        return $name;
    }

    /**
     * 批量插入图片
     * @param $images
     * @return
     */
    public function batchInsertImgs($images = [], $osstype = 'FLEET_AUDIT')
    {
        if (empty($images)) {
            return [];
        }

        $insertData = [];
        foreach ($images as $image) {
            $urlInfo = parse_url($image['image_path']);

            $insertData[] = [
                'id'                => $this->generateId(),
                'oss_bucket_type'   => $osstype,
                'oss_bucket_key'    => $image['id'],
                'bucket_name'       => substr($urlInfo['host'],0, strpos($urlInfo['host'], '.')),
                'object_key'        => trim($urlInfo['path'], '/'),
                'original_name'     => $image['original_name'] ?? null,
            ];
        }
        return $this->batch_insert('sys_attachment', $insertData);
    }

    /**
     * 生成图片唯一ID
     */
    public function generateId()
    {
        if (class_exists('Ramsey\Uuid\Provider\Node\RandomNodeProvider')) {
            $nodeProvider = new RandomNodeProvider();
            $newId        = Uuid::uuid1($nodeProvider->getNode(), mt_rand(1, 16000))->toString();
            $newId        = str_replace('-', '', $newId);
        } else {
            $newId = Particle::generateParticle();
        }
        return $newId;
    }

    /**
     * 获取省列表
     * @param array $paramIn
     * @return array
     */
    public function getProvinceCode($paramIn = [])
    {
        $ids = $paramIn['ids'] ?? '';
        if (!empty($ids)) {
            $sql = "--
                SELECT 
                    code,
                    name
                FROM `sys_province` 
                WHERE `code` IN ({$ids})
            ";
            $obj          = $this->getDI()->get('db_rby')->query($sql);
            $returnData   = $obj->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        } else {
            $sql = "--
                SELECT 
                    code,
                    name
                FROM `sys_province` 
            ";
            $obj          = $this->getDI()->get('db_rby')->query($sql);
            $returnData   = $obj->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        }
        return $returnData;
    }

    /**
     * 获取省列表
     * @return array
     */
    public function getProvinceList(): array
    {
        $result = [];
        $list = $this->getProvinceCode();
        foreach ($list as $item) {
            $result[] = [
                'value' => $item['code'],
                'label' => $item['name'],
            ];
        }
        return $result;
    }


    /**
     * 获取网点列表
     * @param array $paramIn
     * @return array
     */
    public function getStoreList($paramIn = [])
    {
        $ids      = $paramIn['ids'] ?? '';
        $category = $paramIn['category'] ?? '';
        $where    = "";
        if (!empty($category)) {
            $where = " AND category IN ($category) ";
        }


        if (!empty($ids)) {
            $sql = "--
                SELECT 
                    id,
                    name
                FROM `sys_store` 
                WHERE `id` IN ({$ids}) AND state = 1
                {$where}
            ";
            $obj          = $this->getDI()->get('db_rby')->query($sql);
            $returnData   = $obj->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        } else {
            $sql = "--
                SELECT 
                    id,
                    name
                FROM `sys_store`
                WHERE state = 1
                {$where}
            ";
            $obj          = $this->getDI()->get('db_rby')->query($sql);
            $returnData   = $obj->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        }
        return $returnData;
    }


    /**
     * 发送站内消息
     * @param array $paramIn
     * @return bool
     */
    public function sendMessageToSubmitter($paramIn = []): bool
    {
        try{
            //获取必须参数
            if (!$paramIn || !$paramIn['staff_info_id'] || !$paramIn['message_title']){
                return false;
            }
            //接收push信息人id
            $staff_info_id      = $paramIn['staff_info_id'];
            //push标题
            $message_title      = $paramIn['message_title'];
            //push标题
            $message_content    = $paramIn['message_content'];
            //模块名称
            $auditType          = $paramIn['type'];

            //拼接push接口数据
            $data = [
                "staff_info_id"     => $staff_info_id,  //提交人ID
                "title"             => $message_title,  //标题
                "category"          => $auditType,      //类别
                "content"           => $message_content,//内容
            ];
            $this->logger->write_log("publicRe:pushMessageToSubmitter-" . json_encode($data),'info');

            //调用push接口
            $fle_rpc = (new ApiClient('bi_rpc','','message_to_backyard', $this->lang));
            $fle_rpc->setParams($data);
            $fle_rpc->execute();

            return true;
        }catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("publicRe:pushAndSendMessageToSubmitter-" . $e->getMessage(),'info');
            return false;
        }
    }

    /**
     * 推送push
     * @param array $paramIn
     * @return bool
     */
    public function sendPushToSubmitter($paramIn = []): bool
    {

        try{
            //获取必须参数
            if (!$paramIn || !$paramIn['staff_info_id'] || !$paramIn['message_title']){
                return false;
            }
            //接收push信息人id
            $staff_info_id      = $paramIn['staff_info_id'];
            //push标题
            $message_title      = $paramIn['message_title'];
            //push标题
            $message_content    = $paramIn['message_content'];
            //模块名称
            $auditType          = $paramIn['type'];
            //获取环境变量
            $svc_call           = env('svc_call','http://192.168.0.230:8001/Svc/call');
            //BY调整页面地址
            $path               = $paramIn['path'] ?? "";

            $extend             = $paramIn['extend'] ?? [];

            //发送push
            switch ($path) {
                case "message_list":
                    //消息列表
                    $message_scheme     = "flashbackyard://fe/tab?index=message";
                    break;
                case "approval_detail":
                    $params = array_map(function ($v, $k) {
                        return sprintf("%s=%s", $k, $v);
                    }, $extend, array_keys($extend));
                    $paramsStr = join('&', $params);
                    $approval_html_url  = env('approval_html_url','flashbackyard://fe/html?url=');
                    $approval_detail_url = (new SettingEnvServer())->getSetVal('approval_detail_url') . '?' . $paramsStr;;
                    $message_scheme     = $approval_html_url . urlencode($approval_detail_url);
                    break;
                default:
                    //默认地址
                    $approval_html_url  = env('approval_html_url','flashbackyard://fe/html?url=');
                    $approval_detail_url= env('approval_detail_url','http://192.168.0.222:90/#/ApprovalDetail/');
                    $message_scheme     = $approval_html_url . urlencode($approval_detail_url);
                    break;
            }

            //拼接push接口数据
            $data = [
                "staff_info_id"     => $staff_info_id,  //上级id
                "src"               => "backyard",      //1:'kit'; 2:'backyard','c';
                "message_title"     => $message_title,  //标题
                "message_content"   => $message_content,//内容
                "message_scheme"    => $message_scheme, //地址
            ];
            $this->getDI()->get('logger')->write_log("publicRe:pushMessage-" . json_encode($data),'info');

            //rpc调用
            $ret = (new ApiClient('bi_rpc', '', 'push_to_staff'));
            $ret->setParams($data);
            $bi_return = $ret->execute();



            return true;
        }catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("publicRe:pushAndSendMessageToSubmitter-" . $e->getMessage(),'info');
            return false;
        }
    }

    /**
     * 给外协公司 登录osm 的 人发 push
     * @param $paramIn
     * @return bool
     */
    public function sendPushToOsmCompany($paramIn)
    {
        try{
            //获取必须参数
            if (empty($paramIn)){
                return false;
            }

            $this->getDI()->get('logger')->write_log("publicRe:pushMessage-osm:" . json_encode($paramIn),'info');

            //rpc调用
            $ret = (new ApiClient('bi_rpcv2', '', 'push.pushToDevices',$this->lang));
            $ret->setParams($paramIn);
            $result = $ret->execute();
            $this->getDI()->get('logger')->write_log("publicRe:sendPushToOsmCompany:result" . json_encode($result),'info');
            return true;
        }catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("publicRe:sendPushToOsmCompany-" . $e->getMessage(),'info');
            return false;
        }
    }
}
