<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;

class SysListRepository extends BaseRepository
{
    protected $db;
    protected $dbFle;

    public function __construct()
    {
        parent::__construct();
        $this->db    = $this->getDI()->get('db');
        $this->dbFle = $this->getDI()->get('db_fle');
    }

    /**
     * @description 获取部门列表
     * @param $paramIn
     * @return array
     */
    public function getDepartmentList($paramIn = [])
    {
        $returnData = [];
        $ids        = $paramIn['ids'] ?? '';
        $columns    = $paramIn['columns'] ?? 'id, name';
        if (empty($ids)) {
            return $returnData;
        }
        $ids = is_array($ids) ? $ids : explode(',', $ids);
        return SysDepartmentModel::find([
            'conditions' => 'id in({ids:array})',
            'bind'       => [
                'ids' => $ids,
            ],
            'columns'    => $columns,
        ])->toArray();
    }

    /**
     * @description 获取网点列表(有序)
     * @param $paramIn
     * @return array
     */
    public function getStoreList($paramIn = [])
    {
        $returnData = [];
        $ids        = $paramIn['ids'] ?? '';
        if (empty($ids)) {
            return $returnData;
        }
        $data   = $this->getStoreListById($ids);
        $data[] = [
            'id'   => '-1',
            'name' => 'Head Office',
        ];
        return array_reverse($data);
    }

    /**
     * @description 获取网点列表（无序）
     * @return array
     */
    public function getStoreUnorderList($paramIn = [])
    {
        $returnData = [];
        $ids        = $paramIn['ids'] ?? '';
        if (empty($ids)) {
            return $returnData;
        }
        $data   = $this->getStoreListById($ids);
        $data[] = [
            'id'   => '-1',
            'name' => 'Head Office',
        ];
        return $data;
    }

    /**
     * @description 根据网点Id 获取网点信息
     * @param $store_ids
     * @return array
     */
    private function getStoreListById($store_ids)
    {
        if (empty($store_ids)) {
            return [];
        }
        $store_ids = is_array($store_ids)? $store_ids: explode(',', $store_ids);
        $store_ids = array_map(function ($v) {
            return trim($v, '\'\"');
        }, $store_ids);
        return SysStoreModel::find([
            'conditions' => 'id in({ids:array})',
            'bind'       => [
                'ids' => $store_ids,
            ],
            'columns'    => 'id, detail_address, name',
        ])->toArray();
    }

    /**
     * @description 获取职位列表
     */
    public function getPositionList($paramIn = [])
    {
        $returnData = [];
        $ids        = $paramIn['ids'] ?? '';
        if (empty($ids)) {
            return $returnData;
        }
        $ids = is_array($ids)? $ids: explode(',', $ids);
        return HrJobTitleModel::find([
            'conditions' => 'id in({ids:array})',
            'bind'       => [
                'ids' => $ids,
            ],
            'columns'    => 'id, status, job_name as name',
        ])->toArray();
    }

    /**
     * 员工信息列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getStaffList($paramIn = [])
    {
        $returnData = [];
        $ids        = $paramIn['ids'] ?? '';
        if (!$ids) {
            return $returnData;
        }
        $sql        = "--
                    SELECT
                        id,
                        `name`,
                        `organization_type`,
                        `job_title`,
                        `organization_id` as store_id,
                        mobile,
                        hire_date
                    FROM
                        staff_info 
                    WHERE
                        id IN ( {$ids} )
                    ";
        $obj        = $this->dbFle->query($sql);
        $returnData = $obj->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    public function getRegionList($paramIn = [])
    {
        $returnData = [];
        $ids        = $paramIn['ids'] ?? '';
        if (empty($ids)) {
            return $returnData;
        }
        $ids = is_array($ids)? $ids: explode(',', $ids);
        return SysManageRegionModel::find([
            'conditions' => 'id in({ids:array})',
            'bind'       => [
                'ids' => $ids,
            ],
            'columns'    => 'id, name',
        ])->toArray();
    }

    public function getPieceList($paramIn = [])
    {
        $returnData = [];
        $ids        = $paramIn['ids'] ?? '';
        if (empty($ids)) {
            return $returnData;
        }
        $ids = is_array($ids)? $ids: explode(',', $ids);
        return SysManagePieceModel::find([
            'conditions' => 'id in({ids:array})',
            'bind'       => [
                'ids' => $ids,
            ],
            'columns'    => 'id, name',
        ])->toArray();
    }
}