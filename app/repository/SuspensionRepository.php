<?php
/**
 * Author: Bruce
 * Date  : 2024-12-23 19:05
 * Description:
 */

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\SuspensionAuditModel;
use FlashExpress\bi\App\Models\backyard\SuspensionSignConfigModel;

class SuspensionRepository extends BaseRepository
{
    public $timezone;

    public function __construct($timezone)
    {
        parent::__construct();
        $this->timezone = $timezone;
    }
    /**
     * 获取 停职申请信息
     * @param $id
     * @param string $columns
     * @return array
     */
    public static function getInfo($id, $columns = '*')
    {
        $data = SuspensionAuditModel::findFirst(
            [
                'columns'    => $columns,
                'conditions' => "id = :id:",
                'bind'       => [
                    'id' => $id,
                ],
            ]
        );

        return empty($data) ? [] : $data->toArray();
    }

    public static function getAuditInfo($params, $columns = ['*'])
    {
        if(empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind = [];

        if(!empty($params['staff_info_id'])) {
            $conditions .= ' and staff_info_id = :staff_info_id:';
            $bind['staff_info_id'] = $params['staff_info_id'];
        }

        if(!empty($params['id'])) {
            $conditions .= ' and id = :id:';
            $bind['id'] = $params['id'];
        }

        if(!empty($params['audit_states'])) {
            $conditions .= ' and audit_state in ({audit_states:array})';
            $bind['audit_states'] = $params['audit_states'];
        }

        return SuspensionAuditModel::find([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();
    }

    /**
     * 获取 停职签字配置信息
     * @param $staffId
     * @param string $columns
     * @return array
     */
    public static function getSignConfigInfo($staffId, $columns = '*')
    {
        $data = SuspensionSignConfigModel::findFirst(
            [
                'columns'    => $columns,
                'conditions' => "staff_info_id = :staff_info_id: and is_deleted = :is_deleted:",
                'bind'       => [
                    'staff_info_id' => $staffId,
                    'is_deleted'    => SuspensionSignConfigModel::IS_DELETED_NO,
                ],
            ]
        );

        return empty($data) ? [] : $data->toArray();
    }

}