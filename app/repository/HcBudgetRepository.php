<?php


namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\fle\FleSysDepartmentModel;

class HcBudgetRepository extends BaseRepository
{
    public function getAllStoreList() {
        $list = SysStoreModel::find(['columns' => 'id, name', 'conditions' => 'state = 1'])->toArray();
        $head_office = ['id' => -1, 'name' => 'Head Office'];
        array_unshift($list, $head_office);
        $list = array_column($list, null, 'id');

        return $list;
    }

    public function getAllDeptList() {
        $list = FleSysDepartmentModel::find(['columns' => 'id, name', 'conditions' => 'deleted = 0'])->toArray();
        $list = array_column($list, null, 'id');
        return $list;
    }

    public function getAllJobTitleList() {
        $list = HrJobTitleModel::find(['columns' => 'id, job_name', 'conditions' => 'status = 1'])->toArray();
        $list = array_column($list, null, 'id');
        return $list;
    }

}