<?php
/**
 * Author: Bruce
 * Date  : 2025-01-09 21:18
 * Description:
 */

namespace FlashExpress\bi\App\Repository;



use FlashExpress\bi\App\Models\backyard\PayrollSendFile2316Model;

class PayrollSendFileRepository extends BaseRepository
{
    public function __construct($lang = 'zh-CN',$timezone)
    {
        parent::__construct($lang);
        $this->timezone =  $timezone;
    }

    public static function getOne($params, $columns = ['*'])
    {
        if(empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind = [];

        if(!empty($params['id'])) {
            $conditions .= ' and id = :id:';
            $bind['id'] = $params['id'];
        }

        $data = PayrollSendFile2316Model::findFirst([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);

        return empty($data) ? [] : $data->toArray();
    }
}