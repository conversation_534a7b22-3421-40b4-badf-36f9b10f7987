<?php
/**
 * Created by PhpStorm.
 * User: zyp
 * Date: 2020-01-07
 * Time: 14:29
 */

namespace FlashExpress\bi\App\Repository;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\ReportAbsenteeismLogModel;
use FlashExpress\bi\App\Models\backyard\ReportAuditLogModel;
use FlashExpress\bi\App\Models\backyard\ReportAuditModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\ViolationReasonTypeModel;
use FlashExpress\bi\App\Repository\StaffRepository;

use FlashExpress\bi\App\library\Particle;
use FlashExpress\bi\App\Server\SettingEnvServer;

class ReportRepository extends BaseRepository
{
    // 举报类型：迟到/早退 <= 10 分钟超过1次
    const REPORT_TYPE_LATE_EARLY = 33;

    // 举报类型：无故连续旷工1天
    const REPORT_TYPE_ABSENTEEISM = 31;
    // 举报类型：未告知上级或无故旷工
    const REPORT_TYPE_ABSENTEEISM_SUPERIOR = 12;
    const REPORT_TYPE_VIOLATE_REGULATIONS = 6;//违反公司的命令/通知/规则/纪律/规定

    public $timezone;

    public static $reportWk = [
        'report_wk_flow_1' => [2,4,6,8,10,12,13,15,17,19,21,22,23,24,25,26,27],
        'report_wk_flow_2' => [5,9,16,18,20,28,29,30],
        'report_wk_flow_3' => [11],
    ];
    public static $reportType = [
        '1' => 'type-1', // 调查
        '2' => 'type-2', // 发警告信
        '3' => 'type-3', // 开除
    ];
    public function __construct($lang = 'zh-CN',$timezone)
    {
        parent::__construct($lang);
        $this->timezone =  $timezone;
        $this->staff = new StaffRepository();
    }
    public function initialize()
    {

        parent::initialize();
    }

    /**
     * 创建
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addReportR($data , $imageData = [])
    {
        $insetData = $data;
        $db = $this->getDI()->get('db');
        // 插入
        try {
            $db->begin();
            $successImg =1;
            // 动态生成SQL语句(另一种语法)
            $success = $db->insertAsDict('report_audit', $insetData);
            $reportId = $db->lastInsertId();

            if($imageData['image_path'] ){
                $insertImgDataList = array();
                foreach ($imageData['image_path'] as $v){
                    $urlInfo = parse_url($v);//完整url
                    $insertImgData['id'] = Particle::generateParticle();
                    $insertImgData['oss_bucket_type'] = 'REPORT_AUDIT_IMG';
                    $insertImgData['oss_bucket_key'] = $reportId;
                    $insertImgData['bucket_name'] = substr($urlInfo['host'],0, strpos($urlInfo['host'], '.'));
                    $insertImgData['object_key'] = trim($urlInfo['path'], '/');
                    $insertImgData['original_name'] = '举报图片';
                    $insertImgDataList[] = $insertImgData;
                }
                if(!empty($insertImgDataList))
                $successImg = $this->batch_insert('sys_attachment', $insertImgDataList);
            }

            if (!$success || !$successImg  ) {
                $db->rollback();
                return false;
            }
            $db->commit();
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('report addReportR'. $e->getMessage());
            $db->rollback();
            return false;
        }
        return $reportId;
    }

    /**
     * 费用记录
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getReportR($paramIn)
    {
        $id = isset($paramIn['id']) ? $paramIn['id'] : $paramIn;
        $sql = " SELECT id,report_type,reason,remark,reject_reason,warning_status,report_id,submitter_id,event_date,status,final_approver,final_approval_time,CONVERT_TZ( created_at, '+00:00', '".$this->timezone."' ) AS created_at,
CONVERT_TZ( updated_at, '+00:00', '".$this->timezone."' ) AS updated_at,wf_role,serial_no,staff_department_id,staff_job_title,staff_store_id,submitter_department_id,submitter_job_title  FROM report_audit where id = {$id}  ;";
        $info_data = $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $info_data;
    }
    /**
     * 根据类型和id获取附件sys_attachment
     * @param $staff_id
     */
    public function getAttachment($id,$type){

        $sql = "select * from sys_attachment where oss_bucket_type = '{$type}' and oss_bucket_key = {$id} and deleted = 0 ;";
        $data = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $data ??[];
    }
    /**
     * 申请日志
     * @Access  public
     * staff_audit_union 更新+插入 106同意+107待处理 103驳回 104撤销[多条数据]
     * @Param   request
     * @Return  array
     */
    public function insterUnion( $union)
    {
        $db = $this->getDI()->get('db');
        try {
            $db->begin();
            $this->batch_insert( 'staff_audit_union' , $union );
            $db->commit();
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('report:insterUnion'. $e->getMessage());
            $db->rollback();
            return false;
        }
        return true;
    }
    /**
     * 审批日志插入
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function auditApprovalUpdateInsert( $costData, $userinfo )
    {


        $db = $this->getDI()->get('db');
        // 插入
        try {
            $db->begin();
            // 动态生成SQL语句(另一种语法)
            if($costData['status'] == 1){
                $insetData = [
                    'audit_id' => $costData['id'],
                    'type'     => 17,
                    'level'    => 1,
                    'status'   => 7,  //待审批
                    'submitter_id' => $costData['submitter_id'],
                    'staff_ids' => $userinfo['staff_ids'] ?? 0
                ];
                $success = $db->insertAsDict('staff_audit_approval', $insetData);
            }else{
                //update staff_audit_approval set status = {$status} where audit_id = {$id} and type = {$type} and status = 7
                $success = $db->updateAsDict(
                    'staff_audit_approval',
                    [
                        'status' => 7,//$costData['status'],
                    ],
                    [
                        'conditions' => ' audit_id =  ' . $costData['id'] . ' and  type = 15 '
                    ]
                );
                $db->affectedRows();
            }
            if (!$success) {
                $db->rollback();
                return false;
            }
            $lastId = $db->lastInsertId();
            $db->commit();
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('report:auditApprovalUpdateInsert'. $e->getMessage());
            $db->rollback();
            return false;
        }
        return $lastId;
    }
    /**
     * 非最终的同意状态
     * @Access  public
     * staff_audit_tool_log
     * staff_audit_union 更新+插入 106同意+107待处理 103驳回 104撤销[多条数据]
     * staff_audit_approval 更新当前步骤，且插入下一步信息: 更新+插入 3驳回 4撤销 6同意+7待处理[多人一条数据]
     * @Param   request
     * @Return  array
     */
    public function insertUnionApprovalLog( $union,$approvalInster,$log )
    {
        $db = $this->getDI()->get('db');
        // 插入
        $insetlog = $this->getInsertDbSql('staff_audit_tool_log', $log);
        $insetapp = $this->getInsertDbSql( 'staff_audit_approval' , $approvalInster );
        //更新 staff_audit_union staff_audit_approval where id;
        $up_usql = "update staff_audit_union set status_union = 106   where origin_id = {$log['original_id']} and type_union = 17 and status_union = 107  ;";
        $up_asql = "update staff_audit_approval set status = 6 where audit_id = {$log['original_id']} and type = 17 and status = 7 ;";
        try {
            $db->begin();
            $db->execute($up_usql);
            $db->execute($up_asql);
            $db->execute($insetlog);
            $this->batch_insert( 'staff_audit_union' , $union );
            //$db->execute($insetunion);//先更新旧的，不要重复执行插入
            $db->execute($insetapp);
            $db->commit();
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('report:insertUnionApprovalLog'. $e->getMessage());
            $db->rollback();
            return false;
        }
        return true;
    }

    /**
     * @param 最终状态（最终同意和不同意情况下操作）
     * cost_audit
     * taff_audit_tool_log
     * staff_audit_union 更新+插入 106同意+107待处理 103驳回 104撤销
     * staff_audit_approval 更新当前步骤，且插入下一步信息: 更新+插入 3驳回 4撤销 6同意+7待处理
     * @param 日志表组装数据 更改状态 和相关日志
     */
    public function cancel($log, $updateReport)
    {
        $status_union = $updateReport['status'];
        if($updateReport['status'] == 2){
            $status_union = 6;
        }
        $insetSql = $this->getInsertDbSql('staff_audit_tool_log', $log);
        //更新 staff_audit_union staff_audit_approval where id;
        //approval_id ={$updateCost['final_approver']},
        $up_firstunsql = "update staff_audit_union set status_union = 10{$updateReport['status']}   where origin_id = {$updateReport['id']} and type_union = 17 and status_union = 101  ;";
        $up_usql = "update staff_audit_union set status_union = 10{$status_union}   where origin_id = {$updateReport['id']} and type_union = 17 and status_union = 107  ;";
        $up_asql = "update staff_audit_approval set status = {$status_union} where audit_id = {$updateReport['id']} and type = 17 and status = 7 ;";
        $up_sql = "update report_audit set status = {$updateReport['status']} ,reject_reason = '{$updateReport['reject_reason']}' , final_approver = {$updateReport['final_approver']}, final_approval_time = now() where id = '{$updateReport['id']}' ;";
        $db = $this->getDI()->get('db');
        try {
            $db->begin();
            $db->execute($up_firstunsql);
            $db->execute($up_usql);
            $db->execute($up_asql);
            $db->execute($up_sql);

            $db->execute($insetSql);
            $db->commit();
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('reportRe:cancel' . $e->getMessage());
            $db->rollback();
            return false;
        }
        return true;
    }

    /**
     * @param taff_audit_tool_log  日志表组装数据
     */
    public function insertLog($reportArr = [], $uinfo = [])
    {
        $log['staff_id'] = $reportArr['submitter_id'];     //申请人
        $log['type'] = 17;
        $log['original_type'] = $reportArr['status'];     //原始状态
        $log['to_status_type'] = $reportArr['status'];     //修改后状态
        $log['original_id'] = $reportArr['id'];     //关联id
        $log['operator'] = $uinfo['id'];     //操作人
        $log['operator_name'] = $uinfo['name'];     //操作人名称
        $insetSql = $this->getInsertDbSql('staff_audit_tool_log', $log);
        $db = $this->getDI()->get('db');
        $res = $db->execute($insetSql);
        return $res;
    }
    /**
     *  获取全部举报分类和原因
     * @return array
     * CreateTime: 2020/10/21 0021 17:44
     */
    public function dictReportResonAll($isDetail = false){
        $reasonArr = [];
        $reportType = self::$reportType;
        $typeArr = [];
        foreach ($reportType as $id => $type) {
            $typeArr[$id] = [
                'id' => $id,
                'type' => $this->getTranslation()->_($type)
            ];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['t_id,t_key,remark,type']);
        $builder->from(ViolationReasonTypeModel::class);
        $builder->where('is_deleted = :is_deleted:', ['is_deleted' => ViolationReasonTypeModel::UN_DELETED]);
        $builder->groupBy('t_id');
        if (isCountry('PH')) {
            $builder->andWhere('pid > 0');
            $builder->orderBy('sort desc,id asc');
        } else {
            $builder->orderBy('sort desc,id desc');
        }
        $list = $builder->getQuery()->execute()->toArray();
        foreach ($list as $reason) {
            $reasonArr[$reason['t_id']] = [
                'id' => $reason['t_id'],
                'reason' => $this->getTranslation()->_($reason['t_key']),
            ];
        }
        $data['data'] = [
            'reason' => $reasonArr,
            'type' => $typeArr,
        ];
        return $data;
    }

    /** 获取举报的分类
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function dictReportR($isSpecialPositions = false, $flag = false, $is_hire_type_un_paid = false, $otherTypes = [])
    {
        $t = $this->getTranslation();
        $data = [];
        $reasonArr = [];
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['t_id,t_key,remark,type']);
        $builder->from(ViolationReasonTypeModel::class);
        $builder->where('is_deleted = :is_deleted:', ['is_deleted' => ViolationReasonTypeModel::UN_DELETED]);
        $reportType = self::$reportType;
        if($isSpecialPositions && $flag) {
            if (isCountry('MY') && $is_hire_type_un_paid === true){
                // 个人代理qaqc 目前仅my有
                $type_ids = [ViolationReasonTypeModel::TYPE_UNPAID_QAQC];
                if(!empty($otherTypes)) {
                    $type_ids = array_values(array_unique(array_merge($type_ids, $otherTypes)));
                }
                $builder->andWhere('type in ({type_ids:array})', ['type_ids' => $type_ids]);
            }else{
                $type_ids = [ViolationReasonTypeModel::TYPE_NO_UNPAID_QAQC];
                if(!empty($otherTypes)) {
                    $type_ids = array_values(array_unique(array_merge($type_ids, $otherTypes)));
                }
                // 正式qaqc
                $builder->andWhere('type in ({type_ids:array})', ['type_ids' => $type_ids]);
            }
        } else {
            if (isCountry('MY') && $is_hire_type_un_paid === true){
                // 个人代理上级 目前仅my有
                $type_ids = [ViolationReasonTypeModel::TYPE_UNPAID_SUPERIOR];
                if(!empty($otherTypes)) {
                    $type_ids = array_values(array_unique(array_merge($type_ids, $otherTypes)));
                }
                $builder->andWhere('type in ({type_ids:array})', ['type_ids' => $type_ids]);
            }else{
                // 正式上级
                $type_ids = [ViolationReasonTypeModel::TYPE_NO_UNPAID_SUPERIOR];
                if(!empty($otherTypes)) {
                    $type_ids = array_values(array_unique(array_merge($type_ids, $otherTypes)));
                }
                $builder->andWhere('type in ({type_ids:array})', ['type_ids' => $type_ids]);
            }
            unset($reportType['3']);
        }
        $builder->groupBy('t_id');
        $builder->orderBy('sort desc,id desc');
        $list = $builder->getQuery()->execute()->toArray();
        foreach ($list as $reason) {
            $reasonArr[] = [
                'id' => $reason['t_id'],
                'reason' => $t->_($reason['t_key']),
            ];
        }
        $typeArr = [];
        foreach ($reportType as $id => $type) {
            $typeArr[$id] = [
                'id' => $id,
                'type' => $t->_($type)
            ];
        }
        $data['data'] = [
            'reason' => $reasonArr,
            'type' => $typeArr,
        ];
        return $data;
    }

    public function getStaffInfo($staffId)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['hsi.staff_info_id as staff_id', 'hsi.name', 'hjt.job_name', 'ss.name as store_name', 'ss.manage_piece', 'ss.manage_region', 'hsi.sys_store_id', 'hsi.node_department_id', 'hsi.hire_type']);
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftJoin(SysStoreModel::class, 'ss.id = hsi.sys_store_id', 'ss');
        $builder->leftJoin(HrJobTitleModel::class, 'hjt.id = hsi.job_title', 'hjt');

        $builder->where('hsi.staff_info_id = :staff_id:', ['staff_id' => $staffId]);
        $staffInfo = $builder->getQuery()->getSingleResult();
        if(empty($staffInfo)) {
            return [];
        }
        $staffInfo = $staffInfo->toArray();
        if($staffInfo['sys_store_id'] == enums::HEAD_OFFICE_ID) {
            $staffInfo['store_name'] = enums::HEAD_OFFICE;
        }
        $staffInfo['hire_type_text'] = !empty($staffInfo['hire_type']) ? $this->getTranslation()->_('hire_type_' . $staffInfo['hire_type']) : '';
        

        return $staffInfo;
    }

    public function getStaffInfoFormal($staffId)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['hsi.staff_info_id as staff_id', 'hsi.name', 'hjt.job_name', 'ss.name as store_name', 'ss.manage_piece', 'ss.manage_region', 'hsi.sys_store_id', 'hsi.node_department_id', 'hsi.hire_type']);
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftJoin(SysStoreModel::class, 'ss.id = hsi.sys_store_id', 'ss');
        $builder->leftJoin(HrJobTitleModel::class, 'hjt.id = hsi.job_title', 'hjt');

        $builder->where('hsi.staff_info_id = :staff_id:', ['staff_id' => $staffId]);
        $builder->andWhere('hsi.formal in ({formal:array})', ['formal' => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN]]);
        $builder->andWhere('hsi.is_sub_staff = :is_sub_staff:', ['is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_0]);
        $staffInfo = $builder->getQuery()->getSingleResult();
        if(empty($staffInfo)) {
            return [];
        }
        $staffInfo = $staffInfo->toArray();
        if($staffInfo['sys_store_id'] == enums::HEAD_OFFICE_ID) {
            $staffInfo['store_name'] = enums::HEAD_OFFICE;
        }
        $staffInfo['hire_type_text'] = !empty($staffInfo['hire_type']) ? $this->getTranslation()->_('hire_type_' . $staffInfo['hire_type']) : '';


        return $staffInfo;
    }
    /**
     * 违规原因 列表
     * 举报原因 列表
     * @param bool $is_need_deleted
     * @return mixed
     */
    public function violationReasonTypeList(bool $is_need_deleted = true,$is_need_pid = true, $otherTypes = [])
    {
        $t = $this->getTranslation();
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['t_id as id ','t_key','pid','event_desc_key']);
        $builder->from(ViolationReasonTypeModel::class);
        $type_ids = [ViolationReasonTypeModel::TYPE_PH_NO_UNPAID];
        if(!empty($otherTypes)) {
            $type_ids = array_values(array_unique(array_merge($type_ids, $otherTypes)));
        }
        $builder->where('type in ({type_ids:array})', ['type_ids' => $type_ids]);
        if (!$is_need_deleted) {
            $builder->andWhere('is_deleted = :is_deleted:', ['is_deleted' => ViolationReasonTypeModel::UN_DELETED]);
        }
        if (!$is_need_pid) {
            $builder->andWhere('pid > 0 ');
        }

        $builder->groupBy('t_id');
        $builder->orderBy('sort desc,id asc');
        $list = $builder->getQuery()->execute()->toArray();
        foreach ($list as &$v) {
            $v['reason'] = $v['t_key'] ? $t->_($v['t_key']) : '';
            $v['remark'] = $v['event_desc_key'] ? $t->_($v['event_desc_key']) : '';
        }
        return $list;
    }

    public static function getAuditOne($params, $columns = ['*'])
    {
        if(empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind = [];

        if(!empty($params['id'])) {
            $conditions .= ' and id = :id:';
            $bind['id'] = $params['id'];
        }

        $data = ReportAuditModel::findFirst([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);

        return empty($data) ? [] : $data->toArray();
    }

    public static function getAbsenteeismInfo($params, $columns = ['*'])
    {
        if(empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind = [];

        if(!empty($params['id'])) {
            $conditions .= ' and id = :id:';
            $bind['id'] = $params['id'];
        }

        if(!empty($params['attendance_date']) && is_array($params['attendance_date'])) {
            $conditions .= ' and attendance_date in ({attendance_date:array})';
            $bind['attendance_date'] = $params['attendance_date'];
        }

        if(!empty($params['state'])) {
            $conditions .= ' and report_state = :state:';
            $bind['state'] = $params['state'];
        }

        if(!empty($params['type'])) {
            $conditions .= ' and type = :type:';
            $bind['type'] = $params['type'];
        }

        if(!empty($params['report_month'])) {
            $conditions .= ' and report_month = :report_month:';
            $bind['report_month'] = $params['report_month'];
        }

        return ReportAbsenteeismLogModel::find([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();
    }

    public static function getAuditLogOne($params, $columns = ['*'])
    {
        if(empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind = [];

        if(!empty($params['report_id'])) {
            $conditions .= ' and report_id = :report_id:';
            $bind['report_id'] = $params['report_id'];
        }

        $data = ReportAuditLogModel::findFirst([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
            'order'      => 'id desc',
        ]);

        return empty($data) ? [] : $data->toArray();
    }


}
