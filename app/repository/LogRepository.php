<?php

namespace FlashExpress\bi\App\Repository;

class LogRepository extends BaseRepository
{
    public function __construct()
    {
        parent::__construct();
        $this->db = $this->getDI()->get('db');

        //模块类型
//        $this->typeArr = [
//            1 => $this->getTranslation()->_('6100'),   //面试
//            2 => $this->getTranslation()->_('6101'),   //offer
//            3 => $this->getTranslation()->_('6102'),   //入职
//            4 => $this->getTranslation()->_('6103'),   //面试反馈
//            5 => $this->getTranslation()->_('6118'),   //简历
//            6 => $this->getTranslation()->_('6121'),   //基本信息
//            7 => $this->getTranslation()->_('6122'),   //家庭信息
//            8 => $this->getTranslation()->_('6123'),   //经历与能力
//            9 => $this->getTranslation()->_('6124'),   //相关问卷
//            10 => $this->getTranslation()->_('6125'),   //附件信息
//        ];
//
//        //状态
//        $this->interviewStatusArr = [
//            1 => $this->getTranslation()->_('6104'),   //进入下一步
//            2 => $this->getTranslation()->_('6105'),   //不通过
//            3 => $this->getTranslation()->_('6106'),   //终试通过
//            4 => $this->getTranslation()->_('6107'),   //修改内容
//        ];
//
//        //动作
//        $this->actionArr = [
//            1 => $this->getTranslation()->_('6108'),   // 创建
//            2 => $this->getTranslation()->_('8360'),   // 修改
//            3 => $this->getTranslation()->_('6110'),   // 删除
//            4 => $this->getTranslation()->_('6111'),   // 取消
//            5 => $this->getTranslation()->_('6112'),   // 查看
//            6 => $this->getTranslation()->_('6113'),   // 预约
//            7 => $this->getTranslation()->_('6114'),   // 填写
//            8 => $this->getTranslation()->_('6115'),   // 发送
//            9 => $this->getTranslation()->_('6116'),   // 办理
//            10 => $this->getTranslation()->_('6117'),  // 反馈
//        ];
    }

    /**
     * 日志添加
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addLog($paramIn = [])
    {
        $staffInfoId = $paramIn['staff_info_id'];
        $moduleId = $paramIn['module_id'];
        $moduleType = $paramIn['module_type'];
        $moduleLevel = $paramIn['module_level'];
        $moduleStatus = $paramIn['module_status'];
        $action = $paramIn['action'];
        $userIp = $paramIn["user_ip"];
        $dataBefore = $paramIn['data_before'];
        $dataAfter = $paramIn['data_after'];
        $data = [
            'staff_info_id' => $staffInfoId,
            'module_id' => $moduleId,
            'module_type' => $moduleType,
            'module_level' => $moduleLevel,
            'module_status' => $moduleStatus,
            'action' => $action,
            'user_ip' => $userIp,
            'data_before' => $dataBefore,
            'data_after' => $dataAfter
        ];
        $this->db->begin();
        $data = $this->db->insertAsDict(
            'hr_log', $data
        );
        if (!$data) {
            $this->db->rollback();
            return false;
        }
        $id = $this->db->lastInsertId();
        $this->db->commit();
        return $id;

    }


    /**
     * 获取修改前数据和修改后数据
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getDataDiff($paramIn = [])
    {
        try {
            $moduleId = $paramIn['module_id'];
            $moduleType = $paramIn['module_type'];
            $action = $paramIn['action'];
            $dataAfter = $paramIn['data_after'];
            $fieldsArr = array_keys($dataAfter);
            $fields = implode(",", $fieldsArr);
            switch ($moduleType) {
                case "1":
                    //面试
                    $sql = "select " . $fields . " from hr_interview where interview_id = " . $moduleId;
                    break;
                case "2":
                    //offer
                    $sql = "select " . $fields . " from hr_interview_offer where id = " . $moduleId;
                    break;
                case "3":
                    //入职
                    $sql = "select " . $fields . " from hr_entry where entry_id = " . $moduleId;
                    break;
                case "4":
                    //面试反馈
                    $sql = "select " . $fields . " from hr_interview_info where interview_info_id = " . $moduleId;
                    break;
                case "5":
                    //简历
                    $sql = "select " . $fields . " from hr_resume where id = " . $moduleId;
                    break;
                case "6":
                    //简历-基本信息
                    $sql = "select " . $fields . " from hr_resume where id = " . $moduleId;
                    break;
                case "7":
                    //简历-家庭信息
                    $sql = "select " . $fields . " from hr_family where id = " . $moduleId;
                    break;
                case "8":
                    //简历-经济与能力
                    $sql = "select " . $fields . " from hr_economy_ability where id = " . $moduleId;
                    break;
                case "9":
                    //简历-相关问卷
                    $sql = "select " . $fields . " from hr_survey_question where id = " . $moduleId;
                    break;
                case "10":
                    //简历-附件信息
                    $sql = "select " . $fields . " from hr_annex where id = " . $moduleId;
                    break;
                default:
                    return true;
            }
            $obj = $this->db->query($sql);
            $data = $obj->fetch(\Phalcon\Db::FETCH_ASSOC);
            //更新前数据
            $dataAfterArr = array_diff($dataAfter, $data);
            $result['data_after'] = json_encode($dataAfterArr);
            //更新后数据
            $dataBeforeArr = array_diff($data, $dataAfter);
            $result['data_before'] = json_encode($dataBeforeArr);

            return $result;
        }catch (\Exception $e){
            return [];
        }

    }

}