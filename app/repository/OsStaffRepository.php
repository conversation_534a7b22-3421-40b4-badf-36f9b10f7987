<?php

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrShiftModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffOutsourcingModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;

class OsStaffRepository extends BaseRepository
{
    public $timezone;

    public function __construct($timezone)
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    /**
     * 创建外协员工申请
     */
    public function insertOsStaff($insert)
    {
        try {
            $insertSql = $this->getInsertDbSql('hr_staff_outsourcing', $insert);
            $this->getDI()->get('db')->query($insertSql);
            $last_id = $this->getDI()->get('db')->lastInsertId();
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("osStaffRe:insertOsStaff:" . $e->getMessage());
            $last_id = '';
        }
        return $last_id;
    }

    /**
     * 更新外协员工申请
     * @param array $paramIn
     * @return boolean
     */
    public function updateOsStaff($paramIn = [])
    {
        $status          = $paramIn['status'];
        $reject_reason   = $paramIn['reject_reason'];
        $auditId         = $paramIn['audit_id'];
        $auditNum        = $paramIn['final_audit_num'];
        $db              = $this->getDI()->get('db');
        $db->begin();
        try {
            $sql  = "update hr_staff_outsourcing set status= {$status}, reject_reason='{$reject_reason}', final_audit_num = {$auditNum} where id = $auditId";
            $db->execute($sql);
            $db->commit();
        }catch (\Exception $e){
            /* 有异常回滚 */
            $db->rollback();
            $this->getDI()->get('logger')->write_log("osStaffRe:updateOsStaff:" . $e->getMessage());
        }
        $affectedRows = $this->getDI()->get('db')->affectedRows();
        return $affectedRows !== 0 ? true: false;
    }

    /**
     * 获取外协员工详情
     * @param int $id   主键ID
     * @return array
     */
    public function getOsStaffInfo($id)
    {
        if (empty($id)) {
            return [];
        }

        $querySql = "
            --
            SELECT 
                id
                ,serial_no
                ,os_type
                ,source_category
                ,staff_id
                ,job_id
                ,department_id
                ,store_id
                ,employment_date
                ,employment_days
                ,demend_num
                ,shift_id
                ,final_audit_num
                ,status
                ,reason_type as reason
                ,reason as remark
                ,reject_reason
                ,CONVERT_TZ( created_at, '+00:00', '{$this->timezone}' ) AS created_at
                ,CONVERT_TZ( updated_at, '+00:00', '{$this->timezone}' ) AS updated_at
                ,wf_role
                ,hire_os_type
                ,out_company_id
                ,out_company_data
                ,shift_begin_time
                ,shift_end_time
                ,work_partition
            FROM `hr_staff_outsourcing` WHERE `id` = {$id}";
        $returnData = $this->getDI()->get('db')->query($querySql)->fetch(\Phalcon\Db::FETCH_ASSOC);

        if ($returnData) {
            $job_title              = HrJobTitleModel::findFirst(['conditions' => 'id = :id: ',
                                                                  'bind'       => ['id' => $returnData['job_id'] ?? ''],
                                                                  'columns'    => 'id,job_name',
            ]);

            $returnData['job_title']    = $job_title->job_name ?? '';
            $returnData['department']   = 'Operations';

            $query_img_sql = "
                --
                select 
                    bucket_name
                    ,object_key
                from sys_attachment 
                where oss_bucket_key = '{$id}'
                    and oss_bucket_type = 'OUTSOURCING_STAFF' 
                    and deleted = 0";
            $images = $this->getDI()->get('db')->query($query_img_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if (!empty($images)) {
                $returnData['image_path'] = [];
                foreach ($images as $image) {
                    array_push($returnData['image_path'], convertImgUrl($image['bucket_name'], $image['object_key']));
                }
            }
        }
        return $returnData;
    }

    /**
     * 获取班次列表
     */
    public function getShiftList()
    {
//        //泽铭需求
//        //暂时排除部分班次
//        $query_sql  = "
//            --
//            SELECT
//                id
//                ,type
//                ,start
//                ,end
//            FROM `hr_shift`
//            WHERE id NOT IN (35,36,37,38,39,40,41,42,44,43,45,46,47,48,49)
//            and shift_attendance_type = :shift_attendance_type
//            order by type,start asc";
//        //有 end  不能使用 ->find ??
//        $returnData = $this->getDI()->get('db_rby')->query($query_sql,['shift_attendance_type'=>HrShiftModel::SHIFT_ATTENDANCE_TYPE_FIXED])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
//        return $returnData;
        return HrShiftModel::find([
            'columns'    => 'id,type,start,[end]',
            'conditions' => 'id NOT IN ({ids:array}) and shift_attendance_type = :shift_attendance_type: and shift_group = :shift_group:',
            'bind'       => [
                'ids' => [35,36,37,38,39,40,41,42,44,43,45,46,47,48,49],
                'shift_attendance_type' => HrShiftModel::SHIFT_ATTENDANCE_TYPE_FIXED,
                'shift_group' => HrShiftModel::SHIFT_GROUP_FULL_DAY_SHIFT
            ],
            'order'      => "type,start asc",
        ])->toArray();
    }

    /**
     * 查找工作班次
     */
    public function getWorkShift($shiftId)
    {
        if (empty($shiftId)) {
            return [];
        }

        $returnData = HrShiftModel::findFirst([
            'columns'    => 'type,[start],[end],shift_group',
            'conditions' => "id = :id:",
            'bind' => ['id' => $shiftId]
        ]);

        return empty($returnData) ? [] : $returnData->toArray();
    }

    /**
     * 获取外协员工审批参考信息
     */
    public function getOsStaffReferInfo($store_id, $start_time, $end_time)
    {
        if (empty($store_id) || $store_id == -1) {
            return [];
        }

        //应派件数
        $ac = new ApiClient('ard_api', '', 'dc.should_delivery_today', $this->lang);
        $ac->setParams(['store_id'   => $store_id,
                        'start_date' => date('Y-m-d', $start_time),
                        'end_date'   => date('Y-m-d', $end_time),
        ]);
        $ac_result = $ac->execute();
        if (!empty($ac_result['result']['data'])) {
            $list = array_column($ac_result['result']['data'], 'delivery_cnt', 'stat_date');
        }
        $returnData['cnt'] = $list ?? [];

        //人均妥投计算项 - 查询网点当日有效妥投件数
        $querySql = "
            --
            select 
                format(a.delivery_count/delivery_man_count,2) as avg_count
                ,date_format(a.created_at, '%m-%d') as stat_date
                ,a.created_at
                from  delivery_avg_count as a
                inner join sys_store as b on a.store_id=b.id
                WHERE a.store_id = '{$store_id}' and b.category not in (4,6) 
                and a.created_at between  '" . date("Y-m-d", $start_time) . "' and '" . date("Y-m-d", $end_time) . "'
                order by a.created_at desc limit 0,7
        ";
        $dataAvg = $this->getDI()->get('db_rbi')->query($querySql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $returnData['avg'] = array_column($dataAvg, 'avg_count', 'stat_date');

        //揽件量
        $undertakeSql = "
            --
            SELECT 
                delivery_num as undertake_cnt
                ,date_format(day_date, '%m-%d') as stat_date
            FROM `pickup_trend` 
            where 
                `store_id` = '{$store_id}'
                and day_date between  '" . date("Y-m-d", $start_time) . "' and '" . date("Y-m-d", $end_time) . "'
            ORDER BY `day_date` desc
        ";
        $dataCng = $this->getDI()->get('db_rbi')->query($undertakeSql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $returnData['undertake'] = array_column($dataCng, 'undertake_cnt', 'stat_date');
        return $returnData;
    }

    /**
     * 获取指定网点指定职位的在职正式员工数量
     * @param $store_id
     * @param array $job_title
     * @return array
     */
    public function getStoreOnJobStaffNum($store_id, array $job_title): array
    {
        $data = HrStaffInfoModel::find([
            'conditions' => "sys_store_id = :sys_store_id: AND job_title in ({job_title:array}) AND state = :state: AND formal = :formal: AND is_sub_staff = 0 ",
            'columns'    => 'job_title,count(1) as total',
            'bind'       => ['job_title'    => $job_title,
                             'sys_store_id' => $store_id,
                             'state'        => HrStaffInfoModel::STATE_ON_JOB,
                             'formal'       => HrStaffInfoModel::FORMAL_1,
            ],
            'group'      => 'job_title',
        ])->toArray();
        return array_column($data, 'total', 'job_title');
    }


    /**
     * 获取快递员人数-正式在职快递员数量
     */
    public function getCourierCnt($store_id, $job_id)
    {
        if (empty($store_id) || $store_id == -1) {
            return [];
        }

        $countsql = "
            --
            select count(1) from `hr_staff_info` where job_title = {$job_id} and sys_store_id = '{$store_id}' and state = 1 and formal = 1;
        ";
        $data = $this->getDI()->get('db_rby')->fetchColumn($countsql);
        return $data;
    }

    /**
     * 获取网点角色下的在职快递员数量
     */
    public function getCourierSoles($store_id, $roles_id = 2)
    {
        if (empty($store_id) || empty($roles_id)) {
            return [];
        }
        $sql = "
            --
            SELECT
                count(hr_staff_info.id) as num 
            FROM
                hr_staff_info
                LEFT JOIN hr_staff_info_position ON hr_staff_info.staff_info_id = hr_staff_info_position.staff_info_id 
            WHERE
                hr_staff_info_position.position_category = ".$roles_id." 
                AND hr_staff_info.state = 1 
                AND hr_staff_info.formal != 0  AND hr_staff_info.is_sub_staff = 0 
                AND hr_staff_info.sys_store_id = '".$store_id."'
            ";
        $data = $this->getDI()->get('db_rby')->fetchColumn($sql);
        return $data;
    }

    //获取打卡员工数量 不算补卡 fbi rpc 和加班调用
    public function getAttendanceNum($storeId, $dateList, $jobTitles = []){
        if(empty($jobTitles)){
            return 0;
        }
        $data = [];
        foreach($dateList as $date){
            $jobStr = implode(',', $jobTitles);
            //产品说这职位没有支援 可以直接取 不用去重
            $sql    = "SELECT  attendance_date, count(*) as num 
                    FROM staff_work_attendance 
                    WHERE attendance_date = :date_at and job_title in ({$jobStr}) and (started_store_id = :store_id or end_store_id = :store_id)";
            $item   = $this->getDI()->get('db_rby')->query($sql,
                ['date_at' => $date, 'store_id' => $storeId])->fetchAll(\Phalcon\Db::FETCH_ASSOC);

            $data = array_merge($data, $item);
        }
        $data = empty($data) ? [] : array_column($data, 'num', 'attendance_date');
        return $data;
    }

    /**
     * 网点是否存在岛屿
     * @param $store_id
     * @return bool
     */
    public function isStoreIsland($store_id) : bool
    {
        if (empty($store_id)) {
            return false;
        }

        $querySql = "--
                    SELECT 
                        count(1)
                    FROM `sys_district` 
                    WHERE `store_id` = '{$store_id}' and island = 1 and deleted = 0
                    ";
        $data = $this->getDI()->get('db_fle')->fetchColumn($querySql);
        return $data > 0;
    }

    /**
     * 网点是否偏远
     * @param $store_id
     * @return bool
     */
    public function isStoreForaway($store_id) :bool
    {
        if (empty($store_id)) {
            return false;
        }

        $querySql = "--
                    SELECT 
                        count(1)
                    FROM `sys_district` 
                    WHERE `store_id` = '{$store_id}' and upcountry = 1 and deleted = 0
                    ";
        $data = $this->getDI()->get('db_fle')->fetchColumn($querySql);
        return $data > 0;
    }

    /**
     * 获取职位与部门的关联关系
     * @param $departmentId
     * @param $jobTitle
     * @return array
     */
    public function getRelation($departmentId, $jobTitle)
    {
        $querySql = "--
                    SELECT 
                        department_id,
                        job_id
                    FROM `hr_job_department_relation` 
                    WHERE `department_id` = '{$departmentId}' and `job_id` = '{$jobTitle}'
                    ";
        return $this->getDI()->get('db')->fetchAll($querySql);
    }

    /**
     * 获取网点今天短期外协申请记录
     * @param $store_id
     * @return mixed
     */
    public function shortTermApplyR($store_id)
    {
        $today = gmdate("Y-m-d");
        $sql  = "--
            SELECT staff_id FROM `hr_staff_outsourcing` 
            WHERE `store_id` = '{$store_id}' AND os_type = 1 AND created_at >= '{$today}';
             ";
        $returnData = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    public static function getStaffOutsourcingList($params, $columns = ['*'])
    {
        if(empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind = [];

        if(!empty($params['store_id'])) {
            $conditions .= ' and store_id = :store_id:';
            $bind['store_id'] = $params['store_id'];
        }

        if(!empty($params['job_id'])) {
            $conditions .= ' and job_id = :job_id:';
            $bind['job_id'] = $params['job_id'];
        }

        if(!empty($params['employment_date'])) {
            $conditions .= ' and employment_date = :employment_date:';
            $bind['employment_date'] = $params['employment_date'];
        }

        if(!empty($params['shift_id'])) {
            $conditions .= ' and shift_id = :shift_id:';
            $bind['shift_id'] = $params['shift_id'];
        }

        if(!empty($params['status'])) {
            $conditions .= ' and status = :status:';
            $bind['status'] = $params['status'];
        }

        if(!empty($params['id'])) {
            $conditions .= ' and id = :id:';
            $bind['id'] = $params['id'];
        }

        if(isset($params['is_audit'])) {
            if($params['is_audit'] == HrStaffOutsourcingModel::IS_AUDIT_YES) {
                $conditions .= ' and is_audit = :is_audit:';
                $bind['is_audit'] = HrStaffOutsourcingModel::IS_AUDIT_YES;
            }

            if($params['is_audit'] == HrStaffOutsourcingModel::IS_AUDIT_NO) {
                $conditions .= ' and is_audit = :is_audit:';
                $bind['is_audit'] = HrStaffOutsourcingModel::IS_AUDIT_NO;
            }
        }

        return HrStaffOutsourcingModel::find([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();
    }

    public function getPendingOutsourcing($where, $columns = ['*'])
    {
        if(empty($where['time']) || empty($where['job_id']) || empty($where['status'])) {
            return [];
        }

        $builder    = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(HrStaffOutsourcingModel::class);

        $builder->where("STR_TO_DATE(CONCAT(employment_date, ' ', shift_begin_time), '%Y-%m-%d %H:%i') 
  < :time:", ['time' => $where['time']]);

        $builder->andWhere("job_id = :job_id: and status = :status: ", ['job_id' => $where['job_id'], 'status' => $where['status']]);

        return $builder->getQuery()->execute()->toArray();

    }

    public static function getStaffOutsourceOne($params, $columns = ['*'])
    {
        if(empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind = [];

        if(!empty($params['id'])) {
            $conditions .= ' and id = :id:';
            $bind['id'] = $params['id'];
        }

        $data = HrStaffOutsourcingModel::findFirst([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);

        return empty($data) ? [] : $data->toArray();
    }

}
