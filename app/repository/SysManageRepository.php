<?php
/**
 * Author: Bruce
 * Date  : 2024-07-23 01:08
 * Description:
 */

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;

class SysManageRepository extends BaseRepository
{
    public $timezone;

    public function __construct($timezone)
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    public static function getRegionList($params, $columns = ['*'])
    {
        if(empty($params)) {
            return [];
        }
        $condition = "1 = :default:";
        $bind      = ['default' => 1];

        if(!empty($params['ids'])){
            $condition .= " AND id in ({ids:array})";
            $bind['ids'] = $params['ids'];
        }

        if(!empty($params['manager_id'])){
            $condition .= " AND manager_id = :manager_id: AND deleted = :deleted: ";
            $bind['manager_id'] = $params['manager_id'];
            $bind['deleted'] = SysManageRegionModel::IS_NOT_DELETED;
        }

        $data = SysManageRegionModel::find([
            'columns'    => $columns,
            'conditions' => $condition,
            'bind'       => $bind,
        ])->toArray();

        return empty($data) ? [] : array_column($data, NULL, 'id');
    }

    public static function getPieceList($params, $columns = ['*'])
    {
        if(empty($params)) {
            return [];
        }
        $condition = "1 = 1";
        $bind      = [];

        if(!empty($params['ids'])){
            $condition .= " AND id in ({ids:array})";
            $bind['ids'] = $params['ids'];
        }

        if(!empty($params['manager_id'])){
            $condition .= " AND manager_id = :manager_id: AND deleted = :deleted: ";
            $bind['manager_id'] = $params['manager_id'];
            $bind['deleted'] = SysManagePieceModel::IS_NOT_DELETED;
        }

        $data = SysManagePieceModel::find([
            'columns'    => $columns,
            'conditions' => $condition,
            'bind'       => $bind,
        ])->toArray();

        return empty($data) ? [] : array_column($data, NULL, 'id');
    }
}