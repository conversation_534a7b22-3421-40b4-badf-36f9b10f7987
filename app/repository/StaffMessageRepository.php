<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\backyard\HrPenaltyDetailModel;
use FlashExpress\bi\App\Models\coupon\MessageContentModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;

class StaffMessageRepository extends BaseRepository
{

    public function __construct()
    {
        parent::__construct();
    }

    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 参数查询
     * @param $params
     * @param $colum
     * @return array
     */
    public function getListByParams($params, $colum): array
    {
        if (empty($params)) {
            return [];
        }
        $fields  = (!is_array($colum) || empty($colum)) ? "*" : implode(',', $colum);
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($fields);
        $builder->from(['mr' => MessageCourierModel::class]);
        $builder->leftJoin(MessageContentModel::class, 'mr.message_content_id = mt.id', 'mt');
        $builder->andWhere("mr.is_del = 0");
        foreach ($params as $field => $val) {
            if (!is_array($val)) {
                $builder->andWhere($field." = :{$field}:", [$field => $val]);
            }
            if (is_array($val)) {
                $builder->andWhere($field." IN ({{$field}:array})", [$field => $val]);
            }
        }
        return $builder->getQuery()->execute()->toArray();
    }




}