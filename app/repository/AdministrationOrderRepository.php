<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\backyard\AdministrationOrderModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;

class AdministrationOrderRepository extends BaseRepository
{
    private $db = null;

    public function __construct()
    {
        parent::__construct();
        $this->db = $this->getDI()->get("db");
    }

    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 向 administration_order 表插入数据
     * @param $paramIn
     * @return int
     */
    public function add($paramIn)
    {
        $flag      = $this->db->insertAsDict((new AdministrationOrderModel)->getSource(), $paramIn);
        $ticket_id = 0;
        if ($flag) {
            $ticket_id = $this->db->lastInsertId();
        }
        return $ticket_id;
    }

    /**
     * 参数查询
     * @param $params
     * @param $colum
     * @return array
     */
    public function getInfoByParams($params, $colum): array
    {
        if (empty($params)) {
            return [];
        }
        $fields  = (!is_array($colum) || empty($colum)) ? "*" : implode(',', $colum);
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($fields);
        $builder->from(['m' => AdministrationOrderModel::class]);
        foreach ($params as $field => $val) {
            if (!is_array($val)) {
                $builder->andWhere($field." = :{$field}:", [$field => $val]);
            }
            if (is_array($val)) {
                $builder->andWhere($field." IN ({{$field}:array})", [$field => $val]);
            }
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 翻页查询
     * @param $params
     * @param array $colum
     * @param array $order
     * @param int $size
     * @param int $offset
     * @return array
     */
    public function list($params, array $colum = [], array $order = [], int $size = 20, int $offset = 1): array
    {
        if (empty($params)) {
            return [];
        }
        $fields  = (!is_array($colum) || empty($colum)) ? "*" : implode(',', $colum);
        $builder = $this->modelsManager->createBuilder();
        //$builder->columns($fields);
        $builder->from(['m' => AdministrationOrderModel::class]);
        foreach ($params as $field => $val) {
            if (!is_array($val)) {
                $builder->andWhere($field." = :{$field}:", [$field => $val]);
            }
            if (is_array($val)) {
                $builder->andWhere($field." IN ({{$field}:array})", [$field => $val]);
            }
        }

        $builder->columns("count(1) as total");
        $totalInfo = $builder->getQuery()->getSingleResult();
        $total     = intval($totalInfo->total);

        $builder->columns($fields);
        $builder->orderBy($order['field'].' '.$order['sort']);
        $builder->limit($size, $offset);

        $list = $builder->getQuery()->execute()->toArray();
        return ['total' => $total, 'list' => $list];
    }

    /**
     * 更新数据
     * @param array $updateData
     * @param array $whereData
     * @return false
     */
    public function update(array $updateData, array $whereData): bool
    {
        if (empty($updateData) || empty($whereData)) {
            return false;
        }

        return $this->getDI()->get('db')->updateAsDict((new AdministrationOrderModel())->getSource(), $updateData,
            $whereData);
    }

    /**
     * 根据日期获取条数
     * @param $day
     * @return int
     */
    public function getNumByDay($day)
    {
        $sql                = "select count(*) as num from administration_order where created_at >= :begin_time and created_at <= :end_time ";
        $bind['begin_time'] = $day." 00:00:00";
        $bind['end_time']   = $day." 23:59:59";
        $arr                = $this->db->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC, $bind);
        return intval($arr['num']);
    }


    /**
     * 查询工单数量
     * @param $params
     * @param $colum
     * @return int
     */
    public function getNumUseStaffInfo($params): int
    {
        $builder = $this->modelsManager->createBuilder();
        $builder = $this->bindParams($params, $builder);
        $builder->columns('count(1) as total');
        $totalInfo = $builder->getQuery()->getSingleResult();
        return intval($totalInfo->total);
    }


    /**
     * 查询工单列表
     * @param $params
     * @param $colum
     * @return array
     */
    public function getListUseStaffInfo(
        $params,
        array $colum = [],
        array $order = [],
        int $size = 20,
        int $offset = 1
    ): array {
        $fields  = (!is_array($colum) || empty($colum)) ? "*" : implode(',', $colum);
        $builder = $this->modelsManager->createBuilder();
        $builder = $this->bindParams($params, $builder);
        $builder->columns("count(1) as total");
        $totalInfo = $builder->getQuery()->getSingleResult();
        $total     = intval($totalInfo->total);
        $builder->columns($fields);
        $builder->orderBy($order['field'].' '.$order['sort']);
        $builder->limit($size, $offset);
        $list = $builder->getQuery()->execute()->toArray();
        return ['total' => $total, 'list' => $list];
    }

    /**
     * 绑定查询参数
     * @param $params
     * @param $builder
     * @return mixed
     */
    private function bindParams($params, &$builder)
    {
        if (!empty($params['multi_permission'])) {
            $condition = [];
            $bind      = [];
            $multi_permission= $params['multi_permission'];
            if (!empty($multi_permission['stores'])) {
                $condition[]          = "staff.sys_store_id in ({stores:array}) ";
                $bind['stores'] = $multi_permission['stores'];
            }

            if (!empty($multi_permission['manage_departments'])) {
                $condition[]                = " staff.node_department_id in ({manage_departments:array}) and staff.sys_store_id= '-1' ";
                $bind['manage_departments'] = $multi_permission['manage_departments'];
            }


            if (!empty($multi_permission['departments'])) {
                $condition[]                = "staff.node_department_id in ({departments:array}) ";
                $bind['departments'] = $multi_permission['departments'];
            }

            if (!empty($multi_permission['staffs'])) {
                $condition[]           = "staff.staff_info_id in ({staffs:array}) ";
                $bind['staffs'] = $multi_permission['staffs'];
            }
            if ($condition && $bind) {
                $builder->andWhere(implode(" OR ", $condition), $bind);
            }
        }
        //问题所属网点
        if (!empty($params['source_store_id'])) {
            $builder->andWhere("m.store_id = :source_store_id: ",
                ['source_store_id' => $params['source_store_id']]);
        }

        //状态
        if (!empty($params['status'])) {
            $builder->andWhere("m.status = :status: ", ['status' => $params['status']]);
        }

        //问题类型
        if (!empty($params['question_type_id'])) {
            $builder->andWhere("m.question_type_id = :question_type_id: ",
                ['question_type_id' => $params['question_type_id']]);
        }

        //提交人
        if (!empty($params['created_staff_id'])) {
            $builder->andWhere("m.created_staff_id = :created_staff_id: ",
                ['created_staff_id' => $params['created_staff_id']]);
        }
        $builder->from(['m' => AdministrationOrderModel::class]);
        $builder->innerjoin(HrStaffInfoModel::class, 'm.created_staff_id = staff.staff_info_id', 'staff');
        return $builder;
    }



}