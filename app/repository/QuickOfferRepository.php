<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrEconomyAbilityModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewModel;
use FlashExpress\bi\App\Models\backyard\HrJdModel;
use FlashExpress\bi\App\Models\backyard\HrLogModel;
use FlashExpress\bi\App\Models\backyard\HrResumeExtentModel;
use FlashExpress\bi\App\Models\backyard\QuickOfferModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysProvinceModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\VehicleInfoModel;
use FlashExpress\bi\App\Models\backyard\SysCityModel;
use FlashExpress\bi\App\Server\QuickOfferServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\SysServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use Phalcon\DiInterface;
use Phalcon\Paginator\Adapter\Model;

class QuickOfferRepository extends BaseRepository
{

    public function initialize()
    {
        parent::initialize();
    }


    public function __construct($lang = 'zh-CN')
    {
        parent::__construct($lang);
    }

    public function getState(): array
    {
        return [
            HrInterviewModel::NO_COMMUNICATION          => $this->getTranslation()->_('quick_offer_state_' . HrInterviewModel::NO_COMMUNICATION),
            HrInterviewModel::WAITING_INTERVIEW         => $this->getTranslation()->_('quick_offer_state_' . HrInterviewModel::WAITING_INTERVIEW),
            HrInterviewModel::INTERVIEW_IN_THE          => $this->getTranslation()->_('quick_offer_state_' . HrInterviewModel::INTERVIEW_IN_THE),
            HrInterviewModel::INTERVIEW_ON_BEHALF_OFFER => $this->getTranslation()->_('quick_offer_state_' . HrInterviewModel::INTERVIEW_ON_BEHALF_OFFER),
            HrInterviewModel::INTERVIEW_LSSUED_OFFER    => $this->getTranslation()->_('quick_offer_state_' . HrInterviewModel::INTERVIEW_LSSUED_OFFER),
            HrInterviewModel::INTERVIEW_REJECTED        => $this->getTranslation()->_('quick_offer_state_' . HrInterviewModel::INTERVIEW_REJECTED),
            HrInterviewModel::INTERVIEW_CANCELLED       => $this->getTranslation()->_('quick_offer_state_' . HrInterviewModel::INTERVIEW_CANCELLED),
            HrInterviewModel::INTERVIEW_DELETED         => $this->getTranslation()->_('quick_offer_state_' . HrInterviewModel::INTERVIEW_DELETED),
            HrInterviewModel::INTERVIEW_EMPLOYEED       => $this->getTranslation()->_('quick_offer_state_' . HrInterviewModel::INTERVIEW_EMPLOYEED),
        ];
    }


    /**
     * 获取简历已提交 列表
     * @param $staff_id
     * @param $pageNum
     * @param $pageSize
     * @return array
     */
    public function getSubmittedList($staff_id, $pageNum, $pageSize): array
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as total');
        $builder->from(['qo' => QuickOfferModel::class])
            ->innerjoin(HrResumeModel::class, "qo.resume_id = resume.id", "resume")
            ->innerjoin(HrInterviewModel::class, "qo.interview_id = interview.interview_id", "interview")
            ->where('qo.submitter_staff_id = :submitter_staff_id: and qo.deleted = :deleted:',
                ['submitter_staff_id' => $staff_id, 'deleted' => 0]);
        $builder->andWhere('qo.state = :state:', ['state' => QuickOfferModel::STATE_SUBMITTED]);

        //获取总数
        $totalInfo           = $builder->getQuery()->getSingleResult();
        $count               = intval($totalInfo->total);
        $returnData['total'] = $count;
        $builder->columns(
            "qo.id as quick_offer_id,
	                qo.resume_id ,
	                qo.interview_id ,
	                qo.state,
	                DATE_FORMAT( CONVERT_TZ( qo.created_at, '+00:00', '" . $this->timezone . "' ), '%Y-%m-%d' ) AS created_date,
	                DATE_FORMAT( CONVERT_TZ( qo.updated_at, '+00:00', '" . $this->timezone . "' ), '%Y-%m-%d' ) AS updated_date,
	                DATE_FORMAT( CONVERT_TZ( qo.submit_at, '+00:00', '" . $this->timezone . "' ), '%Y-%m-%d' ) AS submit_date,
	                qo.phone,
	                qo.last_name,
	                qo.first_name,
	                interview.state,
	                resume.is_out
	                "
        );
        $builder->orderBy('qo.submit_at desc');
        $builder->limit($pageSize, ($pageNum - 1) * $pageSize);
        $data     = $builder->getQuery()->execute()->toArray();
        $stateArr = $this->getState();
        foreach ($data as &$v) {
            //处理by推荐简历列表展示状态
            $state           = $v['state'];
            $v['state_text'] = $stateArr[$state] ?? '';
            //简历已淘汰，状态改成 已拒绝
            if ($v['is_out'] == HrResumeModel::RESUME_IS_OUT_YES) {
                $v['state_text'] = $stateArr[HrInterviewModel::INTERVIEW_REJECTED];
            }
            if (isCountry("PH") && !empty($v['phone'])) {
                $v['phone'] = str_pad($v['phone'], 11, "0", STR_PAD_LEFT);
            }
        }

        $returnData['list'] = $data;
        return $returnData;
    }

    /**
     * 获取待提交列表
     * @param $staff_id
     * @param $pageNum
     * @param $pageSize
     * @return array
     */
    public function getWaitSubmitList($staff_id, $pageNum, $pageSize): array
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as total');
        $builder->from(['qo' => QuickOfferModel::class])
            ->where('qo.submitter_staff_id = :submitter_staff_id: and qo.deleted = :deleted:',
                ['submitter_staff_id' => $staff_id, 'deleted' => 0]);
        $builder->inWhere('qo.state', [QuickOfferModel::STATE_EDITING, QuickOfferModel::STATE_WAIT_SUBMIT]);
        $builder->orderBy('qo.submit_at desc');

        //获取总数
        $totalInfo           = $builder->getQuery()->getSingleResult();
        $count               = intval($totalInfo->total);
        $returnData['total'] = $count;
        $builder->columns(
            "qo.id as quick_offer_id,
	                qo.resume_id,
	                qo.state,
	                DATE_FORMAT( CONVERT_TZ( qo.created_at, '+00:00', '" . $this->timezone . "' ), '%Y-%m-%d' ) AS created_date,
	                DATE_FORMAT( CONVERT_TZ( qo.updated_at, '+00:00', '" . $this->timezone . "' ), '%Y-%m-%d' ) AS updated_date,
	                DATE_FORMAT( CONVERT_TZ( qo.submit_at, '+00:00', '" . $this->timezone . "' ), '%Y-%m-%d' ) AS submit_date,
	                qo.phone,
	                qo.last_name,
	                qo.first_name"
        );
        $builder->limit($pageSize, ($pageNum - 1) * $pageSize);
        $builder->orderBy('qo.submit_at desc');
        $data = $builder->getQuery()->execute()->toArray();
        $t    = $this->getTranslation();
        foreach ($data as &$v) {
            $v['state_text'] = $t->_('qo_state_' . $v['state']);
            if (!empty($v['phone']) && isCountry("PH")) {
                $v['phone'] = str_pad($v['phone'], 11, "0", STR_PAD_LEFT);
            }
        }
        $returnData['list'] = $data;
        return $returnData;
    }

    /**
     * 其他网点列表
     * @param $params
     * @return array
     */
    public function getHasHcStoreList($params): array
    {
        $page_size = !empty($params['page_size']) ? $params['page_size'] : 10;
        $page_num  = !empty($params['page_num']) ? $params['page_num'] : 1;;
        $result = [
            'list' => [],
        ];
        // 模糊查询出符合条件的网点
        // 第一步关联hc的目的时 滤掉网点下没有hc的数据，减少后续的循环。
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as total');
        $builder->from(['store' => SysStoreModel::class]);
        $builder->leftjoin(HrHcModel::class, 'store.id = hc.worknode_id', 'hc');
        $builder->leftjoin(HrJdModel::class, 'hc.job_id = jd.job_id', 'jd');
        $builder->leftjoin(SysDepartmentModel::class, 'hc.department_id = department.id', 'department');
        $builder->leftjoin(SysCityModel::class, 'hc.city_code = city.code', 'city');
        $builder->andWhere('hc.state_code = :state_code:', ['state_code' => HrHcModel::STATE_RECRUITING]);
        $builder->inWhere('hc.reason_type', [HrHcModel::REASON_TYPE_RECRUIT, HrHcModel::REASON_TYPE_QUIT]);

        if (isset($params['type'])) {
            //个人代理
            if ($params['type'] == QuickOfferServer::TYPE_IC) {
                $builder->andWhere('hc.hire_type = :hire_type:',
                    ['hire_type' => HrHcModel::HIRE_TYPE_CONTRACT_LABOUR]);
            }
            //普通员工
            if ($params['type'] == QuickOfferServer::TYPE_RE) {
                //【quick offer 雇佣类型联动修改点(1,2)】 - 2
                if (isCountry(['MY', 'PH'])) {
                    $builder->andWhere('hc.hire_type not in ({hire_type:array})',
                        [
                            'hire_type' => [
                                HrHcModel::HIRE_TYPE_CONTRACT_LABOUR,
                                HrHcModel::HIRE_TYPE_SPECIAL_CONTRACT_MONTHLY_WORKERS,
                            ],
                        ]);
                } else {
                    $builder->andWhere('hc.hire_type != :hire_type:',
                        ['hire_type' => HrHcModel::HIRE_TYPE_CONTRACT_LABOUR]);
                }
            }
            //月薪制
            if ($params['type'] == QuickOfferServer::TYPE_MONTH) {
                $builder->andWhere('hc.hire_type = :hire_type:',
                    ['hire_type' => HrHcModel::HIRE_TYPE_SPECIAL_CONTRACT_MONTHLY_WORKERS]);
            }
        }

        if (!empty($params['search_store'])) {
            $builder->andWhere('store.name like :store_name:', ['store_name' => '%' . $params['search_store'] . '%']);
        }

        if (!empty($params['job_id'])) {
            $builder->andWhere('hc.job_id = :hc_job_id:', ['hc_job_id' => $params['job_id']]);
        }
        $totalInfo = $builder->getQuery()->getSingleResult();
        $this->logger->write_log(["getHasHcStoreList-sql" => $builder->getQuery()->getSql()], 'info');

        $count = $result['total'] = intval($totalInfo->total);
        if (empty($count)) {
            return $result;
        }
        $builder->columns('store.id as store_id,department.name as department_name,jd.job_name,hc.hc_id,hc.job_id,store.name as store_name,city.name as city_name,hc.city_code');
        $builder->orderBy('hc.priority_id ASC, hc.surplusnumber DESC, hc.approval_completion_time ASC');
        $builder->limit($page_size, ($page_num - 1) * $page_size);
        $data = $builder->getQuery()->execute()->toArray();


        $result['list'] = $data;
        return $result;
    }

    /**
     * 获取日志信息
     * @param $conditions
     * @param $binds
     * @return mixed
     */
    public function getLogOneInfo($conditions, $binds)
    {
        $result = HrLogModel::findFirst([
            'columns'    => 'id, staff_info_id',
            'conditions' => $conditions,
            'bind'       => $binds,
        ]);

        return !empty($result) ? $result->toArray() : [];
    }

}
