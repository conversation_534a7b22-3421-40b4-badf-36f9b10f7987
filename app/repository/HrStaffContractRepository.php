<?php


namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Enums\HrStaffIdentityAnnexEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrStaffAnnexInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffContractModel;

class HrStaffContractRepository extends BaseRepository
{

    public function initialize()
    {
        parent::initialize();
    }
    //已通过
    const APPROVE_STATE_PASS=4;
    /**
     * 获取身份证\签名信息
     * 9802【FBI】资产相关清单优化
     * 原：从WHR简历中身份证正面照字段取值
     * 更改为：从Flash员工管理，身份证正面字段取该处审核状态为已通过的身份证照片
     * @param array $paramIn 参数组
     * @return array
     */
    public function winhrApprove($paramIn = []){
        $staffId = $paramIn["staff_id"] ?? 0;
        $res = [
            "id_card_pic"=>"",
            "sign_name_pic"=>"",
            "state"=>self::APPROVE_STATE_PASS,
        ];

        //从员工电子合同表中获取签名照照片
        $sql="select * from hr_staff_contract where staff_id = {$staffId}  and contract_is_deleted=0  and contract_status not in(10,20,30,100,110) and contract_signature_img != '' and contract_child_type in(51,52) order by id desc limit 1";
        $approve  = $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        if(!empty($approve)){
            $res['sign_name_pic'] = $approve['contract_signature_img'];
        } else {
            return array();
        }

        //读取员工身份证附件表，获取员工身份证正面字段以及审核状态为已通过的身份证照片
//        $staff_identity_info = HrStaffIdentityAnnexModel::findFirst([
//            'conditions' => 'staff_info_id = :staff_info_id: and audit_state = :audit_state:',
//            'bind' => ['staff_info_id' => $staffId, 'audit_state'=>HrStaffIdentityAnnexEnums::AUDIT_STATE_PASSED]
//        ]);
        $staff_identity_info = HrStaffAnnexInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and audit_state = :audit_state: and type = :type:',
            'bind' => ['staff_info_id' => $staffId, 'audit_state'=>HrStaffAnnexInfoModel::AUDIT_STATE_PASSED, 'type'=>HrStaffAnnexInfoModel::TYPE_ID_CARD]
        ]);
        $staff_identity_info_arr = $staff_identity_info ? $staff_identity_info->toArray() : [];
        if (!empty($staff_identity_info_arr)) {
            $res['id_card_pic'] = $staff_identity_info_arr['annex_path_front'] ?? '';
        }

        //当身份证照片审核状态为已通过&&签名照片都存在时，审核状态默认为已通过
        if($res['sign_name_pic'] && $res['id_card_pic']) {
            $t = $this->getTranslation();
            $data =[
                "id_card_pic"=>$res['id_card_pic'],
                "sign_name_pic"=>$res['sign_name_pic'],
                "state"=>self::APPROVE_STATE_PASS,
                "state_name" => $t['approve_state_'.self::APPROVE_STATE_PASS],
                "reason"=>""
            ];
        } else {
            $data=array();
        }
        return $data;
    }

    private function getPic($bucket_name,$object_key){
        $img_prefix = env("img_prefix","http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/");

        return $img_prefix.$object_key;
        //return "http://".$bucket_name.'.oss-ap-southeast-1.aliyuncs.com/'.$object_key;
    }

    public static function getOne($params, $columns = ['*'])
    {
        if(empty($params)) {
            return [];
        }
        $conditions = 'contract_is_deleted = :contract_is_deleted: and contract_is_need = :contract_is_need:';
        $bind['contract_is_deleted'] = HrStaffContractModel::CONTRACT_DELETED_NO;
        $bind['contract_is_need'] = HrStaffContractModel::CONTRACT_NEED_YES;

        if(!empty($params['id'])) {
            $conditions .= ' and id = :id:';
            $bind['id'] = $params['id'];
        }

        if(!empty($params['staff_id'])) {
            $conditions .= ' and staff_id = :staff_id:';
            $bind['staff_id'] = $params['staff_id'];
        }

        //合同状态
        if(!empty($params['contract_status'])) {
            $conditions .= ' and contract_status = :contract_status:';
            $bind['contract_status'] = $params['contract_status'];
        }

        $data = HrStaffContractModel::findFirst([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);

        return !empty($data) ? $data->toArray() : [];
    }
}


