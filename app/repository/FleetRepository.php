<?php

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\FleetAuditModel;

class FleetRepository extends BaseRepository
{
    public $timezone;

    public function __construct($timezone)
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    /**
     * 创建加班车申请
     * @param array $insert
     * @return int
     */
    public function InsertFleet($insert)
    {
        try {
            $insertSql = $this->getInsertDbSql('fleet_audit', $insert);
            $this->getDI()->get('db')->query($insertSql);
            $last_id = $this->getDI()->get('db')->lastInsertId();
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("FleetRe:InsertFleet-" . $e->getMessage());
            $last_id = '';
        }
        return $last_id;
    }

    /**
     * 获取临时加班车详情
     */
    public function getFleetInfo($fleet_id, $isShowImg = false)
    {
        if (empty($fleet_id)) {
            return [];
        }

        $query_sql = "
            --
            select 
                id
                ,car_type
                ,serial_no
                ,capacity
                ,DATE_FORMAT(expected_date, '%Y-%m-%d %H:%i:%s') as expected_time
                ,DATE_FORMAT(expected_date, '%Y-%m-%d %H:%i') as expected_date
                ,start_store
                ,end_store
                ,region
                ,reason
                ,reason_type
                ,reject_reason
                ,reject_type
                ,line_id
                ,line_back_id
                ,single_line
                ,system_quote
                ,abnormal_cost
                ,final_cost
                ,submitter_id
                ,status
                ,DATE_FORMAT(CONVERT_TZ(created_at, '+00:00', '".$this->timezone."' ),'%Y-%m-%d %H:%i:%s') as created_at 
                ,DATE_FORMAT(CONVERT_TZ(updated_at, '+00:00', '".$this->timezone."' ),'%Y-%m-%d %H:%i:%s') as updated_at 
                ,final_approver
                ,CONVERT_TZ(final_approval_time, '+00:00', '".$this->timezone."' ) AS final_approval_time
                ,audit_type
                ,fd_courier_id
                ,submitter_id
                ,wf_role
                ,via_store_ids
                ,via_store_short_name
                ,plan_date
                ,plan_back_date
                ,set_flag
                ,select_btn_num
            from fleet_audit where id = :id";
        $staff_info = $this->getDI()->get('db')->query($query_sql,['id'=>$fleet_id])->fetch(\Phalcon\Db::FETCH_ASSOC);

        if ($isShowImg) {
            $query_img_sql = "
                --
                select 
                    bucket_name
                    ,object_key
                from sys_attachment 
                where oss_bucket_key = :id 
                    and oss_bucket_type = 'FLEET_AUDIT' 
                    and deleted = 0";
            $images = $this->getDI()->get('db')->query($query_img_sql,['id'=>$fleet_id])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if (!empty($images)) {
                $staff_info['image_path'] = [];
                foreach ($images as $image) {
                    $staff_info['image_path'][] = convertImgUrl($image['bucket_name'], $image['object_key']);
                }
            }
        }

        return $staff_info;
    }

    /**
     * 更新fleet主状态
     * @param int $fleet_id     主键id
     * @param int $status       审批状态
     * @param string $reject_reason 驳回原因
     * @return boolean
     */
    public function updateFleet($fleet_id, $status, $reject_reason = '')
    {
        $update_sql = "update fleet_audit set status = {$status}, reject_reason = '{$reject_reason}' where id = {$fleet_id}";
        $db = $this->getDI()->get('db');
        try{

            $db->begin();
            $db->execute($update_sql);

            $db->commit();
            return true;
        }catch (\Exception $e){
            $db->rollback();
            $this->getDI()->get('logger')->write_log("FleetRepository:updateFleet:" . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取提交人ID
     */
    public function getSimilarRequest($paramIn = [])
    {
        $startStore = $paramIn['start_store'] ?? '';
        $endStore   = $paramIn['end_store'] ?? '';
        $arriveTime = $paramIn['arrive_time'] ?? '';

        if (empty($startStore) || empty($endStore) || empty($arriveTime)) {
            return [];
        }

        //相同始发网点、目的网点、期望到达日期的提交人ID
        $querySql = "--
            SELECT
                DISTINCT(submitter_id)
            FROM `fleet_audit` 
            WHERE
                start_store = ? AND
                end_store = ? AND
                expected_date BETWEEN ? AND ?
        ";

        $data = $this->getDI()->get('db')->query($querySql, [
            $startStore,
            $endStore,
            date("Y-m-d 00:00:00", strtotime($arriveTime)),
            date("Y-m-d 23:59:59", strtotime($arriveTime))
        ])->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        if (!empty($data)) {
            $data = array_column($data, 'submitter_id');
        }

        return $data;
    }

    /**
     * 更新fleet更新插入时间
     * @param int $id       主键ID
     * @return mixed
     */
    public function updateFleetInTime($paramIn =[])
    {
        $this->getDI()->get('logger')->write_log($paramIn,'info');
        $fleetId = $paramIn['fleetId'] ?? '';
        if (empty($fleetId)) {
            return false;
        }
        $db = $this->getDI()->get('db');
        try{
            $db->updateAsDict(
                'fleet_audit',
                [
                    'approve_time' => gmdate('Y-m-d H:i:s')
                ],
                [
                    'conditions' => 'id = ?',
                    'bind'       => [$fleetId],
                ]
            );
            $this->getDI()->get('logger')->write_log($db->affectedRows(),'info');
            return true;
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log($e->getTraceAsString() . $e->getMessage());
            return false;
        }
    }

    /**
     * 查询加班车数据
     * @param $params
     * @param array $columns
     * @return array
     */
    public static function getList($params, $columns = ['*'])
    {
        if(empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind       = [];

        if (!empty($params['id'])) {
            $conditions .= ' and id = :id:';
            $bind['id'] = $params['id'];
        }

        if (!empty($params['start_store']) && !empty($params['end_store'])) {
            $conditions .= ' and start_store = :start_store: and end_store = :end_store:';
            $bind['start_store'] = $params['start_store'];
            $bind['end_store'] = $params['end_store'];

        }

        if (!empty($params['start_expected_date']) && !empty($params['end_expected_date'])) {
            $conditions .= ' and expected_date >= :start_expected_date: and expected_date <= :end_expected_date:';
            $bind['start_expected_date'] = $params['start_expected_date'];
            $bind['end_expected_date'] = $params['end_expected_date'];
        }

        if (!empty($params['status'])) {
            $conditions .= ' and status = :status:';
            $bind['status'] = $params['status'];
        }

        if ($conditions == '1 = 1') {
            return [];
        }

        return FleetAuditModel::find([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();
    }
}
