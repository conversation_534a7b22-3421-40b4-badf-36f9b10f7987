<?php


namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\library\DateHelper;

class DiscountRepository extends BaseRepository
{
    public $timezone;

    public function __construct($timezone = '+07:00')
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    /**
     * 获取申请运费折扣详情
     * @param $audit_id
     * @return array
     */
    public function getDetail($audit_id)
    {
        if (empty($audit_id)) {
            return [];
        }

        $query_sql = "
            --
            select 
                id
                ,serial_no
                ,submitter_id
                ,costomer_id
                ,costomer_name
                ,costomer_mobile
                ,costomer_type
                ,costomer_created_at
                ,costomer_parcel_count
                ,costomer_estimate_parcel_count
                ,price_rule_category
                ,price_type
                ,current_disc
                ,request_disc
                ,DATE_FORMAT(disc_start_date, '%Y-%m-%d %H:%i:%s') as disc_start_date
                ,DATE_FORMAT(disc_end_date, '%Y-%m-%d %H:%i:%s') as disc_end_date
                ,proportion
                ,valid_days
                ,remark
                ,apply_reason_type
                ,state
                ,reject_reason
                ,sync_state
                ,is_valid
                ,DATE_FORMAT(CONVERT_TZ(created_at, '+00:00', '{$this->timezone}' ),'%Y-%m-%d %H:%i:%s') as created_at 
                ,DATE_FORMAT(CONVERT_TZ(updated_at, '+00:00', '{$this->timezone}' ),'%Y-%m-%d %H:%i:%s') as updated_at 
                ,wf_role
                ,channel
            from freight_discount where id = {$audit_id}";
        $detailInfo = $this->getDI()->get('db')->query($query_sql)->fetch(\Phalcon\Db::FETCH_ASSOC);


        $sql = "--
            SELECT
                 coupon_type
                ,coupon_name
                ,coupon_days_type
                ,coupon_valid_days
                ,coupon_num
                ,coupon_start_date
                ,coupon_end_date 
            FROM freight_discount_coupon
            WHERE pid = {$audit_id}";
        $coupunInfo = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        $detailInfo['coupon'] = $coupunInfo ?? [];

        $query_img_sql = "
                --
                select 
                    bucket_name
                    ,object_key
                from sys_attachment 
                where oss_bucket_key = {$audit_id} 
                    and oss_bucket_type = 'FREIGHT_DISCOUNT' 
                    and deleted = 0";
        $images = $this->getDI()->get('db')->query($query_img_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (!empty($images)) {
            $detailInfo['image_path'] = [];
            foreach ($images as $image) {
                array_push($detailInfo['image_path'], convertImgUrl($image['bucket_name'], $image['object_key']));
            }
        }

        return $detailInfo;
    }

    /**
     * 获取申请运费折扣详情
     * @param $customer_id
     * @return array
     */
    public function getCustomerAllRequest($customer_id)
    {
        if (empty($customer_id)) {
            return [];
        }

        $query_sql = "
            --
            select 
                fd.id
                ,serial_no
                ,submitter_id
                ,costomer_id
                ,costomer_name
                ,costomer_mobile
                ,costomer_type
                ,costomer_created_at
                ,costomer_parcel_count
                ,costomer_estimate_parcel_count
                ,price_rule_category
                ,price_type
                ,current_disc
                ,request_disc
                ,DATE_FORMAT(disc_start_date, '%Y-%m-%d %H:%i:%s') as disc_start_date
                ,DATE_FORMAT(disc_end_date, '%Y-%m-%d %H:%i:%s') as disc_end_date
                ,valid_days
                ,remark
                ,apply_reason_type
                ,state
                ,reject_reason
                ,sync_state
                ,is_valid
                ,DATE_FORMAT(CONVERT_TZ(fd.created_at, '+00:00', '{$this->timezone}' ),'%Y-%m-%d %H:%i:%s') as created_at 
                ,DATE_FORMAT(CONVERT_TZ(fd.updated_at, '+00:00', '{$this->timezone}' ),'%Y-%m-%d %H:%i:%s') as updated_at 
                ,wf_role
                ,coupon_type
                ,coupon_name
                ,coupon_days_type
                ,coupon_valid_days
                ,coupon_num
                ,coupon_start_date
                ,coupon_end_date
                ,request_cod_poundage_rate_str
                ,request_return_discount_rate
                ,request_credit_term
            from freight_discount fd
            left join freight_discount_coupon fdc on fd.id = fdc.pid
            where costomer_id = '{$customer_id}' and fd.state IN (1,2) and is_valid = 1
            ";
        $detailInfo = $this->getDI()->get('db')->query($query_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $detailInfo;
    }

    /**
     * 计算占比
     * @param $customer_id
     * @return array|string
     */
    public function calcProportion($customer_id)
    {
        if (empty($customer_id)) {
            return "";
        }

        $startDate = date("Y-m-d", strtotime("-30 days"));
        $endDate   = date("Y-m-d", strtotime("-1 days"));

        $querySql = "--
            select 
                count(1) as total, 
                sum(if(express_category = 2,1,0)) as express_category_count,  
                sum(if(insured =1,1,0)) as insured_count,
                sum(if(freight_insure_enabled =1,1,0)) as freight_insured_count
            from parcel_info
            where client_id  = '{$customer_id}' and state != 9
            and created_at >= CONVERT_TZ('{$startDate} 00:00:00' , '{$this->timezone}', '+00:00')
            and created_at <= CONVERT_TZ('{$endDate} 23:59:59', '{$this->timezone}', '+00:00')
        ";
        //$query_db = env('runtime') == 'pro' ? 'db_adb' : 'db_fle';
        $detailInfo = $this->getDI()->get('db_fle')->query($querySql)->fetch(\Phalcon\Db::FETCH_ASSOC);

        $total = $detailInfo['total'] ?? 0;
        $expressCount = $detailInfo['express_category_count'] ?? 0;
        $insuredCount = $detailInfo['insured_count'] ?? 0;
        $freightCount = $detailInfo['freight_insured_count'] ?? 0;

        return [
            'express'   => $total == 0 ? 0: number_format($expressCount/$total, 2),
            'insured'   => $total == 0 ? 0: number_format($insuredCount/$total, 2),
            'freight'   => $total == 0 ? 0: number_format($freightCount/$total, 2),
        ];
    }

    /**
     * 获取同步失败的申请列表
     */
    public function getSyncFailureDiscList()
    {
        $sql = "--
            SELECT 
                id
                ,serial_no
                ,submitter_id
                ,costomer_id
                ,costomer_name
                ,costomer_mobile
                ,costomer_type
                ,price_type
                ,current_disc
                ,request_disc
                ,disc_start_date
                ,disc_end_date
                ,valid_days
                ,remark
                ,state
                ,sync_state
                ,is_valid
                ,DATE_FORMAT(CONVERT_TZ(created_at, '+00:00', '{$this->timezone}' ),'%Y-%m-%d %H:%i:%s') as created_at 
                ,DATE_FORMAT(CONVERT_TZ(updated_at, '+00:00', '{$this->timezone}' ),'%Y-%m-%d %H:%i:%s') as updated_at 
                ,wf_role
            FROM freight_discount WHERE state = 2 AND sync_state = 1
            ORDER BY id desc
            ";
        $detailInfo = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        return $detailInfo;
    }

    /**
     * 获取上个月(昨天之前30天)每日发件量
     * @param $client_id
     * @return int
     */
    public function getPerDayParcelCount($client_id) : int
    {
        $clientId = $client_id ?? '';
        $dateRangeArr= DateHelper::DateRange(strtotime("-30 day"), strtotime("-1 day"));
        $dateRange   = getIdsStr($dateRangeArr);

        $querySql = "--
            SELECT
                sum(parcel_cnt) as total
            FROM client_parcel_weight_stat_day_v2
            WHERE client_id = '{$clientId}' AND stat_date IN ( {$dateRange} )
        ";
        $db_bi_or_polar = env('runtime') == 'pro' ? 'db_rnl' : 'db_rbi';
        $lastMonthParcelTotal = $this->getDI()->get($db_bi_or_polar)->fetchColumn($querySql);
        return isset($lastMonthParcelTotal) && $lastMonthParcelTotal ? intval(($lastMonthParcelTotal ?? 0) / 30) : 0;
    }

    /**
     * 获取即将过期的折扣数据
     */
    public function getDiscountWillExpire($type)
    {
        $typeIds   = getIdsWithoutQuotes($type);
        $querySql = "--
            SELECT d.* 
            FROM client_discount_info_v2 d 
            LEFT JOIN (
                SELECT client_id,`price_rule_category`  
                FROM `client_discount_info_v2` 
                WHERE disc_start_date >= concat (DATE (now()), ' 17:00:00') AND state = 1 ) d2 
            ON d.`client_id` = d2.client_id AND d.`price_rule_category` = d2.price_rule_category 
            WHERE d.price_rule_category IN ( {$typeIds} ) and d.state = 1
            AND d.disc_end_date >= concat (DATE (now()), ' 17:00:00')
            AND d. disc_end_date <= concat(date_add(DATE(now()),interval 6 day) , ' 18:00:00')
            AND d2.client_id IS NULL 
            GROUP BY d.`id`,d.disc_start_date,d.`disc_end_date`  
            ORDER BY d.`disc_end_date` DESC
        ";
        $list = $this->getDI()->get('db_fle')->query($querySql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $list;
    }
}