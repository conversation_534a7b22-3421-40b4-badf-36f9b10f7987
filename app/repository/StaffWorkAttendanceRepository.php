<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceAttachmentModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Models\StaffWorkAttendance;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;


class StaffWorkAttendanceRepository extends BaseRepository
{

    public function initialize()
    {

        parent::initialize();
    }

    /**
     * 查询时间内打卡记录
     * @Access  public
     * @Param   $paramIn
     * @return mixed
     */
    public function getPunchCardData($paramIn)
    {
        $staffId      = $paramIn['staff_id'];
        $day          = $paramIn['date_at'];
        $punchCardSql = "SELECT * FROM staff_work_attendance 
                           WHERE staff_info_id = :staff_info_id 
                           AND attendance_date = :attendance_date";
        $data         = $this->getDI()->get('db')->query($punchCardSql,
            ['staff_info_id' => $staffId, 'attendance_date' => $day]);
        return $data->fetch(\Phalcon\Db::FETCH_ASSOC);
    }

    //判断 班次是否跨天 整理好对应的 日期和班次 方便 校验
    public function format_shift($shift_info,$date){
        $shift_start = "{$date} {$shift_info['shift_start']}:00";
        //防止 班次 没有前导0
        $shift_start = date('Y-m-d H:i:s',strtotime($shift_start));

        $shift_end = "{$date} {$shift_info['shift_end']}:00";
        //防止 班次 没有前导0
        $shift_end = date('Y-m-d H:i:s',strtotime($shift_end));

        if($shift_start > $shift_end)
            $shift_end = date('Y-m-d H:i:s',strtotime("{$shift_end} +1 day"));

        return array('start' => $shift_start, 'end' => $shift_end);
    }


    //部分职位打卡 获取对应管辖网点id
    public function getManagerStore( $staffId, $columns = 'id store_id,lat,lng' ) {

        //获取管辖范围
        $relations        = (new StaffServer($this->lang, $this->timezone))->getStaffJurisdiction($staffId);
        $store_id         = $relations['stores'] ?? [];           //网点
        $region_id        = $relations['regions'] ?? [];          //大区
        $piece_id         = $relations['pieces'] ?? [];           //片区
        $store_categories = $relations['store_categories'] ?? []; //网点类型


        //啥也没取着 就返回空
        if (empty($store_id) && empty($region_id) && empty($piece_id) && empty($store_categories)) {
            return array();
        }


        //先看 store 里面有没有-2 如果有 表示管辖全部
        if (!empty($store_id) && in_array('-2', $store_id)) {
            $store_infos = SysStoreModel::find([
                                                   'columns'    => $columns,
                                                   'conditions' => 'state = 1 ',
                                               ])->toArray();
        } else {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns($columns);
            $builder->from(['s' => SysStoreModel::class]);

            if (!empty($store_id)) {
                $builder->orWhere('id in ({store_id:array})', ['store_id' => $store_id]);
            }
            if (!empty($region_id)) {
                $builder->orWhere('manage_region in ({manage_region:array})', ['manage_region' => $region_id]);
            }
            if (!empty($piece_id)) {
                $builder->orWhere('manage_piece in ({manage_piece:array})', ['manage_piece' => $piece_id]);
            }
            if (!empty($store_categories)) {
                $builder->orWhere('category in ({store_categories:array})', ['store_categories' => $store_categories]);
            }
            $store_infos = $builder->getQuery()->execute()->toArray();


        }

        return $store_infos;

    }

    /**
     * @description 获取全部大区
     * @return array
     */
    public function getAllRegions(): array
    {
        $region = SysManageRegionModel::find([
            "deleted = 0",
            "columns" => "id"
        ])->toArray();
        return array_column($region, 'id');
    }

    /**
     * 获取员工人脸图片
     * @param $staff_info_id
     * @return array
     */
    public function getStaffFaceImage($staff_info_id)
    {
        if (empty($staff_info_id)) {
            return [];
        }
        $staffAttachment = StaffWorkAttendanceAttachmentModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and deleted = 0',
            'bind' => [
                'staff_info_id' => $staff_info_id
            ],
            'columns' => 'work_attendance_bucket as bucket,work_attendance_path as path',
            'order' => 'created_at desc',
        ]);
        return $staffAttachment ? $staffAttachment->toArray(): [];
    }
}
