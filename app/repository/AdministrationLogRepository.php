<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\backyard\AdministrationLogModel;

class AdministrationLogRepository extends BaseRepository
{
    private $db = null;

    const NOT_DELETED = 0;  // 未删除
    const IS_DELETED = 1;   // 已删除

    const CREATED_TYPE_SUBMIT = 1;    // 员工提交
    const CREATED_TYPE_REPLY = 2;     // 回复

    public function __construct()
    {
        parent::__construct();
        $this->db = $this->getDI()->get("db");
    }

    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 向 administration_log 表插入数据
     * @param array $paramIn
     * @return int
     */
    public function add(array $paramIn): int
    {
        $flag   = $this->db->insertAsDict((new AdministrationLogModel)->getSource(), $paramIn);
        $log_id = 0;
        if ($flag) {
            $log_id = $this->db->lastInsertId();
        }
        return $log_id;
    }

    /**
     * 翻页查询-list
     * @param $params
     * @param array $colum
     * @param array $order
     * @param int $size
     * @param int $offset
     * @return array
     */
    public function list($params, array $colum = [], array $order = [], int $size = 20, int $offset = 1): array
    {
        if (empty($params)) {
            return [];
        }
        $fields  = (!is_array($colum) || empty($colum)) ? "*" : implode(',', $colum);
        $builder = $this->modelsManager->createBuilder();
        //$builder->columns($fields);
        $builder->from(['m' => AdministrationLogModel::class]);
        foreach ($params as $field => $val) {
            if (!is_array($val)) {
                $builder->andWhere($field." = :{$field}:", [$field => $val]);
            }
            if (is_array($val)) {
                $builder->andWhere($field." IN ({{$field}:array})", [$field => $val]);
            }
        }

        $builder->columns("count(1) as total");
        $totalInfo = $builder->getQuery()->getSingleResult();
        $total     = intval($totalInfo->total);

        $builder->columns($fields);
        $builder->orderBy($order['field'].' '.$order['sort']);
        $builder->limit($size, $offset);

        $list = $builder->getQuery()->execute()->toArray();
        return ['total' => $total, 'list' => $list];
    }

    /**
     * 更新数据
     * @param array $updateData
     * @param array $whereData
     * @return false
     */
    public function update(array $updateData, array $whereData): bool
    {
        if (empty($updateData) || empty($whereData)) {
            return false;
        }

        return $this->getDI()->get('db')->updateAsDict((new AdministrationLogModel())->getSource(), $updateData,
            $whereData);
    }
}