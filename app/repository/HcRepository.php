<?php

namespace FlashExpress\bi\App\Repository;


use app\enums\LangEnums;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HRStaffingModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;

class HcRepository extends BaseRepository
{
    public $timezone;

    public function __construct($timezone)
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    /**
     * 新建HC申请
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function insertHc($paramIn = [])
    {
        try {
            $insertSql  = $this->getInsertDbSql('hr_hc', $paramIn);
            $this->getDI()->get('db')->query($insertSql);
            $hcId = $this->getDI()->get('db')->lastInsertId();
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("HcRepository:insertHc-" . $e->getMessage());
            $hcId = '';
        }
        return $hcId;
    }

    /**
     * HC详情
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function infoHc($paramIn = [])
    {
        $hcId          = $paramIn['hc_id'];
        $sql = "select 
            hr_jd.job_name,
            hr_state.state_value,
            hr_hc.*,
            CONVERT_TZ(hr_hc.createtime, '+00:00', '{$this->timezone}' ) AS createtime,
            CONVERT_TZ(hr_hc.updated_at, '+00:00', '{$this->timezone}' ) AS updated_at,
            CONVERT_TZ(NOW(), '+00:00', '{$this->timezone}') as now_date,
            DATE_FORMAT(CONVERT_TZ(hr_hc.expirationdate, '+00:00', '{$this->timezone}' ),'%Y-%m-%d') as expirationdate 
            from hr_hc 
            left join hr_jd on hr_hc.job_id = hr_jd.job_id 
            left join hr_state on hr_state.state_code = hr_hc.state_code 
            where  hc_id = {$hcId}";
        $data        = $this->getDI()->get('db')->query($sql);
        $returnData  = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }
    
    /**
     * 检查hcId是否存在
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function checkHc($paramIn = [])
    {
        $hcId        = $paramIn['hc_id'];
        $submitterId = $paramIn['submitter_id'] ?? '';
        $stateCode   = $paramIn['state_code'] ?? '';
        if (empty($hcId)) {
            return [];
        }
        $sql = "select * from hr_hc where hc_id=" . $hcId;
        if ($submitterId) {
            $sql .= " and submitter_id =" . $submitterId;
        } else if ($stateCode) {
            $sql .= " and state_code=" . $stateCode;
        }
        $data       = $this->getDI()->get('db')->query($sql);
        $returnData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);

        return $returnData;
    }

    /**
     * JD详情
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function infoJD($paramIn = [])
    {
        $jobId = $paramIn['job_id'];
        $sql = "select 
                  *,
                  CONVERT_TZ(created_at,'+00:00', '".$this->timezone."' ) AS created_at, 
                  CONVERT_TZ(updated_at,'+00:00', '".$this->timezone."' ) AS updated_at 
                  from hr_jd 
                  where job_id = {$jobId}";
        $data        = $this->getDI()->get('db')->query($sql);
        $returnData  = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }


    /**
     * 获取申请人所在部门的负责人及对应的boss
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getDepartmentManager($paramIn = [])
    {
        $staffId          = $paramIn['staff_info_id'];
        $sql = "select 
            hsi.staff_info_id,
            sd.id as department_id,
            hsi.job_title,
            sd.company_id,
            sd.level,
            sd.manager_id
            from hr_staff_info hsi
            join sys_department sd on sd.id = hsi.node_department_id 
            where  hsi.staff_info_id = {$staffId}";
        $data        = $this->getDI()->get('db_rby')->query($sql);
        return $data->fetch(\Phalcon\Db::FETCH_ASSOC);
    }

    /**
     * 获取网点负责人id
     * @return array
     */
    public function getSysStoreInfo()
    {
        $sysStoreInfoSql = "--
                        SELECT
                            manager_id
                        FROM
                            sys_store 
                        WHERE
                            state = 1 
                        AND 
                            manager_id IS NOT NULL";
        $data = $this->getDI()->get('db_rby')->query($sysStoreInfoSql);
        $sysStoreInfoData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        $returnData = $sysStoreInfoData;
        return $returnData;
    }

    /**
     * 获取员工基础信息
     * @param string $staffId
     * @return array
     */
    public function getHcStaffInfo($staffId = '')
    {
        $returnData = [];
        if (!empty($staffId)) {

            if (isCountry('PH')) {
                $jobTitleIds = '11,272,269,79,1645,1107,93,932,65';
            } else {
                $jobTitleIds = '11,272,269,79';
            }

            $staffInfoSql = "--
                            SELECT
                                job_title
                            FROM
                                hr_staff_info
                            WHERE
                                staff_info_id = {$staffId} and state = 1 and formal = 1 and is_sub_staff = 0 and job_title in ({$jobTitleIds})";
            $data = $this->getDI()->get('db_rby')->query($staffInfoSql);
            $staffInfoData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
            $returnData = $staffInfoData;
        }
        return $returnData;
    }

    /**
     * 获取转岗人信息
     * @param string $staffId
     * @return array
     */
    public function getTransferInfo($staffId = '')
    {
        $returnData = [];
        if (!empty($staffId)) {
            $staffInfoSql = "--
                            SELECT
                                state,
                                sys_store_id,
                                job_title,
                                sys_department_id,
                                node_department_id
                            FROM
                                hr_staff_info
                            WHERE
                                staff_info_id = {$staffId} and state = 1 and formal in (1,4) and is_sub_staff = 0";
            $data = $this->getDI()->get('db_rby')->query($staffInfoSql);
            $staffInfoData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
            $returnData = $staffInfoData;
        }
        return $returnData;
    }

    /**
     * 获取离职停职待离职员工列表
     * @param array $param
     * @return array
     */
    public function getStaffLeaveList($param = []) {
        try {
            //部门 职位 网点
            if(empty($param)) {
                return [];
            }

            if(in_array($param['job_title_id'], [13, 110, 452])) {
                $param['job_title_id'] = '13, 110, 452';
            }

            $sql = "SELECT `hr_staff_info`.`staff_info_id`,
                   `hr_staff_info`.`name`,
                   `hr_staff_info`.`node_department_id`,
                   `hr_staff_info`.`job_title`,
                   `hr_staff_info`.`sys_store_id`,
                   `hr_staff_info`.`state`,
                   `hr_staff_info`.`wait_leave_state`,
                   `hr_staff_info`.`leave_date`,
                   `hr_staff_info`.`stop_duties_date`,
                   `hr_job_title`.`job_name`
              FROM `hr_staff_info`
              LEFT JOIN `hr_job_title` ON `hr_staff_info`.`job_title`= `hr_job_title`.`id`
             WHERE `formal`= 1
               and `is_sub_staff`= 0
               and `node_department_id`= :department_id
               and `job_title` in (".$param['job_title_id'].")
               and `sys_store_id`= :sys_store_id
               and (`state` = 3 or `wait_leave_state`= 1  or (`state` = 2 and `leave_date`>= :leave_date))";
            $list = $this->getDI()->get("db_rbi")->query(
                $sql,
                [
                    'department_id' => $param['department_id'],
                    'sys_store_id' => $param['store_id'],
                    'leave_date' => $param['level_date']
                ]
            )->fetchAll(\Phalcon\Db::FETCH_ASSOC);

            return empty($list) ? []: $list;

        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("HcRepository:getStaffLeaveList error-" . $e->getMessage().'-----'.$e->getLine());
            return [];
        }
    }

    /**
     * 查找不可选的关联hc的staff_info_id
     * @return array
     */
    public function getHrHcLeaveStaffIds() {
        try {
            /*
            $sql = "SELECT
                        hr_hc_leave_staff.hc_id,
                        hr_hc_leave_staff.staff_info_id 
                    FROM
                        hr_hc_leave_staff
                        LEFT JOIN hr_hc ON hr_hc_leave_staff.hc_id = hr_hc.hc_id 
                    WHERE
                        hr_hc.state_code != 4 
                        AND hr_hc.approval_state_code not in (4, 5) ";
            */
            $sql = "SELECT
                        hr_hc_leave_staff.hc_id,
                        hr_hc_leave_staff.staff_info_id 
                    FROM
                        hr_hc_leave_staff
                        LEFT JOIN hr_hc ON hr_hc_leave_staff.hc_id = hr_hc.hc_id 
                    WHERE 
                    hr_hc.state_code != 4 
                    and (hr_hc.state_code != 1 or hr_hc.approval_state_code not in (4, 5) or approval_completion_time is null) 
                    and (state_code != 9 or approval_state_code != 5)";
            $list = $this->getDI()->get("db")->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $staff_info_ids = array_unique(array_column($list, 'staff_info_id'));
            return !empty($staff_info_ids) ? $staff_info_ids : [];
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("HcRepository:getHrHcLeaveStaff error-" . $e->getMessage().'-----'.$e->getLine());
            return [];
        }
    }

    /**
     * 获取hc申请离职员工列表
     * @param $hc_id
     * @return array
     */
    public function getHrHcLeaveStaffListByHcId($hc_id) {
        try {

            $sql = "SELECT
                        hc_id,
                        staff_info_id,
                        snapshot 
                    FROM
                        hr_hc_leave_staff
                    WHERE
                        hc_id = :hc_id";
            $list = $this->getDI()->get("db")->query($sql, ['hc_id' => $hc_id])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            return !empty($list) ? $list : [];
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("HcRepository:getHrHcLeaveStaffListByHcId error-" . $e->getMessage().'-----'.$e->getLine());
            return [];
        }
    }

    /**
     * 根据id查找是否已经有关联hc
     * @param $staff_info_ids
     * @return array
     */
    public function getHrHcLeaveStaffsByStaffInfoIds($staff_info_ids) {
        try {
            if(empty($staff_info_ids)) {
                return [];
            }

            $ids = implode(',', $staff_info_ids);
            /*
            $sql = "SELECT
                        hc_id,
                        staff_info_id,
                        snapshot 
                    FROM
                        hr_hc_leave_staff
                    WHERE
                        staff_info_id in (".$ids.")";
            */

            $sql = "SELECT
                        hr_hc_leave_staff.hc_id,
                        hr_hc_leave_staff.staff_info_id 
                    FROM
                        hr_hc_leave_staff
                        LEFT JOIN hr_hc ON hr_hc_leave_staff.hc_id = hr_hc.hc_id 
                    WHERE 
                    hr_hc.state_code != 4 
                    and (hr_hc.state_code != 1 or hr_hc.approval_state_code not in (4, 5) or approval_completion_time is null) 
                    and (state_code != 9 or approval_state_code != 5) and hr_hc_leave_staff.staff_info_id in (".$ids.")";

            $list = $this->getDI()->get("db")->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            return !empty($list) ? $list : [];
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("HcRepository:getHrHcLeaveStaffsByStaffInfoIds error-" . $e->getMessage().'-----'.$e->getLine());
            return [];
        }
    }

    /**
     * 获取工号对应的语言
     */
    public function getStaffAcceptLang($staff_info_ids): array
    {
        if (empty($staff_info_ids)) {
            return [];
        }

        $staffIds = implode(',', $staff_info_ids);
        $equipment_types = implode(',', [LangEnums::$equipment_type['kit'], LangEnums::$equipment_type['backyard']]);

        $querySql = "
            SELECT b.staff_info_id, b.accept_language
            FROM staff_account b
            JOIN (
                SELECT staff_info_id as staff_id,max(updated_at) max_updated_at 
                FROM staff_account 
                WHERE staff_info_id IN ({$staffIds}) and equipment_type in ({$equipment_types}) AND accept_language IS NOT NULL
                GROUP BY staff_info_id 
            ) t on b.staff_info_id = t.staff_id and b.updated_at = t.max_updated_at
        ";
        return $this->getDI()->get('db_fle')->query($querySql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }

    /**
     * 通过Hc_id获取部门岗位信息
     * @param $params
     * @return array
     */
    public function getHcInfo($hcId): array
    {

        $audit_object = $this->modelsManager->createBuilder();
        $audit_object->columns(
            "main.hc_id, dep.name as department_name, job.job_name,main.worknode_id,main.department_id,main.job_title"
        );
        $audit_object->from(['main' => HrHcModel::class]);
        $audit_object->leftJoin(SysDepartmentModel::class, 'main.department_id = dep.id', 'dep');
        $audit_object->leftJoin(HrJobTitleModel::class, 'main.job_title = job.id', 'job');
        $audit_object->andWhere('main.hc_id = :hc_id:', ['hc_id' =>$hcId]);
        $data = $audit_object->getQuery()->execute()->getFirst();

        if($data){
            return $data->toArray();
        }
        return [];
    }
}
