<?php
/**
 * Author: Bruce
 * Date  : 2022-12-06 01:11
 * Description:
 */

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\HrOutsourcingOrderDetailModel;
use FlashExpress\bi\App\Models\backyard\HrOutsourcingOrderModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\OutsourcingCompanyModel;
use FlashExpress\bi\App\Models\backyard\StaffHikvisionModel;
use FlashExpress\bi\App\Models\backyard\OutsourcingStaffStoreOrderNumModel;

class OsOrderRepository extends BaseRepository
{

    public $timezone;

    public function __construct($timezone)
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    /**
     * 查询订单信息
     * @param $params
     * @return mixed
     */
    public function getOsOrderList($params)
    {
        $previousDay = date('Y-m-d', strtotime("-1 day", strtotime($params['employment_date'])));
        $nextDay     = date('Y-m-d', strtotime("+1 day", strtotime($params['employment_date'])));

        $default_conditions = "out_company_id IN ({out_company_id:array}) and employment_date in ({employment_dates:array}) and status in ({status:array})";
        $default_bind       = [
            'out_company_id'   => is_array($params['out_company_id']) ? $params['out_company_id'] : (array) $params['out_company_id'],
            'employment_dates' => [$previousDay, $params['employment_date'], $nextDay],
            'status'           => [HrOutsourcingOrderModel::STATUS_PENDING, HrOutsourcingOrderModel::STATUS_EFFECTIVE],
        ];
        if (isset($params['serial_no']) && !empty($params['serial_no'])) {
            $default_conditions        .= " and serial_no != :serial_no:";
            $default_bind['serial_no'] = $params['serial_no'];
        }

        return HrOutsourcingOrderModel::find([
            'columns'    => "id, serial_no, shift_id, employment_date",
            'conditions' => $default_conditions,
            'bind'       => $default_bind,
        ])->toArray();
    }

    /**
     * 获取外协订单信息
     * @param $osId
     * @return array
     */
    public function getOsOrderInfo($osId)
    {
        $orderInfo = HrOutsourcingOrderModel::findFirst([
            'conditions' => "id = :order_id:",
            'bind'       => [
                'order_id' => $osId,
            ],
        ]);

        return !empty($orderInfo) ? $orderInfo->toArray() : [];
    }

    /**
     *
     * @param $params
     * @param bool $isCount
     * @return mixed
     */
    public function getOrderDetailStaffQuery($params, $isCount = false)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('staff_info_id, staff_name as name');
        $builder->from(HrOutsourcingOrderDetailModel::class);

        $whereString = 'serial_no = :serial_no: ';
        if (is_array($params['serial_no'])) {
            $whereString = 'serial_no in ({serial_no:array}) ';
        }
        $builder->where($whereString, ['serial_no' => $params['serial_no']]);

        if ($isCount) {
            $builder->columns('count(*) as count');
            return $builder->getQuery()->getSingleResult()->toArray();
        }

        if (empty($params['is_all'])) {
            $builder->limit($params['page_size'], $params['page_size'] * ($params['page'] - 1));
        }
        $builder->orderBy('id desc');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 可选择的 工号信息 sql
     * @param $params
     * @param bool $isCount
     * @return mixed
     */
    public function getOptionalStaffQuery($params, $isCount = false)
    {
        //use_num >= 3; is_frequently_used = 1;
        $this->getDI()->get('logger')->write_log(['getOptionalStaffQuery'=>$params],'info');
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("oscs.staff_info_id, hsi.name,if(osson.use_num >= 3, '1', '0') AS is_frequently_used,osson.use_num");
        $builder->from(['oscs' => StaffHikvisionModel::class]);
        $builder->join(HrStaffInfoModel::class, 'hsi.staff_info_id = oscs.staff_info_id and state = :state: and hsi.formal = :formal:', 'hsi');
        $builder->leftJoin(OutsourcingStaffStoreOrderNumModel::class, "osson.staff_info_id = oscs.staff_info_id and osson.store_id = :store_id:", 'osson');
        //绑定参数初始化
        $bind = ['state'=>HrStaffInfoModel::STATE_1,'formal'=>HrStaffInfoModel::FORMAL_0,'store_id'=>$params['store_id'],'company_id'=>$params['company_id']];
        $builder->where('outsourcing_company_id = :company_id: ');
        $builder->andWhere('oscs.formal = :hik_formal: ', ['hik_formal' => StaffHikvisionModel::FORMAL_0]);
        if (!empty($params['use_staff_info_ids'])) {
            $builder->andWhere('oscs.staff_info_id not in ({staff_id:array}) ',['staff_id'=>$params['use_staff_info_ids']]);
        }

        //员工id or name
        if(!empty($params['staff_keyword'])) {
            $bind['staff_keyword'] = '%' . $params['staff_keyword'] . '%';
            $builder->andWhere('(hsi.staff_info_id LIKE :staff_keyword: OR hsi.name LIKE :staff_keyword:)');
        }

        if ($isCount) {
            $builder->columns('count(*) as count');
            return $builder->getQuery()->getSingleResult($bind)->toArray();
        }

        $builder->orderBy('osson.use_num desc,oscs.staff_info_id asc');
        $builder->limit($params['page_size'], $params['page_size'] * ($params['page'] - 1));

        return $builder->getQuery()->execute($bind)->toArray();
    }

    /**
     * 可选择的 工号信息 sql
     * @param $params
     * @param bool $isCount
     * @return mixed
     */
    public function getOptionalStaffQueryV2($params, $isCount = false)
    {
        //use_num >= 3; is_frequently_used = 1;
        $this->getDI()->get('logger')->write_log(['getOptionalStaffQuery'=>$params],'info');
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("oscs.staff_info_id, hsi.name,osc.company_name,if(osson.use_num >= 3, '1', '0') AS is_frequently_used,osson.use_num");
        $builder->from(['oscs' => StaffHikvisionModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'hsi.staff_info_id = oscs.staff_info_id ', 'hsi');
        $builder->leftJoin(OutsourcingStaffStoreOrderNumModel::class, "osson.staff_info_id = oscs.staff_info_id and osson.store_id = :store_id:", 'osson');
        $builder->leftJoin(OutsourcingCompanyModel::class, 'oscs.outsourcing_company_id=osc.id','osc');
        //绑定参数初始化
        $bind = ['state'=>HrStaffInfoModel::STATE_1,'formal'=>HrStaffInfoModel::FORMAL_0,'store_id'=>$params['store_id'],'job_title'=>$params['job_title']];
        $builder->where('1 and oscs.is_sync=1 and hsi.state = :state: and hsi.formal = :formal: and hsi.sys_store_id=:store_id: and hsi.job_title=:job_title:');
        $builder->andWhere('oscs.formal = :hik_formal: ', ['hik_formal' => StaffHikvisionModel::FORMAL_0]);

        if (!empty($params['use_staff_info_ids'])) {
            $builder->andWhere('oscs.staff_info_id not in ({staff_id:array}) ',['staff_id'=>$params['use_staff_info_ids']]);
        }

        //员工id or name
        if(!empty($params['staff_keyword'])) {
            $builder->andWhere('hsi.staff_info_id LIKE :staff_keyword: OR hsi.name LIKE :staff_keyword:', ['staff_keyword' => $params['staff_keyword']]);
        }else{
            $builder->andWhere('outsourcing_company_id = :company_id: ',['company_id'=> $params['company_id']]);
        }

        if ($isCount) {
            $builder->columns('count(*) as count');
            return $builder->getQuery()->getSingleResult($bind)->toArray();
        }

        $builder->orderBy('osson.use_num desc,oscs.staff_info_id asc');
        $builder->limit($params['page_size'], $params['page_size'] * ($params['page'] - 1));

        return $builder->getQuery()->execute($bind)->toArray();
    }

    /**
     * 获取外协申请订单列表
     * @param $params
     * @param bool $isCount
     * @return mixed
     */
    public function getOutsourcingOrderQuery($params, $isCount = false)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id as order_id, serial_no, job_id, store_id, employment_date, shift_id, demend_num, final_audit_num, status, need_remark, shift_begin_time, shift_end_time,order_type, is_exceeds');
        $builder->from(HrOutsourcingOrderModel::class);
        $builder = $this->getBuilderWhere($builder, $params);
        if ($isCount) {
            $builder->columns('count(*) as count');
            return $builder->getQuery()->getSingleResult()->toArray();
        }

        $builder->limit($params['page_size'], $params['page_size'] * ($params['page'] - 1));
        //待审批
        $orderString = HrOutsourcingOrderModel::STATUS_PENDING == $params['status'] ? 'is_exceeds desc, id desc' : 'id desc';
        $builder->orderBy($orderString);
        return $builder->getQuery()->execute()->toArray();
    }



    /**
     * 构建 查询 条件
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function getBuilderWhere($builder, $params)
    {
        $builder->where('out_company_id = :company_id:', ['company_id' => $params['company_id']]);

        if(!empty($params['month'])) {
            $startDate = $params['month'].'-01';
            $endDate   = date('Y-m-t', strtotime($startDate));
            $builder->andWhere('employment_date >= :start_date: and employment_date <= :end_date: ',
                ['start_date' => $startDate, 'end_date' => $endDate]);
        }

        if(!empty($params['start_date']) && !empty($params['end_date'])) {
            $startDateTime = gmdate('Y-m-d H:i:s',strtotime($params['start_date'] . ' 00:00:00'));
            $endDateTime   = gmdate('Y-m-d H:i:s',strtotime($params['end_date'] . ' 23:59:59'));

            $builder->andWhere('created_at >= :start_date: and created_at <= :end_date: ',
                ['start_date' => $startDateTime, 'end_date' => $endDateTime]);
        }

        if (!empty($params['status'])) {
            switch ($params['status']) {
                case HrOutsourcingOrderModel::STATUS_PENDING :
                    $builder->andWhere('status = :status:', ['status' => HrOutsourcingOrderModel::STATUS_PENDING]);
                    break;
                case HrOutsourcingOrderModel::STATUS_EFFECTIVE :
                    $builder->andWhere('status = :status:', ['status' => HrOutsourcingOrderModel::STATUS_EFFECTIVE]);
                    break;
                case HrOutsourcingOrderModel::STATUS_CLOSED :
                    $builder->andWhere('status in ({status:array})', [
                        'status' => [
                            HrOutsourcingOrderModel::STATUS_CLOSED,
                            HrOutsourcingOrderModel::STATUS_CANCELED,
                        ],
                    ]);
                    break;
                default:
                    break;
            }
        }

        if(!empty($params['serial_no'])) {
            $builder->andWhere('serial_no like :serial_no: ',
                ['serial_no' => '%' . $params['serial_no'] . '%']);
        }

        if(!empty($params['store_ids'])) {
            $builder->andWhere('store_id in ({store_ids:array}) ',
                ['store_ids' => $params['store_ids']]);
        }
        return $builder;
    }

    /**
     * 获取工单已选员工数量
     * @param $serial_nos
     * @return mixed
     */
    public function getOrderDetailStaffNumber($serial_nos)
    {
        if(empty($serial_nos)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("serial_no,count(1) as count");
        $builder->from(HrOutsourcingOrderDetailModel::class);
        $builder->where('serial_no in ({serial_nos:array})', ['serial_nos' => $serial_nos]);
        $builder->groupby('serial_no');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取员工信息
     * @param $staffIds
     * @return mixed
     */
    public function getStaffInfo($staffIds)
    {
       /* return HrStaffInfoModel::find([
            'columns'    => "staff_info_id, name, identity, company_name_ef, mobile, sex, personal_email, bank_type",
            'conditions' => "staff_info_id in ({staff_ids:array})",
            'bind'       => [
                'staff_ids' => $staffIds,
            ],
        ])->toArray();*/
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('hsi.staff_info_id, hsi.name, hsi.identity, hsi.company_name_ef, hsi.mobile, hsi.sex, hsi.personal_email, hsi.bank_type,oscs.outsourcing_company_id')
            ->from(['hsi'=> HrStaffInfoModel::class])
            ->leftJoin(StaffHikvisionModel::class, 'hsi.staff_info_id=oscs.staff_info_id','oscs')
            ->where('hsi.staff_info_id in ({staff_ids:array})',['staff_ids'=>$staffIds]);

        return $builder->getQuery()->execute()->toArray();

    }

    /**
     * 获取订单详情
     * @param $serialNo
     * @return array
     */
    public function getHrOutsourcingOrderDetail($serialNo)
    {
        $detail = HrOutsourcingOrderDetailModel::find([
            'columns'    => "id",
            'conditions' => "serial_no = :serial_no:",
            'bind'       => [
                'serial_no' => $serialNo,
            ],
        ]);

        return !empty($detail) ? $detail->toArray() : [];
    }

    /**
     * 获取其他的处于待生效和已生效的订单
     * @param $params
     * @return array
     */
    public function getOtherOrderList($params)
    {
        $list= HrOutsourcingOrderModel::find([
            'columns'    => "id, serial_no, shift_id, employment_date",
            'conditions' => "order_type=1 and out_company_id != 0 and serial_no != :serial_no: and status in ({status:array})",
            'bind'       => [
                'serial_no' => $params['serial_no'],
                'status'    => [HrOutsourcingOrderModel::STATUS_PENDING, HrOutsourcingOrderModel::STATUS_EFFECTIVE],
            ],
        ])->toArray();

        return $list;
    }

    public static function getOrderList($params, $columns = ['*'])
    {
        if(empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind = [];

        if(!empty($params['init_serial_no'])) {
            $conditions .= ' and init_serial_no = :init_serial_no:';
            $bind['init_serial_no'] = $params['init_serial_no'];
        }

        if(!empty($params['serial_nos'])) {
            $conditions .= ' and serial_no in ({serial_nos:array})';
            $bind['serial_nos'] = $params['serial_nos'];
        }

        if(!empty($params['id'])) {
            $conditions .= ' and id = :id:';
            $bind['id'] = $params['id'];
        }

        return HrOutsourcingOrderModel::find([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();
    }

}