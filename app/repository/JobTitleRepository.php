<?php

namespace FlashExpress\bi\App\Repository;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;

class JobTitleRepository extends BaseRepository
{
    public function __construct()
    {
        parent::__construct();
    }

    public static function getJobTitleInfo($job_title_id){
        if(empty($job_title_id)){
            return false;
        }
        return HrJobTitleModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'=> [
                'id'  => $job_title_id,
            ]
        ]);
    }

    public static function getJobTitleByIds($job_title_id, $columns = 'id, job_name'): array
    {
        if (!is_array($job_title_id)) {
            $job_title_id = [$job_title_id];
        }
        $conditions = 'id in({job_title_id:array})';
        return HrJobTitleModel::find([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => [
                'job_title_id' => $job_title_id,
            ],
        ])->toArray();
    }
}