<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2020/9/27
 * Time: 5:06 PM
 */

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\StatisticForOvertimeModel;

class StatisticForOvertimeRepository extends BaseRepository{

    public function initialize()
    {

        parent::initialize();
    }


    public function find_by_date($start, $end = '',$flag = ''){
        if(empty($start))
            return false;

        if(empty($end))
            $end = $start;

        $conditions = 'date_at between :start: and :end:';
        $bind['start'] = $start;
        $bind['end'] = $end;
        if(!empty($flag)){
            $flag = ($flag == enums::OT_STATISTIC_TYPE_3) ? enums::OT_STATISTIC_TYPE_1 : $flag;

            $conditions .= ' and flag = :flag:';
            $bind['flag'] = $flag;
        }

        $data = StatisticForOvertimeModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
        ])->toArray();

        return $data;

    }

    public function find_by_store_date($store, $date){

        if(empty($store) || empty($date))
            return false;
        $sql = "select * from statistic_for_overtime 
                where store_id = '{$store}' and date_at = '{$date}'
                ";

        return $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);

    }

    public function delete_by_date($date){
        if(empty($date))
            return false;
        $sql = "delete  from statistic_for_overtime where date_at = '{$date}'";
        return $this->getDI()->get('db')->execute($sql);
    }

    //取 日期对应上一周的
    public function find_by_store($store_id, $date,$flag = 1){
        if(empty($store_id) || empty($date))
            return false;

        $week_date = date('Y-m-d',strtotime("{$date} -1 week"));
        $start = weekStart($week_date);
        $end = weekEnd($week_date);

        $all_data = $this->find_by_date($start,$end,$flag);
        $return = array();
        if(empty($all_data))
            return $return;

        //all_effective_num K值  store_effective_num K1
        //一期逻辑
        if(in_array($flag,array(enums::OT_STATISTIC_TYPE_1,enums::OT_STATISTIC_TYPE_3)))
            $return = $this->format_dc($store_id,$all_data);

        if($flag == enums::OT_STATISTIC_TYPE_2)
            $return = $this->format_shop($store_id,$all_data);

        $this->getDI()->get('logger')->write_log("find_by_store ".json_encode($return),'info');

        return $return;
    }

    //一期需求
    protected function format_dc($store_id,$all_data){
        //实时获取 当时该网点人数
        $sql = "select count(1) from hr_staff_info where state = 1 and is_sub_staff = 0 and  job_title in (37) 
                    and sys_store_id = '{$store_id}' ";
        $job_num = $this->getDI()->get('db_rby')->fetchColumn($sql);

        $all_effective_num = $store_effective_num = $all_time_last = $store_time_last = $all_dc_time  = 0;
        foreach ($all_data as $da){
            $num = $da['delivery_num'] + $da['pickup_num'];
            $all_effective_num += $num;
            $all_time_last += $da['attendance_time'];
            $all_dc_time += $da['attendance_time_for_37'];
            if($da['store_id'] == $store_id){
                $store_effective_num += $num;
                $store_time_last += $da['attendance_time_for_37'];
            }
        }
        $return['all_effective_num'] = 0;//K值
        if(!empty($all_time_last) && !empty($all_dc_time))
            $return['all_effective_num'] = round(($all_effective_num / ($all_dc_time / 60)),1);

        $return['store_effective_num'] = 0;//K1值
        if(!empty($store_time_last) && !empty($store_time_last))
            $return['store_effective_num'] = round(($store_effective_num / ($store_time_last / 60)),1);
        $return['job_num'] = $job_num;//职位总人数
        return $return;
    }

    //二期需求
    protected function format_shop($store_id,$all_data){
        //需要数据
        // 1 员工申请OT日所在网点出勤人数 attendance_num
        //2 员工申请OT日所在网点总揽件量 parcel_num
        // 3 上周SHOP相关网点平均工作效率= （上周SHOP相关网点总揽收量+ 上周SHOP相关网点总派件量）/（上周SHOP相关网点出勤总人次*9）
        // 4 上周所在网点平均工作效率= （上周特定网点总揽收量+ 上周特定网点总派件量）/（上周特定网点出勤总人次*9）
        $all_effective_num = $store_effective_num = $store_time = $all_time  = 0;
        foreach ($all_data as $da){
            $num = $da['delivery_num'] + $da['pickup_num'];
            $all_effective_num += $num;
            $all_time += $da['attendance_time'];//出勤人次
            if($da['store_id'] == $store_id){
                $store_effective_num += $num;//网点件数量
                $store_time += $da['attendance_time'];//网点出勤人次
            }
        }
        $return['all_effective_num'] = 0;//K值
        if(!empty($all_time))
            $return['all_effective_num'] = round(($all_effective_num / ($all_time * 9)),1);

        $return['store_effective_num'] = 0;//K1值
        if(!empty($store_time))
            $return['store_effective_num'] = round(($store_effective_num / ($store_time * 9)),1);

        return $return;
    }

}