<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\backyard\UserModel;
use League\OAuth2\Server\Entities\ClientEntityInterface;
use League\OAuth2\Server\Entities\UserEntityInterface;
use League\OAuth2\Server\Repositories\UserRepositoryInterface;
use Phalcon\Db\Column;

/**
 * Class UserRepository
 * @package App\Repositories
 */
class UserRepository extends Repository implements UserRepositoryInterface
{
    /**
     * Model class name for the concrete implementation
     *
     * @return string
     */
    public function modelName()
    {
        return UserModel::class;
    }

    /**
     * Get a user entity.
     *
     * @param string $username
     * @param string $password
     * @param string $grantType The grant type used
     * @param ClientEntityInterface $clientEntity
     *
     * @return UserEntityInterface
     */
    public function getUserEntityByUserCredentials(
        $username,
        $password,
        $grantType,
        $clientEntity
    )
    {
        return $this->getUserEntityByUserMobile($username);
    }


    /**
     * Get a user entity.
     *
     * @param string $uid
     *
     * @return UserModel
     */
    public function getUserEntityByUserId($uid)
    {
        return $this->findOne(['u_id' => $uid]);
    }

    /**
     * @param $mobile
     * @return mixed|null
     */
    public function getUserEntityByUserMobile($mobile)
    {
        return $this->findOne(['mobile' => $mobile,'cc'=>'66']);
    }

    /**
     * @param $uids
     *
     */
    public function getUserByUserIds($uids)
    {
        $list = UserModel::find([
            "conditions" => " find_in_set(u_id, :user_ids:)",
            "bind" => [
                "user_ids" => implode(",", $uids)
            ],
            "bindTypes" => [
                "user_ids" => Column::BIND_PARAM_STR
            ]
        ]);
        if ($list) {
            return $list->toArray();
        }
        return [];
    }
}
