<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\backyard\AttendanceDataV2Model;

class AttendanceDataV2Repository extends BaseRepository
{

    /**
     * 有打卡（上班或下班）或补卡记录的正式快递员 排除请假员工（如果请了半天假，算0.5个人出勤）
     * @param $store_id
     * @param $job_title
     * @param $date_list
     * @return mixed
     */
    public static function getStoreStaffSumAttendanceTime($store_id, $job_title, $date_list)
    {

        return AttendanceDataV2Model::find([
            'conditions' => "stat_date in ({stat_date:array}) AND sys_store_id = :sys_store_id: AND job_title in ({job_title:array}) ",
            'columns'    => 'stat_date,sum(attendance_time/10) as total',
            'bind'       => [
                'job_title'    => $job_title,
                'sys_store_id' => $store_id,
                'stat_date'    => $date_list,
            ],
            'group'      => 'stat_date',
        ])->toArray();
    }


    /**
     * 
     * @param $store_id
     * @param $job_title
     * @param $date_list
     * @return mixed
     */
    public static function getStoreStaffCountAttendanceTime($store_id, $job_title, $date_list)
    {

       return AttendanceDataV2Model::find([
            'conditions' => "stat_date in ({stat_date:array}) AND sys_store_id = :sys_store_id: AND job_title in ({job_title:array}) AND attendance_time > 0 ",
            'columns'    => 'stat_date,count(1) as total',
            'bind'       => [
                'job_title'    => $job_title,
                'sys_store_id' => $store_id,
                'stat_date'    => $date_list,
            ],
            'group'      => 'stat_date',
        ])->toArray();
    }


}
