<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Enums\AuditListEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\SystemExternalApprovalModel;

class StopRepository extends BaseRepository {

    /**
     * 获取审批回调失败的数据
     * @param $date_time
     * @return array
     */
    public function getCallBackData($date_time): array
    {
        if (empty($date_time)) {
            return [];
        }
        return SystemExternalApprovalModel::find([
            "conditions" => 'created_at >= :created_at: AND is_call_third_party = :is_call_third_party: AND biz_type = :biz_type: AND deleted = :deleted:',
            'bind'       => [
                'created_at'          => $date_time,
                'biz_type'            => enums::$audit_type['STOP'],
                'is_call_third_party' => SystemExternalApprovalModel::is_call_third_party_2,
                'deleted'             => enums::DELETED_NO,
            ],
            'columns'    => 'id,serial_no,biz_type,state',
        ])->toArray();
    }

    /**
     * 我的申请表数据
     * @param array $biz_value
     * @return mixed
     */
    public function getAuditApply(array $biz_value)
    {
        return AuditApplyModel::find([
            'conditions' => "biz_value IN ({biz_value:array}) AND biz_type = :biz_type:",
            'bind'       => [
                'biz_value' => $biz_value,
                'biz_type'  => AuditListEnums::APPROVAL_TYPE_STOP
            ],
            'columns'    => 'id,serial_no,biz_type,state,reject_reason,final_approver',
        ])->toArray();
    }

}
