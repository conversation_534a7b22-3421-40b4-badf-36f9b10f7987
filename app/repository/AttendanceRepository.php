<?php
/**
 * Created by <PERSON>p<PERSON>torm.
 * User: nick
 * Date: 2019/5/8
 * Time: 下午3:38
 */


namespace FlashExpress\bi\App\Repository;

use app\enums\StaffAuditStatusEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AttendanceConfigForDepartmentModel;
use FlashExpress\bi\App\Models\backyard\AttendanceConfigForStaffidModel;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditReissueForBusinessModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;


class AttendanceRepository extends BaseRepository
{
    public $timezone;

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone =  $timezone;
    }

    public function initialize()
    {
        parent::initialize();
    }


    /**
     *
     * 根据 区间 获取打卡记录
     * @param $staff_id
     * @param $start_date
     * @param $end_date
     * @return array
     */
    public function getAttendanceInfo($staff_id,$start_date,$end_date){
        $sql = "select * from staff_work_attendance where staff_info_id = {$staff_id} and 
                started_at <= '{$start_date}' and end_at >= '{$end_date}' limit 1";
        $db = $this->getDI()->get('db_rby')->query($sql);
        $data =  $db->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return empty($data) ? array() : $data[0];
    }

    //根据获取 双班次的打卡信息
    public function getNewAttendanceOtInfo($staff_id,$date){
        $cardInfo = StaffWorkAttendanceModel::find([
            'columns' => "id,staff_info_id,attendance_date,started_at,end_at,concat(attendance_date,'_',shift_type) as u_key",
            'conditions' => 'staff_info_id = :staff_id: and attendance_date = :date:',
            'bind' => ['staff_id' => $staff_id,'date' => $date]
        ])->toArray();

        return empty($cardInfo) ? [] : array_column($cardInfo,null,'u_key');
    }



    //补卡申请需求 获取区间范围内所有打卡记录
    public function getSignInfo($staff_id, $start_date,$end_date){
        $sql = "select staff_info_id,attendance_date date_at,
                date_format(CONVERT_TZ(started_at,  '+00:00', '{$this->timezone}'),'%Y-%m-%d %H:%i:%S') started_at,
                date_format(CONVERT_TZ(end_at,  '+00:00', '{$this->timezone}'),'%Y-%m-%d %H:%i:%S') end_at
                ,TIMESTAMPDIFF(SECOND,started_at,end_at) as second_last
                from staff_work_attendance
                where staff_info_id = {$staff_id} and attendance_date >= '{$start_date}' and attendance_date <= '{$end_date}'
                ";

        $db = $this->getDI()->get('db_rby')->query($sql);
        return $db->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }

    //补卡申请需求  获取单日 打卡记录
    public function getDateInfo($staff_id, $date, $shift_type = 0): array
    {
        $conditions = 'staff_info_id = :staff_info_id: AND attendance_date = :attendance_date:';
        $bind       = ['staff_info_id' => $staff_id, 'attendance_date' => $date];
        if (!empty($shift_type)) {
            $conditions         .= " AND shift_type = :shift_type:";
            $bind['shift_type'] = intval($shift_type);
        }

        $attendanceModel = StaffWorkAttendanceModel::findFirst([
            'columns'    => [
                'id',
                'staff_info_id',
                'attendance_date as date_at',
                "CONVERT_TZ(started_at, '+00:00', '{$this->timezone}')  as started_at",
                "CONVERT_TZ(end_at, '+00:00', '{$this->timezone}') end_at ",
                'shift_start',
                'shift_end',
                'started_at as start_data',
                'end_at as end_data',
                'shift_type',
            ],
            'conditions' => $conditions,
            'bind'       => $bind,

        ]);
        return $attendanceModel ? $attendanceModel->toArray() : [];
    }

    /**
     * 用差分天查询请假的人
     */
    public function getStaffSplit($staffs = array(),$start_date,$end_date)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('a.staff_info_id');
        $builder->from(['a' => StaffAuditModel::class]);
        $builder->leftJoin(StaffAuditLeaveSplitModel::class, 'a.audit_id = sp.audit_id', 'sp');
        $builder->where('sp.staff_info_id in ({staffs:array}) 
                and a.status in (1,2) and a.audit_type = 2 
                and sp.date_at between :start_date: and :end_date: ',
            ['start_date' => $start_date,'end_date' => $end_date,'staffs' => $staffs]);
        // $count = $builder->getQuery()->execute()->count();
        $data = $builder->getQuery()->execute()->toArray();
        return $data;
    }

    /**
     * 用差分天查询请假的人
     */
    public function getStaffSplitByStatus($staffs = array(),$start_date,$end_date,$status = '1,2')
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('a.staff_info_id');
        $builder->from(['a' => StaffAuditModel::class]);
        $builder->leftJoin(StaffAuditLeaveSplitModel::class, 'a.audit_id = sp.audit_id', 'sp');
        $builder->where('sp.staff_info_id in ({staffs:array}) 
                and a.status in ( :status: ) and a.audit_type = 2 
                and sp.date_at between :start_date: and :end_date: ',
            ['start_date' => $start_date,'end_date' => $end_date,'staffs' => $staffs, 'status' => $status]);
        // $count = $builder->getQuery()->execute()->count();
        $data = $builder->getQuery()->execute()->toArray();
        return $data;
    }

    /**
     * 通过工号查询打卡的打卡列表
     */
    public function getDateInfoByStaffs($staffs = array(), $date)
    {
        $data = StaffWorkAttendanceModel::find([
                    'columns' => 'staff_info_id',
                    'conditions' => 'staff_info_id in ({staffs:array}) and attendance_date = :date: ',
                    'bind' => ['staffs' => $staffs ,'date' => $date]
                ])->toArray();
        return $data ?? array();
    }


    /**
     * 新增记录
     * @param $insert
     */
    public function insertInfo($insert){
        $db = $this->getDI()->get('db');
        $db->insertAsDict('staff_work_attendance', $insert);
        return $db->lastInsertId();
    }

    //根据id 更新字段
    public function updateInfo($key, $data){
        if(empty($data))
            return false;

        $format = $this->formatUpdate($data);
        $up_sql = "update staff_work_attendance set {$format} where id = {$key}";
        $db = $this->getDI()->get('db');
        return  $db->execute($up_sql);
    }

     //根据id 更新字段
     public function updateStaffWorAttendance($staff_id,$date, $data){
        if(empty($data))
            return false;

        $format = $this->formatUpdate($data);
        $up_sql = "update staff_work_attendance set {$format} where staff_info_id = {$staff_id} and attendance_date = '{$date}' ";
        $db = $this->getDI()->get('db');
        return  $db->execute($up_sql);
    }

    //是否有考勤底片
    public function get_attendance_photo($staff_id){
        $sql = " select * from staff_work_attendance_attachment where staff_info_id = '{$staff_id}' and deleted = 0";
        $db = $this->getDI()->get('db_rby')->query($sql);
        return $db->fetch(\Phalcon\Db::FETCH_ASSOC);
    }

    //获取总部考勤范围
    public function get_header_range(){
        $sql = " select longitude lng,latitude lat,attendance_range from headquarters_attendance_range where deleted = 0";
        $db = $this->getDI()->get('db_rby')->query($sql);
        return $db->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }


    //获取 网点 配置的打卡范围 如果没配置 默认 200米
    public function get_store_range($store_id){
        $storeInfo = SysStoreModel::findFirst([
            'conditions' => "id = :store_id:",
            'bind' => [
                'store_id'  => $store_id,
            ]
        ]);
        if (!empty($storeInfo) && $storeInfo->category == enums::$stores_category['ffm']) {
            if (isCountry('TH')) {
                return 150;//邮件需求 仓储的打卡范围给到150
            }
            if (isCountry('PH')) {
                return 30;//邮件需求 仓储的打卡范围给到30
            }
        }

        if (date('Y-m-d') >= '2024-01-20' && isCountry('PH') && !empty($storeInfo) && in_array($storeInfo->category,
                [enums::$stores_category['hub'], enums::$stores_category['bhub'],enums::$stores_category['os']])) {
            return 50;//邮件需求 仓储的打卡范围给到70 邮件需求 改成50
        }

        $sql = " select attendance_range from staff_work_attendance_range where organization_id = '{$store_id}' and deleted = 0";
        return $this->getDI()->get('db_rby')->fetchColumn($sql);
    }

    /**
     * 清楚当天的打卡信息
     */
    public function clear_staff_work_attendance($staff_id ,$date){
        $mod = StaffWorkAttendanceModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and attendance_date = :date: ',
            'bind' => ['staff_id' => $staff_id ,'date' => $date]
        ]);
        if (!empty($mod)){
           return $mod->delete();
        }
        return false;
    }

    /**
     * 获取申请支援的员工信息
     * @param $staff_info_id
     * @param $date 打卡又不一定是今天
     * @return array
     */
    public function getSupportOsStaffInfo($staff_info_id,$date = ''): array
    {
        //到期时间是当地时间
        $today = date('Y-m-d');
        if(!empty($date)){
            $today = $date;
        }
        $bind =  ['staff_info_id' => $staff_info_id ,'status' => StaffAuditStatusEnums::AUDIT_STATUS_PASSED,'employment_begin_date'=>$today,'employment_end_date'=>$today];
        $supportStaffInfo = HrStaffApplySupportStoreModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and status = :status: and employment_begin_date <= :employment_begin_date: and employment_end_date >= :employment_end_date: and support_status != 4',
            'bind' =>$bind
        ]);

        return $supportStaffInfo ? $supportStaffInfo->toArray():[];

    }

    /**
     * 批量获取申请支援的员工信息
     * @param $staff_info_ids
     * @param string $date
     * @return array
     */
    public function getSupportOsMultiStaffInfo($staff_info_ids, string $date = ''): array
    {
        if (empty($staff_info_ids)) {
            return [];
        }

        //到期时间是当地时间
        $today = date('Y-m-d');
        if (!empty($date)) {
            $today = $date;
        }

        $bind = ['staff_info_id'         => $staff_info_ids,
                 'status'                => StaffAuditStatusEnums::AUDIT_STATUS_PASSED,
                 'employment_begin_date' => $today,
                 'employment_end_date'   => $today,
        ];
        return HrStaffApplySupportStoreModel::find([
            'conditions' => 'staff_info_id in ({staff_info_id:array}) and status = :status: and employment_begin_date <= :employment_begin_date: and employment_end_date >= :employment_end_date: and support_status != 4',
            'bind'       => $bind,
        ])->toArray();
    }

    /**
     * 获取申请支援的员工信息
     * @param $staff_info_id
     * @param $date
     * @return array
     */
    public function getSupportInfoBySubStaff($staff_info_id,$date = ''): array
    {
        //到期时间是当地时间
        $today = date('Y-m-d');
        if(!empty($date)){
            $today = $date;
        }
        $bind =  ['sub_staff_info_id' => $staff_info_id ,'status' => StaffAuditStatusEnums::AUDIT_STATUS_PASSED,'employment_begin_date'=>$today,'employment_end_date'=>$today];
        $supportStaffInfo = HrStaffApplySupportStoreModel::findFirst([
            'conditions' => 'sub_staff_info_id = :sub_staff_info_id: and status = :status: and employment_begin_date <= :employment_begin_date: and employment_end_date >= :employment_end_date: and support_status != 4',
            'bind' =>$bind
        ]);

        return $supportStaffInfo ? $supportStaffInfo->toArray():[];

    }

    /**
     * 获取部门列表
     */
    public function getDepartmentConfigListById($departmentId){
        if(empty($departmentId)){
            return [];
        }
        $data = AttendanceConfigForDepartmentModel::find(['conditions' => 'is_delete = 0 and department_id = :department_id:',
            'bind' => ['department_id' => $departmentId]]);
        return $data ? $data->toArray() : [];
    }

    /**
     * 获取员工是否在白名单
     */
    public function getStaffConfigById($staffInfoId){
        if(empty($staffInfoId)){
            return [];
        }
        $data = AttendanceConfigForStaffidModel::findFirst(['conditions' => 'is_delete = 0 and staff_info_id = :staff_info_id:',
            'bind' => ['staff_info_id' => $staffInfoId]]);
        return $data ? $data->toArray() : [];
    }

    /**
     * Notes: 根据员工工号和考勤日期获取一条打卡记录
     *        SQL样例：SELECT * FROM staff_work_attendance WHERE staff_info_id = 17156 AND attendance_date = '2022-10-19' LIMIT 1;
     * @param $staffId
     * @param $attendanceDate
     * @return StaffWorkAttendanceModel|false
     */
    public function getStaffWorkAttendanceInfo($staffId, $attendanceDate)
    {
        return StaffWorkAttendanceModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: AND attendance_date = :attendance_date:',
            'bind'       => [
                'staff_info_id'   => $staffId,
                'attendance_date' => $attendanceDate,
            ],
        ]);
    }


    /**
     * 获取员工多个日期的 打卡记录 存在 一个日期 多条记录的情况
     * @param $staffId  工号
     * @param array $dateList 获取 考勤日期list
     * @return array
     */
    public function getAttendanceData($staffId,array $dateList){
        if(empty($staffId) || empty($dateList)){
            return [];
        }

        $data =  StaffWorkAttendanceModel::find([
            'columns' => "staff_info_id,attendance_date,shift_id,shift_ext_id,started_at,end_at,concat(attendance_date,'_',shift_type) as u_key",
            'conditions' => 'staff_info_id = :staff_info_id: AND attendance_date in ({dates:array})',
            'bind'       => [
                'staff_info_id'   => $staffId,
                'dates' => $dateList,
            ],
            'order' => 'attendance_date,shift_type'
        ])->toArray();

        if(empty($data)){
            return [];
        }
        return empty($data) ? [] : array_column($data,null,'u_key');
    }


    /**
     * 获取员工多个日期的 打卡记录 存在 一个日期 多条记录的情况
     * @param $staffId  工号
     * @param array $dateList 获取 考勤日期list
     * @return array
     */
    public function getTripAttendanceData($staffId,array $dateList){
        if(empty($staffId) || empty($dateList)){
            return [];

        }

        $data =  StaffAuditReissueForBusinessModel::find([
            'columns' => "staff_info_id,attendance_date,shift_id,shift_ext_id,start_time as started_at,end_time as end_at,concat(attendance_date,'_',shift_type) as u_key",
            'conditions' => 'staff_info_id = :staff_info_id: AND attendance_date in ({dates:array})',
            'bind'       => [
                'staff_info_id'   => $staffId,
                'dates' => $dateList,
            ],
            'order' => 'attendance_date,shift_type'
        ])->toArray();

        if(empty($data)){
            return [];
        }

        return empty($data) ? [] : array_column($data,null,'u_key');

    }

    /**
     * @description 获取指定网点、在指定日期还在上班的
     * @param $store_id
     * @param $attendance_date
     * @return int
     */
    public function getAttendanceCourierNum($store_id, $attendance_date): int
    {
        $staffInfo = StaffWorkAttendanceModel::find([
            "organization_id = :store_id: and attendance_date = :attendance_date: and job_title = :job_title: and
            started_at IS NOT NULL and end_at IS NULL",
            "bind" => [
                "store_id"        => $store_id,
                "attendance_date" => $attendance_date,
                "job_title"       => enums::$job_title['van_courier']
            ],
            "columns" => "staff_info_id"
        ])->toArray();
        return count($staffInfo);
    }
}