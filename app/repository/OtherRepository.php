<?php

namespace FlashExpress\bi\App\Repository;

class OtherRepository extends BaseRepository
{

    public $timezone;

    public function __construct($timezone = '',$lang = 'zh-CN')
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    public function initialize()
    {

        parent::initialize();
    }

    /**
     * [ 因未回公款停职的员工 hr_staff_info ]
     * @param  网点
     * @return array
     */
    public function stopDutiestaff( $userinfo )
    {
        // SELECT * FROM hr_staff_info WHERE sys_store_id = 'TH01010102'  AND  state =3 AND stop_duty_reason = 7 AND stop_duties_date = '2019-12-27' ;
        $yesterday =  date("Y-m-d",strtotime("-1 day"));
        $today = date("Y-m-d");
        $sql = " SELECT * FROM hr_staff_info WHERE sys_store_id = '{$userinfo['organization_id']}'  AND  state = 3 AND stop_duty_reason = 7 AND stop_duties_date = '{$today}' ; ";
        $db = $this->getDI()->get('db_rby')->query($sql);
        $infoArr = $db->fetchall(\Phalcon\Db::FETCH_ASSOC);

        return $infoArr??[];
    }

    /**
     * [ 获取快递员未回款信息 store_receivable_bill_detail]
     * @param  string
     * @return
     */
    public function courierReceivable( $userinfo ,$stafforstort = 1 )
    {
        if ($stafforstort == 1) {
            //工号查询该网点未回款
            $sql = "
                SELECT * from (
                    select  
                        round( SUM(receivable_amount)/100 , 2 ) as receivable_amount,
                        COUNT(*) AS receivable_count 
                    from store_receivable_bill_detail 
                    where  store_id = '{$userinfo['organization_id']}' and staff_info_id = {$userinfo['id']} and  state = 0 
                ) t1 where t1.receivable_amount > 0
               
                ;";
        } else if  ($stafforstort == 2)  {
            //网点未回款合计
            $sql = "
                SELECT * from (
                    select  
                    round( SUM(receivable_amount)/100 , 2 ) as receivable_amount,
                    COUNT(*) AS receivable_count 
                    from store_receivable_bill_detail 
                    where store_id = '{$userinfo['organization_id']}' and  state = 0 
                ) t1 where t1.receivable_amount > 0
                ;
                ";
        } else {
            //网点经理查看本网点所有未回款staff_id
            $sql = "
                    SELECT * from (
                        select  staff_info_id,
                        round( SUM(receivable_amount)/100 , 2 ) as receivable_amount,
                            COUNT(*) AS receivable_count from store_receivable_bill_detail where store_id = '{$userinfo['organization_id']}' and  state = 0 
                            group by staff_info_id
                    ) t1 where t1.receivable_amount > 0  
                    ;";
            $db = $this->getDI()->get('db_fle')->query($sql);
            $infoArr = $db->fetchall(\Phalcon\Db::FETCH_ASSOC);//多行
            return $infoArr ?? [];
        }
        $db = $this->getDI()->get('db_fle')->query($sql);
        $infoArr = $db->fetch(\Phalcon\Db::FETCH_ASSOC);//单行
        return $infoArr ?? [];
    }

    /**
     * [ 获取网点未回款信息 store_remittance_bill]
     * @param  string
     * @return
     */
    public function onCodReceivable( $userinfo )
    {
        $sql = "SELECT  round( SUM(cod_amount)/100 , 2 ) as cod_amount ,COUNT(*) AS cod_count FROM store_remittance_bill where store_id = '{$userinfo['organization_id']}' and  cod_state = 0";
        $db = $this->getDI()->get('db_fle')->query($sql);
        $infoArr = $db->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $infoArr??[];
    }


    /**
     * [ 获取网点未回款信息 store_remittance_bill]
     * @param  string
     * @return
     */
    public function onParcelReceivable( $userinfo )
    {
        $sql = "SELECT  round( SUM(parcel_amount)/100 , 2 ) as parcel_amount,COUNT(*) AS parcel_count FROM store_remittance_bill where store_id = '{$userinfo['organization_id']}' and  parcel_state = 0;";
        $db = $this->getDI()->get('db_fle')->query($sql);
        $infoArr = $db->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $infoArr??[];
    }


    /**
     * @param taff_audit_tool_log  日志表组装数据
     */
    public function insertLog($insert = [], $uinfo = [])
    {
        $last_id = 0;
        try {
            $insertSql  = $this->getInsertDbSql('staff_audit_tool_log', $insert);
            $this->getDI()->get('db')->query($insertSql);
            $last_id = $this->getDI()->get('db')->lastInsertId();
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("otherRe:insertLog:" . $e->getMessage());
        }
        return $last_id;
    }

    /**
     * 申请日志
     * @Access  public
     * staff_audit_union 更新+插入 106同意+107待处理 103驳回 104撤销[多条数据]
     * @Param   request
     * @Return  array
     */
    public function insterUnion( $union)
    {
        $db = $this->getDI()->get('db');
        try {
            $db->begin();
            $this->batch_insert( 'staff_audit_union' , $union );
            $db->commit();
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("otherRe:insterUnion:" . $e->getMessage());
            $db->rollback();
            return false;
        }
        return true;
    }

    /**
     * 审批日志插入
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function insertApproval($ApprovalArr, $userinfo = [])
    {
        $db = $this->getDI()->get('db');
        // 插入
        try {
            $db->begin();
            $success = $db->insertAsDict('staff_audit_approval', $ApprovalArr);
            if (!$success) {
                $db->rollback();
                return false;
            }
            $lastId = $db->lastInsertId();
            $db->commit();
        } catch (\Exception $e) {
            echo $e->getMessage();
            $db->rollback();
            return false;
        }
        return $lastId;
    }
    /**
     * 非最终的同意状态
     * @Access  public
     * staff_audit_tool_log
     * staff_audit_union 更新+插入 106同意+107待处理 103驳回 104撤销[多条数据]
     * staff_audit_approval 更新当前步骤，且插入下一步信息: 更新+插入 3驳回 4撤销 6同意+7待处理[多人一条数据]
     * @param $union
     * @param $approvalInster
     * @param $log
     * @return bool
     */
    public function insertUnionApprovalLog($union, $approvalInster, $log)
    {
        $db = $this->getDI()->get('db');
        $logtype = $log['type'];
        $sql = "SELECT * FROM staff_audit_approval WHERE audit_id = '{$approvalInster['audit_id']}' AND `type`={$approvalInster['type']} AND `level` = {$approvalInster['level']}; ";
        $infoExistArr = $db->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        if($infoExistArr){
            $this->getDI()->get('logger')->write_log("infoExistArr:" . json_encode($infoExistArr) ,'info');
            return true;
        }
        // 插入
        $insetlog = $this->getInsertDbSql('staff_audit_tool_log', $log);
        $insetapp = $this->getInsertDbSql( 'staff_audit_approval' , $approvalInster );
        //更新 staff_audit_union staff_audit_approval where id;
        $up_usql = "update staff_audit_union set status_union = 106   where origin_id = {$log['original_id']} and type_union = {$logtype} and status_union = 107  ;";
        $up_asql = "update staff_audit_approval set status = 6 where audit_id = {$log['original_id']} and type = {$logtype} and status = 7 ;";
        try {
            $db->begin();
            $db->execute($up_usql);
            $db->execute($up_asql);
            $db->execute($insetlog);
            $this->batch_insert( 'staff_audit_union' , $union );
            //$db->execute($insetunion);//先更新旧的，不要重复执行插入
            $db->execute($insetapp);
            $db->commit();
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("otherRe:insertUnionApprovalLog:" . $e->getMessage());
            $db->rollback();
            return false;
        }
        return true;
    }

    /**
     * 最终状态（最终同意和不同意情况下操作）
     * @param
     * tablename业务主表
     * staff_audit_tool_log
     * staff_audit_union 更新+插入 106同意+107待处理 103驳回 104撤销
     * staff_audit_approval 更新当前步骤，且插入下一步信息: 更新+插入 3驳回 4撤销 6同意+7待处理
     * @param 日志表组装数据 更改状态 和相关日志
     * @return bool
     */
    public function lastApproval($log, $updateArr = [], $tablename = '', $indexName = 'id')
    {
        $status_union = $log['to_status_type'];
        if($log['to_status_type'] == 2){
            $status_union = 6;
        }
        $logtype = $log['type'];
        $insetSql = $this->getInsertDbSql('staff_audit_tool_log', $log);
        //更新 staff_audit_union staff_audit_approval where id;
        $up_firstunsql = "update staff_audit_union set status_union = 10{$log['to_status_type']}   where origin_id = {$updateArr[$indexName]} and type_union = {$logtype} and status_union = 101  ;";
        $up_usql = "update staff_audit_union set status_union = 10{$status_union}   where origin_id = {$updateArr[$indexName]} and type_union = {$logtype} and status_union = 107  ;";
        $up_asql = "update staff_audit_approval set status = {$status_union} where audit_id = {$updateArr[$indexName]} and type = {$logtype} and status = 7 ;";

        $db = $this->getDI()->get('db');
        $whereid =   "{$indexName} = ".$updateArr[$indexName];
        if (isset($updateArr[$indexName])) {
            unset($updateArr[$indexName]);
        }

        try {
            $db->begin();
            $db->updateAsDict($tablename, $updateArr, $whereid);// 警告！在这种情况下，值不会被转义
            $updRes = $db->affectedRows();

            $db->execute($up_firstunsql);
            $db->execute($up_usql);
            $db->execute($up_asql);
            $db->execute($insetSql);
            $db->commit();
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("otherRe:lastApproval:" . $e->getMessage());
            $db->rollback();
            return false;
        }
        return true;
    }


    /**
     * 撤销操作
     * @param $log
     * @param array $updateArr
     * @param string $tablename
     * @return bool
     */
    public function cancelApproval($log, $updateArr = [], $tablename = '', $indexName = 'id')
    {
        $logtype = $log['type'];
        $insetSql = $this->getInsertDbSql('staff_audit_tool_log', $log);

        $up_firstunsql = "update staff_audit_union set status_union = 10{$log['to_status_type']} where origin_id = {$updateArr[$indexName]} and type_union = {$logtype} and status_union IN(101, 102, 106, 107);";
        $up_asql = "update staff_audit_approval set status = {$log['to_status_type']} where audit_id = {$updateArr[$indexName]} and type = {$logtype} and status IN(6, 7);";

        $db = $this->getDI()->get('db');
        $whereid = "{$indexName} = ".$updateArr[$indexName];
        if (isset($updateArr[$indexName])) {
            unset($updateArr[$indexName]);
        }
        try {
            $db->begin();
            $db->updateAsDict($tablename, $updateArr, $whereid);// 警告！在这种情况下，值不会被转义
            if ($db->affectedRows() < 1) {
                $db->rollback();
                return false;
            }

            $db->execute($up_firstunsql);
            $db->execute($up_asql);
            $db->execute($insetSql);
            $db->commit();
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("otherRe:cancelApproval:" . $e->getMessage());
            $db->rollback();
            return false;
        }
        return true;
    }

    /**
     * 创建加班车申请
     * @param array $insert
     * @param string $table
     * @return int
     */
    public function InsertAudit($table, $insert)
    {
        try {
            $insertSql = $this->getInsertDbSql($table, $insert);
            $this->getDI()->get('db')->query($insertSql);
            $last_id = $this->getDI()->get('db')->lastInsertId();
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("OtherRe:Insert-" . $e->getMessage());
            $last_id = '';
        }
        return $last_id;
    }

    /**
     * 验证当前用户是否是在ces培训
     * 12小时内登录过就算
     * @param $userinfo
     * @return array
     */
    public function checkStaffIsCesTraining($userinfo): array
    {
        if(empty($userinfo['id'])){
            return [];
        }
        $sql = "SELECT staff_id FROM ces_login_record where (staff_id = :staff_id or ces_staff_id = :staff_id ) and login_at >= :query_time";
        $bind = [
            'staff_id'  => $userinfo['id'],
            'query_time'     => gmdate("Y-m-d H:i:s",time()-43200),
        ];
        $db = $this->getDI()->get('db_fle')->query($sql,$bind);
        return  $db->fetch(\Phalcon\Db::FETCH_ASSOC)?:[];
    }

}
