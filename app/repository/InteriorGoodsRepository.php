<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Enums\InteriorOrderStatusEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\InteriorStaffEntrySizeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffManageDepartmentModel;
use FlashExpress\bi\App\Models\backyard\InteriorGoodsModel;
use FlashExpress\bi\App\Models\backyard\InteriorGoodsSkuModel;
use FlashExpress\bi\App\Models\backyard\InteriorOrdersGoodsSkuModel;
use FlashExpress\bi\App\Models\backyard\InteriorOrdersModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\InteriorGoodsManageDepartmentPermissionModel;
use FlashExpress\bi\App\Models\backyard\InteriorGoodsStoreCateRelModel;
use FlashExpress\bi\App\Enums\InteriorGoodsEnums;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Server\HrStaffInfoServer;
use FlashExpress\bi\App\Enums\InteriorGoodsStatusEnums;
use FlashExpress\bi\App\Server\SettingEnvServer;

class InteriorGoodsRepository extends BaseRepository {

    public $timezone;
    public $db;


    public function __construct() {
        parent::__construct();
        $this->db = $this->getDI()->get('db');
    }

    /**
     * 员工商城 根据当前用户的部门 查询上级部门所有数据
     * @param int $department_id  网点id
     * @param int  $permission_type  1 限购部门 2 免费部门
     * @return array
     */

    public function getSuperiorDepartmentAll(int $department_id, $permission_type = InteriorGoodsEnums::INTERIOR_GOODS_PURCHASE_TYPE_PURCHASE)
    {
        $department            = SysDepartmentModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => [
                'id' => $department_id,
            ],
            'columns'    => ['id', 'ancestry_v3'],
        ]);
        $superior_goods_id_arr = [];
        if (!empty($department)) {
            $ancestry_v3 = explode('/', $department->ancestry_v3);
            array_pop($ancestry_v3);
            if (!empty($ancestry_v3)) {
                $superior_goods_id_arr = InteriorGoodsManageDepartmentPermissionModel::find([
                    'conditions' => 'department_id in ({department_id:array}) and is_include_sub = :is_include_sub:  and  permission_type = :permission_type:',
                    'bind'       => [
                        'department_id' => array_values($ancestry_v3),
                        'permission_type' => $permission_type,
                        'is_include_sub'  => HrStaffManageDepartmentModel::$is_include_sub_1
                    ],
                    'columns'    => ['goods_id'],
                ])->toArray();
                $superior_goods_id_arr = array_unique(array_column($superior_goods_id_arr, 'goods_id'));
            }
        }
        //当前部门符合的数据
        $current_goods_id_arr = $this->getCurrentDepartmentData($department_id, $permission_type);
        return array_values(array_unique(array_merge($superior_goods_id_arr, $current_goods_id_arr)));
    }


    /**
     * 员工商城 获取当前部门的所属的商品id 集合
     * @param int $department_id 部门id
     * @param $permission_type  1 限购部门 2 免费部门
     * @return array
     */
    public function getCurrentDepartmentData($department_id, $permission_type)
    {
        $current_department_goods_id = InteriorGoodsManageDepartmentPermissionModel::find([
            'conditions' => 'department_id = :department_id: and permission_type = :permission_type:',
            'bind'       => [
                'department_id' => $department_id,
                'permission_type' => $permission_type
            ],
            'columns'    => ['goods_id'],
        ])->toArray();
        return array_unique(array_column($current_department_goods_id, 'goods_id'));
    }


    /**
     * 根据网点类型查找购买权限的商品id集合
     * @param string $storeCateCode 网点类型
     * @param $permission_type  1 限购网点类型 2 免费网点类型
     * @return  array
     **/
    public function getInteriorGoodsStoreCateRelAll(string $storeCateCode, $permission_type = InteriorGoodsEnums::INTERIOR_GOODS_PURCHASE_TYPE_PURCHASE)
    {
        //根据网点类型查找购买权限的商品id集合
        $goods_store_cate_rel_arr = InteriorGoodsStoreCateRelModel::find([
            'conditions' => 'sys_store_cate = :sys_store_cate: and permission_type = :permission_type:',
            'bind'       => ['sys_store_cate' => $storeCateCode, 'permission_type' => $permission_type],
            'columns'    => 'goods_id'
        ])->toArray();
        return array_values(array_unique(array_column($goods_store_cate_rel_arr, 'goods_id')));
    }


    /**
     * 根据网点类型查找购买免费的商品id集合，免费商品必须为在售状态
     * @return  array
     **/
    public function getFreeGoodsForSaleArr()
    {
        //根据网点类型查找购买权限的商品id集合
        $goods_store_cate_rel_obj = $this->modelsManager->createBuilder();
        $goods_store_cate_rel_obj->columns(
            'igscr.store_cate_id, igscr.goods_id, min(igscr.id)'
        );
        $goods_store_cate_rel_obj->from(['igscr' => InteriorGoodsStoreCateRelModel::class]);
        $goods_store_cate_rel_obj->leftJoin(InteriorGoodsModel::class, 'igscr.goods_id = ig.id', 'ig');
        $goods_store_cate_rel_obj->where('ig.status = :status:', ['status' => InteriorGoodsStatusEnums::GOODS_STATUS_ON_SALE]);
        $goods_store_cate_rel_obj->andWhere('igscr.permission_type = :permission_type:', ['permission_type' =>InteriorGoodsEnums::INTERIOR_GOODS_PURCHASE_TYPE_FREE]);

        $goods_store_cate_rel_obj->groupBy('igscr.store_cate_id');
        $data = $goods_store_cate_rel_obj->getQuery()->execute()->toArray();

        return array_column($data, 'goods_id', 'store_cate_id');
    }


    /**
     * 查询限购和免费全部关闭的在售商品id集合
     * @return  array
     **/
    public function getInteriorGoodsFreeByBuyAll()
    {
        //根据网点类型查找购买权限的商品id集合
        $interior_goods_free_by_buy_arr = InteriorGoodsModel::find([
            'conditions' => ' is_limit_buy = :is_limit_buy: and  status = :status:',
            'bind'       => ['is_limit_buy' => 0, 'status' => InteriorGoodsStatusEnums::GOODS_STATUS_ON_SALE],
            'columns'    => 'id'
        ])->toArray();
        return array_column($interior_goods_free_by_buy_arr, 'id');
    }


    /**
     *  判断购买权限和 部门权限是否设置
     *  @param array $userInfo  当前登录用户信息
     * @return  array
     **/
    public function getIsBuyAndDepartmentSet(array $userInfo)
    {
        $free_by_buy_arr = $this->getInteriorGoodsFreeByBuyAll();
        $buy_goods_arr =  $this->getFreeGoodsIdsAll($userInfo,InteriorGoodsEnums::INTERIOR_GOODS_PURCHASE_TYPE_PURCHASE);
        if (empty($buy_goods_arr['msg'])) {
            $data = array_values(array_unique(array_merge($free_by_buy_arr, $buy_goods_arr)));
        } else {
            $data = $free_by_buy_arr;
        }
        return $data;
    }




    /**
     *  校验详情是否显示免费发放
     *  @param array $userInfo  当前登录用户信息
     *  @param int $permission_type 1 限购网点类型 2 免费网点类型
     * @return  array
     **/
    public function getFreeGoodsIdsAll(array $userInfo, int $permission_type = InteriorGoodsEnums::INTERIOR_GOODS_PURCHASE_TYPE_FREE)
    {
        //获取部门全部设置的商品
        $interior_goods_ids = InteriorGoodsManageDepartmentPermissionModel::find([
            'conditions' => 'permission_type = :permission_type:',
            'bind'       => ['permission_type' => $permission_type],
            'columns'    => 'goods_id',
            'group'      => 'goods_id',
        ])->toArray();

        //获取权限全部设置的商品
        $department_goods_ids = InteriorGoodsStoreCateRelModel::find([
            'conditions' => 'permission_type = :permission_type:',
            'bind'       => ['permission_type' => $permission_type],
            'columns'    => 'goods_id',
            'group'      => 'goods_id',
        ])->toArray();

        $department_buy = array_column($interior_goods_ids, 'goods_id');
        $permission_set = array_column($department_goods_ids, 'goods_id');
        //部门设置且权限没有设置
        $department = array_diff($department_buy, $permission_set);
        //权限设置且部门没有设置
        $permission = array_diff($permission_set, $department_buy);
        //权限和 部门都设置的
        $department_and_permission_all = array_intersect($department_buy, $permission_set);

        $storeCateCode = HrStaffInfoServer::getStaffStoreCateCode($userInfo);
        if (isset($storeCateCode['msg'])) {
            return $storeCateCode;
        }
        //当前网点类型下可以显示的商品id
        $goods_ids = $this->getInteriorGoodsStoreCateRelAll($storeCateCode, $permission_type);

        //获取当前购买的部门权限
        $superior_department_goods_ids = $this->getSuperiorDepartmentAll($userInfo['node_department_id'],$permission_type);
        $goods_arr = array_merge(
            array_intersect($department, $superior_department_goods_ids),//得到部门设置且权限没有设置属于部门权限商品
            array_intersect($permission, $goods_ids),//权限设置且部门没有设置属于网点类型权限商品
            array_intersect($department_and_permission_all, array_intersect($superior_department_goods_ids, $goods_ids)));//两个都设置商品在两个都满足的网点类型和部门的数据里面获取交集
        return array_values(array_unique($goods_arr));
    }




    /**
     *  是否在非免费大区里面
     *  @param array $userInfo  当前登录用户信息
     * @return  bool
     **/
    public function isFreeManageRegion(array $userInfo)
    {
        //增加员工所属网点大区判断
        $setting_env = new SettingEnvServer();
        $store_manage_region = $setting_env->getSetVal('shop_free_manage_region_ids');
        $manage_region = false;
        if (!empty($store_manage_region)) {
            $store = (new SysStoreModel())->getOneStoreById($userInfo['sys_store_id']);
            $store_manage_region_arr = explode(',', $store_manage_region);
            if (in_array($store['manage_region'], $store_manage_region_arr)) {
                $manage_region = true;
            }
        }
        return $manage_region;
    }






    /////////////////////////////////////////////////////////

    /**
     * 根据用户的部门 获取对应的映射数据找对应的goods_id
     * @param string $node_department_id 部门
     * @param string $job_title_id 职位id
     * @param string $store_id  网点id
     * @return array
     */
    public function getGoodsId(string $node_department_id, string $job_title_id, string $store_id )
    {
        if (empty($node_department_id) || empty($job_title_id) || empty($store_id)) {
            return [];
        }

        $setting_env_server = new SettingEnvServer();
        $goods_size_all     = [];
        $department_spu_ids = $setting_env_server->getSetVal('auto_interior_orders_department_id_spu_ids');
        $department_spu_ids = json_decode($department_spu_ids, true);
        //部门
        $department_ids = $setting_env_server->getSetVal('auto_interior_orders_department_ids', ',');
        //职位
        $job_title_ids = $setting_env_server->getSetVal('auto_interior_orders_job_ids', ',');
        //免费大区
        $manage_region_ids   = $setting_env_server->getSetVal('shop_free_manage_region_ids', ',');
        //网点
        $store_arr = (new SysStoreModel())->getOneStoreById($store_id);

        $is_size = 0;//无尺码数据 给前端做标识符使用
        if (!empty($department_spu_ids) && in_array($node_department_id, $department_ids) && in_array($job_title_id, $job_title_ids) && !in_array($store_arr['manage_region'], $manage_region_ids) ) {
            $is_size = 1;//有尺码数据 给前端做标识符使用
            $department_spu_arr = array_column($department_spu_ids, 'spu', 'department_id');
            if (in_array($node_department_id, array_keys($department_spu_arr))) {
                $goods_size_all = $this->getGoodsSizeAll($department_spu_arr[$node_department_id]);
            }
        }
        return ['goods_size_all'=> $goods_size_all, 'is_size' => $is_size];
    }


    /**
     * 根据goods_id 返回所有的尺码数据
     * @param integer $goods_id 订单列表
     * @return array
     */
    public function getGoodsSizeAll(int $goods_id)
    {
        if (empty($goods_id)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['igs.goods_id, igs.goods_sku_code as barcode, igs.attr_1 as size,ig.size_img_path']);
        $builder->from(['igs' => InteriorGoodsSkuModel::class]);
        $builder->leftJoin(InteriorGoodsModel::class, 'igs.goods_id = ig.id', 'ig');
        $builder->where('igs.status = :status:', ['status' => InteriorGoodsStatusEnums::GOODS_STATUS_ON_SALE]);
        $builder->andWhere('ig.status = :status:', ['status' => InteriorGoodsStatusEnums::GOODS_STATUS_ON_SALE]);
        $builder->andWhere('igs.goods_id = :goods_id:', ['goods_id' => $goods_id]);
        return $builder->getQuery()->execute()->toArray();
    }


    /**
     * 根据goods_id 返回所有的尺码数据
     * @param integer $staff_id 订单列表
     * @return array
     */
    public function getGoodsSize(int $staff_id)
    {
        if(empty($staff_id)){
            return [];
        }
        $size       = [];
        $entry_size = InteriorStaffEntrySizeModel::findFirst([
            'conditions' => 'staff_id = :staff_id:',
            'bind'       => ['staff_id' => $staff_id]
        ]);
        if (!empty($entry_size)) {
            $size = $entry_size->toArray();
        }
        return $size;
    }

    /**
     * 根据barcode 返回商品对应的数据
     * @param string $barcode barcode
     * @return array
     */
    public function getBarcodeInteriorGoods(string $barcode)
    {
        if (empty($barcode)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['igs.goods_sku_code, igs.attr_1, ig.id as goods_id, ig.goods_name_zh, ig.goods_name_th, ig.goods_name_en, ig.img_path, ig.size_img_path,ig.info']);
        $builder->from(['igs' => InteriorGoodsSkuModel::class]);
        $builder->leftJoin(InteriorGoodsModel::class, 'igs.goods_id = ig.id', 'ig');
        $builder->where('igs.goods_sku_code = :goods_sku_code:', ['goods_sku_code' => $barcode]);
        $data = $builder->getQuery()->execute()->getFirst();
        if (empty($data)) {
            $res = [];
        } else {
            $res = $data->toArray();
        }
        return $res;
    }


    /**
     * 添加入职尺码数据
     * @param array $param 入职数据
     * @param array $user 登录人数据
     * @return array
     */
    public function addEntrySize(array $param, array $user)
    {
        $staff_repository =new StaffRepository();
        $staff_arr          = $staff_repository->getStaffpositionV2($param['staff_id']);
        //菲律宾 && 个人代理 无需记录尺码
        if (isCountry('PH') && $staff_arr['hire_type']  == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            return false;
        }
        $interior_goods_arr = $this->getBarcodeInteriorGoods($param['barcode']);
        $new_time           = date('Y-m-d H:i:s', time());
        if ($staff_arr['organization_type'] == 2) {
            $sys_store_id   = enums::HEAD_OFFICE_ID;
            $sys_store_name = enums::HEAD_OFFICE;
        } else {
            $store_arr      = $staff_repository->getStaffStoreInfo($staff_arr['organization_id']);
            $sys_store_id   = $staff_arr['organization_id'];
            $sys_store_name = $store_arr['name'] ?? '';
        }
        $size_data          = [
            'staff_id'           => $param['staff_id'],
            'entry_time'         => empty($staff_arr['hire_date']) ? '1970-01-01 00:00:00' : date('Y-m-d H:i:s', strtotime($staff_arr['hire_date'])),
            'node_department_id' => $staff_arr['department_id'] ?? '',
            'sys_store_id'       => $sys_store_id,
            'sys_store_name'     => $sys_store_name,
            'goods_id'           => $interior_goods_arr['goods_id'] ?? '',
            'barcode'            => $param['barcode'] ?? '',
            'size'               => $interior_goods_arr['attr_1'] ?? '',
            'goods_name_zh'      => $interior_goods_arr['goods_name_zh'] ?? '',
            'goods_name_th'      => $interior_goods_arr['goods_name_th'] ?? '',
            'goods_name_en'      => $interior_goods_arr['goods_name_en'] ?? '',
            'entry_size'         => $interior_goods_arr['attr_1'] ?? '',
            'entry_goods_id'     => $interior_goods_arr['goods_id'] ?? '',
            'entry_source'       => 1,
            'created_id'         => $user['id'],
            'updated_id'         => $user['id'],
            'created_at'         => $new_time,
            'updated_at'         => $new_time,
        ];
        $this->logger->write_log(['addEntrySize' => json_encode($size_data)], 'info');
        $entry_size_model   = new InteriorStaffEntrySizeModel();
        if ($entry_size_model->create($size_data) === false) {
            $this->getDI()->get('logger')->write_log('员工商城-员工工服尺码-添加商品失败, 原因可能是:' . json_encode($size_data), 'error');
        }
    }


    /**
     * 获取商品购买的总件数
     * @param array $goods_ids 商品id
     * @param array $staff_ids 用户id
     * @return array
     **/
    public function getInteriorOrdersGoodsSkuNum(array $staff_ids, array $goods_ids = [])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('iogs.goods_id, sum(iogs.buy_num) as buy_nums, io.staff_id');
        $builder->from(['iogs' => InteriorOrdersGoodsSkuModel::class]);
        $builder->leftJoin(InteriorOrdersModel::class, 'iogs.order_code = io.order_code', 'io');
        $builder->where('iogs.is_free = :is_free: and  goods_type = :goods_type: ', ['is_free' => 1, 'goods_type' => InteriorGoodsEnums::GOODS_TYPE_WORK_CLOTHES]);
        $builder->andWhere('io.order_status not in ({order_status:array})', ['order_status' => [InteriorOrderStatusEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE, InteriorOrderStatusEnums::ORDER_STATUS_SYSTEM_CANCEL_CODE]]);
        if (!empty($goods_ids)) {
            $builder->inWhere('iogs.goods_id', $goods_ids);
        }
        $builder->inWhere('io.staff_id', $staff_ids);
        $builder->groupBy('io.staff_id, iogs.goods_id');
        $orders_goods_sku_num = $builder->getQuery()->execute()->toArray();
        return array_column($orders_goods_sku_num, null, 'staff_id');
    }


    /**
     * 根据barcode 返回商品对应的数据
     * @param array $barcode 集合
     * @return array
     */

    public function getBarcodeInteriorGoodsAll(array $barcode)
    {
        if (empty($barcode)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['igs.goods_id, igs.goods_sku_code, igs.attr_1 as size,ig.size_img_path, igs.goods_name_zh, igs.goods_name_th, igs.goods_name_en, igs.status, igs.id, ig.goods_type, ig.is_free, igs.surplus_num']);
        $builder->from(['igs' => InteriorGoodsSkuModel::class]);
        $builder->leftJoin(InteriorGoodsModel::class, 'igs.goods_id = ig.id', 'ig');
        $builder->inWhere('igs.goods_sku_code', $barcode);
        $builder->andWhere('igs.status = :status:', ['status' => InteriorGoodsStatusEnums::GOODS_STATUS_ON_SALE]);
        return array_column($builder->getQuery()->execute()->toArray(), null, 'goods_sku_code');
    }

    /**
     * 获取限制购商品id
     * @return array
     */
    public function getLimitGoodsIds()
    {
        $goods_arr = InteriorGoodsModel::find(
            [
                'conditions' => 'is_limit_buy = :is_limit_buy: ',
                'bind'       => ['is_limit_buy' => 1]
            ]
        )->toArray();

        return array_values(array_column($goods_arr, 'id'));
    }

}
