<?php
/**
 * Author: Bruce
 * Date  : 2024-07-06 20:32
 * Description:
 */

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrStaffContractBusinessApplyModel;

class HrStaffContractBusinessApplyRepository extends BaseRepository
{
    public function initialize()
    {
        parent::initialize();
    }

    public static function getOneById($id, $columns = ['*'])
    {
        $data = HrStaffContractBusinessApplyModel::findFirst(
            [
                'columns'    => $columns,
                'conditions' => "id = :id:",
                'bind'       => [
                    'id' => $id,
                ],
            ]
        );

        return empty($data) ? [] : $data->toArray();
    }

    public static function getOneByContractId($params, $columns = ['*'])
    {
        if(empty($params['contract_id'])) {
            return [];
        }

        $conditions = 'contract_id = :contract_id:';
        $bind['contract_id'] = $params['contract_id'];

        if(!empty($params['business_type'])) {
            $conditions .= ' and business_type = :business_type:';
            $bind['business_type'] = $params['business_type'];
        }

        //业务类型 不等于的
        if(!empty($params['business_type_not'])) {
            $conditions .= ' and business_type != :business_type_not:';
            $bind['business_type_not'] = $params['business_type_not'];
        }

        //审批状态
        if(!empty($params['audit_status'])) {
            $conditions .= ' and status in ({status:array})';
            $bind['status'] = $params['audit_status'];
        }

        //审批创建时间
        if(!empty($params['audit_created_at'])) {
            $conditions .= ' and audit_created_at <= :audit_created_at:';
            $bind['audit_created_at'] = $params['audit_created_at'];
        }

        $data = HrStaffContractBusinessApplyModel::findFirst(
            [
                'columns'    => $columns,
                'conditions' => $conditions,
                'bind'       => $bind,
                'order'      => 'id desc'
            ]
        );

        return empty($data) ? [] : $data->toArray();
    }

    public static function getPendingData($params, $columns = ['*'])
    {
        if(empty($params)) {
            return [];
        }

        return HrStaffContractBusinessApplyModel::find(
            [
                'columns'    => $columns,
                'conditions' => "status = :status:",
                'bind'       => [
                    'status' => $params['status'],
                ],
            ]
        )->toArray();
    }
}