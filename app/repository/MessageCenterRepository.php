<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\backyard\MessageModel;
use FlashExpress\bi\App\Models\backyard\QuestionnaireLibModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;

class MessageCenterRepository extends BaseRepository
{
    public $timezone;

    public function __construct($timezone)
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    /**
     * 消息详情
     * @Access  public
     * @param array $paramIn
     * @param string $data_source slave_db: 从库(默认); master_db: 主库（解决创建消息审批流时，bi库的message表主从同步延时问题）
     * @return  array
     */
    public function getMessage(array $paramIn = [], string $data_source = '')
    {
        try {
            $message_id          = $paramIn['id'] ?? 0;
            if (empty($message_id)) {
                return [];
            }

            if ($data_source == 'master_db') {
                $get_sql = "
                    -- 获取消息详情: 消息审批流同步创建
                    SELECT * FROM message WHERE id = :id;
                ";
                $get_param = [
                    'id' => $message_id
                ];

                $return_data = $this->getDI()->get('db')->query($get_sql, $get_param)->fetch(\Phalcon\Db::FETCH_ASSOC);

            } else {
                $return_data = MessageModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind' => ['id' => $message_id],
                ]);

                $return_data = $return_data ? $return_data->toArray() : [];
            }

            return $return_data;

        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('消息审批: getMessage'. $e->getMessage());
        }

        return [];
    }

    /**
     * 更新消息审批终态
     * @Access public
     * @param int $message_id
     * @param int $audit_status
     * @return mixed
     */
    public function updateMessageAuditStatus(int $message_id, int $audit_status)
    {
        try {
            if (empty($message_id) || empty($audit_status)) {
                return false;
            }

            $message_model = MessageModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $message_id]
            ]);

            if (empty($message_model)) {
                return false;
            }

            $message_model->audit_status = $audit_status;
            $message_model->updated_at = $message_model->updated_at;

            if ($message_model->save() === true) {
                $this->getDI()->get('logger')->write_log("updateMessageAuditStatus - [message_id - {$message_id}], 消息审批驳回，终态更新成功", 'info');

                return true;
            }

        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("updateMessageAuditStatus - [message_id - {$message_id}], 消息审批驳回, 终态更新失败: ". $e->getMessage());
        }

        return false;
    }

    /**
     * 获取问卷详情
     * @Access  public
     * @param array $paramIn
     * @return  array
     */
    public function getQuestionnaireDetail(array $paramIn = [])
    {
        try {
            $lib_id          = $paramIn['id'] ?? 0;
            if (empty($lib_id)) {
                return [];
            }

            $return_data = QuestionnaireLibModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $lib_id],
            ]);

            return $return_data ? $return_data->toArray() : [];
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('消息审批: getQuestionnaireDetail'. $e->getMessage());
        }

        return [];
    }


    /**
     * 查询员工消息
     * @param $params
     * @param array $columns
     * @return array
     */
    public static function getMessageCourierInfo($params, $columns = ['*'])
    {
        if(empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind       = [];

        if (!empty($params['id'])) {
            $conditions .= ' and id = :id:';
            $bind['id'] = $params['id'];
        }

        if (!empty($params['staff_info_id'])) {
            $conditions .= ' and staff_info_id = :staff_info_id:';
            $bind['staff_info_id'] = $params['staff_info_id'];
        }

        if (!empty($params['category'])) {
            $conditions .= ' and category = :category:';
            $bind['category'] = $params['category'];
        }

        if (isset($params['read_state']) && in_array($params['read_state'], [MessageCourierModel::READ_STATE_UNREAD, MessageCourierModel::READ_STATE_HAS_READ])) {
            $conditions .= ' and read_state = :read_state:';
            $bind['read_state'] = $params['read_state'];
        }

        $data = MessageCourierModel::findFirst([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);

        return empty($data) ? [] : $data->toArray();
    }

    public static function getCourierOne($params, $columns = ['*'])
    {
        if (empty($params)) {
            return [];
        }

        $conditions = 'is_del = :is_del:';
        $bind['is_del'] = MessageCourierModel::IS_DELETED_NO;

        if (!empty($params['staff_info_id'])) {
            $conditions         .= ' and staff_info_id = :staff_info_id:';
            $bind['staff_info_id'] = $params['staff_info_id'];
        }

        if (isset($params['read_state']) && in_array($params['read_state'], [MessageCourierModel::READ_STATE_HAS_READ, MessageCourierModel::READ_STATE_UNREAD])) {
            $conditions .= ' and read_state = :read_state:';
            $bind['read_state'] = $params['read_state'];
        }

        //是否 子账号可见。
        if (!empty($params['push_state'])) {
            $conditions         .= ' and push_state = :push_state:';
            $bind['push_state'] = $params['push_state'];
        }

        $data = MessageCourierModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => $columns,
        ]);

        return !empty($data) ? $data->toArray() : [];
    }

}
