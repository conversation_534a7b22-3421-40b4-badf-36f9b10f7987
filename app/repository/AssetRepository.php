<?php


namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrAnnexModel;

class AssetRepository extends BaseRepository
{

    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 创建审批人
     * @param array $insert
     * @return array
     */
    public function getAssetIds($paramIn = [])
    {
        $searchWhere = $paramIn['searchWhere'] ?? '';
        $where       =  "";
        if (!empty($searchWhere)) {
            foreach ($searchWhere as $column => $v) {
                switch ($column) {
                    case 'staff_id':
                        $searchCondition[] = " order.staff_info_id = '{$v}'";
                        break;
                    case 'start_date':
                        $searchCondition[] = " order.created_at >= '{$v} 00:00:00'";
                        break;
                    case 'end_date':
                        $searchCondition[] = " order.created_at <= '{$v} 23:59:59'";
                        break;
                    case 'serial_no':
                        $searchCondition[] = " order.order_union_id = '{$v}' ";
                        break;
                    case 'goods_id':
                        $searchCondition[] = " ag.bar_code= '{$v}' ";
                        break;
                    case 'store_id':
                        $searchCondition[] = " order.organization_id = '{$v}' ";
                        break;
                    case 'type':
                        if ($v == enums::$audit_type['AS']) {
                            $searchCondition[] = " order.wf_role like 'as\_%' ";
                        } else if ($v == enums::$audit_type['ASP']) {
                            $searchCondition[] = " order.wf_role like 'asp\_%' ";
                        }
                    default:
                        break;
                }
            }
            if (sizeof($searchCondition) > 1) {
                $where = implode(' AND ', $searchCondition);
            } else {
                $where = current($searchCondition);
            }
            $where = " WHERE " . $where;
        }

        $querySql = "--
            SELECT 
                order.id,
                order.order_union_id as serial_no,
                DATE_FORMAT(CONVERT_TZ(order.created_at,  '+00:00', '{$this->timezone}'),'%Y-%m-%d') created_date,
                order.reject_reason,
                order.wf_role,
                order.staff_info_id as staff_id,
                order.organization_id as store_id,
                order.reason
            FROM
                `assets_order` `order`
                JOIN `assets_order_detail` order_detail ON order.id = order_detail.order_id
                JOIN `assets_goods` ag ON order_detail.goods_id = ag.id
            {$where}
            GROUP BY order.id
            ORDER BY order.id DESC
        ";
        $returnArr = $this->getDI()->get('db')->query($querySql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $returnArr;
    }

    /**
     * 获取资产列表数据
     * @param array $insert
     * @return array
     */
    public function getAssetIdsV2($paramIn = [])
    {
        $searchWhere = $paramIn['searchWhere'] ?? '';
        $pageSize    = $paramIn['page_size'] ?? 20;
        $pageNum     = $paramIn['page_num'] ?? 1;
        $isExistAssetId= isset($paramIn['searchWhere']['goods_id']) && $paramIn['searchWhere']['goods_id'];
        $where       = "";
        $type        = enums::$audit_type['AS'] . "," . enums::$audit_type['ASP'];
        if (!empty($searchWhere)) {
            foreach ($searchWhere as $column => $v) {
                switch ($column) {
                    case 'staff_id':
                        $searchCondition[] = " order.staff_info_id = '{$v}'";
                        break;
                    case 'start_date':
                        $searchCondition[] = " order.created_at >= '{$v} 00:00:00'";
                        break;
                    case 'end_date':
                        $searchCondition[] = " order.created_at <= '{$v} 23:59:59'";
                        break;
                    case 'serial_no':
                        $searchCondition[] = " order.order_union_id = '{$v}' ";
                        break;
                    case 'goods_id':
                        $searchCondition[] = " ag.bar_code= '{$v}' ";
                        break;
                    case 'store_id':
                        $searchCondition[] = " order.organization_id = '{$v}' ";
                        break;
                    case 'type':
                        $new_v=explode('_',$v);
                        $v= str_replace('_','',$v);
                        if ($v == enums::$audit_type['AS']) {
                            $searchCondition[] = " order.wf_role like 'as\_%' ";
                            $type = enums::$audit_type['AS'];
                        } else if ($v == enums::$audit_type['ASP']) {
                            $searchCondition[] = " order.wf_role like 'asp\_%' ";
                            $type = enums::$audit_type['ASP'];
                        } else if($v == enums::$audit_type['AS'].enums::$audit_type['ASP']){
                            $searchCondition[] = " (order.wf_role like 'as\_%' ) ";// or order.wf_role like 'asp\_%'
                            $type = enums::$audit_type['AS'].','.enums::$audit_type['ASP'];
                        }
                        break;
                    case 'staff_approval_id':
                        $searchCondition[] = " aa.approval_id = {$v} ";
                        break;
                    case 'status':
                        $searchCondition[] = " aa.state = {$v} ";
                        break;
                    default:
                        break;
                }
            }
            if (sizeof($searchCondition) > 1) {
                $where = implode(' AND ', $searchCondition);
            } else {
                $where = current($searchCondition);
            }
            $where = " WHERE " . $where;
        }

        //[2]分页计算
        if (!empty($pageNum) && !empty($pageSize)) {
            $pageOffset = ($pageNum - 1) * $pageSize;
        } else {
            $pageOffset = 0;
        }
        $limit = " LIMIT {$pageSize} OFFSET {$pageOffset}";

        if ($isExistAssetId) {
            $querySql = "
                SELECT 
                    order.id,
                    order.order_union_id as serial_no,
                    DATE_FORMAT(CONVERT_TZ(order.created_at,  '+00:00', '{$this->timezone}'),'%Y-%m-%d') created_date,
                    order.reject_reason,
                    order.wf_role,
                    order.staff_info_id as staff_id,
                    order.organization_id as store_id,
                    order.reason
                FROM
                    audit_approval aa
                    JOIN `assets_order` `order` ON order.id = CAST(aa.biz_value AS unsigned) AND aa.biz_type IN ($type)
                    JOIN `assets_order_detail` order_detail ON order.id = order_detail.order_id
                    JOIN `assets_goods` ag ON order_detail.goods_id = ag.id
                {$where}
                GROUP BY aa.biz_value 
                ORDER BY aa.biz_type ASC
            ";//
            $queryCountSql = "
                 SELECT 
                   count(order.id) as num
                FROM
                    audit_approval aa
                    JOIN `assets_order` `order` ON order.id = CAST(aa.biz_value AS unsigned) AND aa.biz_type IN ($type)
                    JOIN `assets_order_detail` order_detail ON order.id = order_detail.order_id
                    JOIN `assets_goods` ag ON order_detail.goods_id = ag.id
                {$where}  GROUP BY aa.biz_value 
            ";
        } else {
            $querySql = "
                SELECT 
                order.id,
                    order.order_union_id as serial_no,
                    DATE_FORMAT(CONVERT_TZ(order.created_at,  '+00:00', '{$this->timezone}'),'%Y-%m-%d') created_date,
                    order.reject_reason,
                    order.wf_role,
                    order.staff_info_id as staff_id,
                    order.organization_id as store_id,
                    order.reason,
                    aa.state as status
                FROM
                    audit_approval aa
                    JOIN `assets_order` `order` ON order.id = CAST(aa.biz_value AS unsigned) AND aa.biz_type IN ($type)
                {$where}
                ORDER BY aa.biz_type ASC
            ";//
            $queryCountSql = "
                SELECT 
                count(order.id) as num
                FROM
                    audit_approval aa
                    JOIN `assets_order` `order` ON order.id = CAST(aa.biz_value AS unsigned) AND aa.biz_type IN ($type)
                {$where}
            ";
        }

        $returnArr= [];
        $totalCount =[];
        if(!empty($paramIn['source'])){
                $totalCountArr = $this->getDI()->get('db_rby')->fetchOne($queryCountSql,\Phalcon\Db::FETCH_ASSOC);
                $totalCount=$totalCountArr['num']??0;
                $this->getDI()->get('logger')->write_log('gaofeng001'.json_encode($totalCountArr),'info');
        }else{
            $returnArr = $this->getDI()->get('db_rby')->query($querySql. $limit)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

            $totalCountArr = $this->getDI()->get('db_rby')->fetchOne($queryCountSql,\Phalcon\Db::FETCH_ASSOC);
            $totalCount=$totalCountArr['num']??0;
        }

        return [$returnArr, $totalCount];
    }

    public function uploadAnnex($paramIn = []){
        $returnData = [];
        $id = $paramIn["id"] ?? 0;
        $fileUrl = $paramIn["file_url"] ?? [];
        if (!$id || !$fileUrl){
            return $returnData;
        }
        $config = $this->getDI()->getConfig();
        // 批量插入
        foreach ($fileUrl as $v) {
            $fileType = isset($v['file_type']) ? $v['file_type'] : 0;
            $model = HrAnnexModel::find([
                'conditions' => 'oss_bucket_key = :oss_bucket_key: and file_type = :file_type: and type = :type:',
                'bind' => [
                    'oss_bucket_key' => $id,
                    'file_type'      => $fileType,
                    'type'           => 4,
                ]
            ]);
            $model->update(['deleted' => CommonEnums::IS_DELETED_YES]);
            //$this->getDI()->get('db')->updateAsDict(
            //    'hr_annex',
            //    [
            //        'deleted' => 1,
            //    ],
            //    'oss_bucket_key = "' . $id . '" and `type`=4 and file_type = '.$fileType
            //);
            $imgInsetData[] = [
                'oss_bucket_key'  => $id,
                'oss_bucket_type' => 'ASSET_ANNEX',
                'bucket_name'     => $config->bucket_name,
                'object_key'      => isset($v['file_path']) ? $v['file_path'] : '',
                'original_name'   => isset($v['file_name']) ? $v['file_name'] : '',
                'file_size'       => isset($v['file_size']) ? $v['file_size'] : '',
                'file_type'       => $fileType,
                'type'            => 4,
            ];
        }
        foreach ($imgInsetData as $k => $v) {
            $imgData = $this->getDI()->get('db')->insertAsDict(
                'hr_annex', $v
            );
            if ($imgData) {
                $annexId      = $this->getDI()->get('db')->lastInsertId();
                $v['id']      = $annexId;
                $returnData[] = $v;
            }
        }
        return $returnData;
    }

    public function getAnnexList($paramIn = []){
        $id = $paramIn["id"] ?? 0;
        $fileType = $paramIn["file_type"] ?? 0;
        if (!$id){
            return [];
        }
        $sql = "--
                SELECT
                    * 
                FROM
                    hr_annex 
                WHERE
                    type = 4 
                    AND deleted = 0 
                    AND oss_bucket_key = {$id}";
        if ($fileType){
            $sql .= " AND file_type = {$fileType}";
        }
        $returnArr = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $returnArr;
    }

    public function getAssetsHandoer($paramIn = []){
        $staffId = $paramIn["staff_id"] ?? 0;
        $state = $paramIn["state"] ?? 0;
        if (!$staffId){
            return [];
        }
        $sql = "--
                SELECT
                    * 
                FROM
                    assets_handover 
                WHERE
                    staff_id = {$staffId}";
        if ($state){
            $sql .= " AND state = {$state}";
        }
        $returnArr = $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $returnArr;
    }

    public function addAssetsHandoer($paramIn = []){
        $staffId = $paramIn["staff_id"] ?? 0;
        $sql = "--
                select * from assets_handover where staff_id = {$staffId}";
        $data = $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        $param = [
            "staff_id" => $staffId,
            "state" => 2,
        ];
        if ($data){
            $this->getDI()->get('db')->updateAsDict(
                'assets_handover',
                $param,
                'staff_id = '.$staffId
            );
        }else{
            $this->getDI()->get('db')->insertAsDict(
                'assets_handover', $param
            );
        }
        $id      = $this->getDI()->get('db')->lastInsertId();
        return $id;
    }
}


