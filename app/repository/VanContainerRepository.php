<?php
/**
 * Author: Bruce
 * Date  : 2025-05-21 21:08
 * Description:
 */

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\VanContainerModel;

class VanContainerRepository extends BaseRepository
{
    /**
     * 查询停职申请
     * @param $params
     * @param $columns
     * @return array
     */
    public static function getOne($params, $columns = ['*'])
    {
        if (empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind       = [];

        if (!empty($params['staff_info_id'])) {
            $conditions .= ' and  staff_info_id = :staff_info_id:';
            $bind['staff_info_id'] = $params['staff_info_id'];
        }

        $data = VanContainerModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => $columns,
            'order'      => 'id desc',
        ]);

        return !empty($data) ? $data->toArray() : [];
    }
}