<?php
/**
 * Author: Bruce
 * Date  : 2023-12-05 10:41
 * Description:
 */

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\SysStoreModel;

class SysStoreRepository extends BaseRepository
{
    public function initialize()
    {
        parent::initialize();
    }

    public static function getStoreListById(array $storeIds,$columns = '*')
    {
        if (empty($storeIds)) {
            return [];
        }
        return SysStoreModel::find([
            'columns'    => $columns,
            'conditions' => "id in ({store_id:array})",
            'bind'       => [
                'store_id' => $storeIds,
            ],
        ])->toArray();
    }


    public static function getStoreName($storeId)
    {
        if (empty($storeId)) {
            return '';
        }
        $storeInfo = SysStoreModel::findFirst([
            'columns'    => 'name',
            'conditions' => "id = :store_id:",
            'bind'       => [
                'store_id' => $storeId,
            ],
        ]);

        return $storeInfo->name ?? '';
    }

    /**
     * 获取网点信息
     * @param $storeId
     * @param string $columns
     * @return array
     */
    public static function getSysStoreInfo($storeId, $columns = '*')
    {
        $storeInfo = SysStoreModel::findFirst([
            'columns'    => $columns,
            'conditions' => "id = :store_id:",
            'bind'       => [
                'store_id' => $storeId,
            ],
        ]);

        return !empty($storeInfo) ? $storeInfo->toArray() : [];
    }

    /**
     * 获取根据网点负责人查询负责网点信息
     * @param $staffInfoId
     * @param string $columns
     * @return array
     */
    public static function getManageStoreList($staffInfoId, $columns = '*')
    {
        return SysStoreModel::find([
            'columns'    => $columns,
            'conditions' => "manager_id = :manager_id:",
            'bind'       => [
                'manager_id' => $staffInfoId,
            ],
        ])->toArray();

    }
}