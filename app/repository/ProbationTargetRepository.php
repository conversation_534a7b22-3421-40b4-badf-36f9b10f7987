<?php
namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrProbationTargetBusinessModel;
use FlashExpress\bi\App\Models\backyard\HrProbationTargetDetailModel;
use FlashExpress\bi\App\Models\backyard\HrProbationTargetMessageModel;
use FlashExpress\bi\App\Models\backyard\HrProbationTargetModel;

class ProbationTargetRepository extends BaseRepository
{
    /**
     * 获取目标详情
     * @param $targetId
     * @return array
     */
    public function getTargetInfoByTargetId($targetId): array
    {
        if (!$targetId) {
            return [];
        }

        $info = HrProbationTargetModel::findFirst(
            [
                'conditions' => 'id = :target_id: AND is_deleted = :is_deleted:',
                'bind'       => [
                    'target_id'  => $targetId,
                    'is_deleted' => enums::IS_DELETED_NO,
                ],
            ]
        );

        return $info ? $info->toArray() : [];
    }


    /**
     * 获取目标流程详情数据
     * @param $params
     * @param $columns
     * @return array
     */
    public function getTargetBusinessInfo($params, $columns = '*'): array
    {
        if (empty($params)) {
            return [];
        }

        $conditions = '';
        $bind       = [];

        if (!empty($params['id'])) {
            $conditions        .= ' id = :id: AND';
            $bind['id'] = $params['id'];
        }

        if (!empty($params['staff_info_id'])) {
            $conditions        .= ' staff_info_id = :staff_info_id: AND';
            $bind['staff_info_id'] = $params['staff_info_id'];
        }

        if (!empty($params['state'])) {
            $conditions    .= '  state = :state: AND';
            $bind['state'] = $params['state'];
        }

        $conditions = rtrim(trim($conditions),'AND');

        if (!$conditions) {
            return [];
        }

        $info = HrProbationTargetBusinessModel::findFirst(
            [
                'columns' => $columns,
                'conditions' => $conditions,
                'bind'       => $bind,
                'order'      => 'id desc',
            ]
        );

        return $info ? $info->toArray() : [];
    }

    /**
     * 获取发送的消息记录
     * @param $params
     * @param $columns
     * @return array
     */
    public function getTargetMessageInfo($params, $columns = '*'): array
    {
        if (empty($params)) {
            return [];
        }

        $conditions = '';
        $bind       = [];

        if (!empty($params['target_business_id'])) {
            $conditions        .= ' target_business_id = :target_business_id: AND';
            $bind['target_business_id'] = $params['target_business_id'];
        }

        if (!empty($params['staff_info_id'])) {
            $conditions        .= ' staff_info_id = :staff_info_id: AND';
            $bind['staff_info_id'] = $params['staff_info_id'];
        }

        if (!empty($params['type'])) {
            $conditions    .= '  type = :type: AND';
            $bind['type'] = $params['type'];
        }

        if (!empty($params['state'])) {
            $conditions    .= '  state = :state: AND';
            $bind['state'] = $params['state'];
        }

        $conditions = rtrim(trim($conditions),'AND');

        if (!$conditions) {
            return [];
        }

        $info = HrProbationTargetMessageModel::findFirst(
            [
                'columns' => $columns,
                'conditions' => $conditions,
                'bind'       => $bind,
                'order'      => 'id desc',
            ]
        );

        return $info ? $info->toArray() : [];
    }

    /**
     * 保存目标
     * @param $params
     * @return false|int
     */
    public function saveTargetBusiness($params)
    {
        $db         = $this->getDI()->get("db");
        $tableName  = (new HrProbationTargetBusinessModel())->getSource();
        $businessId = $params['id'] ?? 0;

        if ($businessId) {
            unset($params['id']);

            $res = $db->updateAsDict(
                $tableName,
                $params,
                ["conditions" => 'id=?', 'bind' => [$businessId]]
            );

            if (!$res) {
                return false;
            }
        } else {
            $res = $db->insertAsDict($tableName, $params);
            if (!$res) {
                return false;
            }
            $businessId = $db->lastInsertId();
        }

        return $businessId;
    }

    /**
     * 保存目标
     * @param $params
     * @return false|int
     */
    public function saveTargetMessage($params)
    {
        $db         = $this->getDI()->get("db");
        $tableName  = (new HrProbationTargetMessageModel())->getSource();
        $businessId = $params['id'] ?? 0;

        if ($businessId) {
            unset($params['id']);

            $res = $db->updateAsDict(
                $tableName,
                $params,
                ["conditions" => 'id=?', 'bind' => [$businessId]]
            );

            if (!$res) {
                return false;
            }
        } else {
            $res = $db->insertAsDict($tableName, $params);
            if (!$res) {
                return false;
            }
            $businessId = $db->lastInsertId();
        }

        return $businessId;
    }

    /**
     * 保存目标
     * @param $params
     * @return false|int
     */
    public function saveTarget($params)
    {
        $db        = $this->getDI()->get("db");
        $tableName = (new HrProbationTargetModel())->getSource();
        $targetId  = $params['id'] ?? 0;

        if ($targetId) {
            unset($params['id']);

            $res = $db->updateAsDict(
                $tableName,
                $params,
                ["conditions" => 'id=?', 'bind' => [$targetId]]
            );

            if (!$res) {
                return false;
            }
        } else {
            $res = $db->insertAsDict($tableName, $params);
            if (!$res) {
                return false;
            }
            $targetId = $db->lastInsertId();
        }

        return $targetId;
    }

    /**
     * 保存目标详情
     * @param $params
     * @return false|int
     */
    public function saveTargetDetail($params)
    {
        $db        = $this->getDI()->get("db");
        $tableName = (new HrProbationTargetDetailModel())->getSource();
        $detailId  = $params['id'] ?? 0;

        if (!empty($params['id'])) {
            unset($params['id']);
            $res = $db->updateAsDict(
                $tableName,
                $params,
                [
                    "conditions" => 'id=?',
                    'bind'       => [$detailId],
                ]
            );

            if (!$res) {
                return false;
            }
        } else {
            $res = $db->insertAsDict($tableName, $params);
            if (!$res) {
                return false;
            }
            $detailId = $db->lastInsertId();
        }

        return $detailId;
    }
}