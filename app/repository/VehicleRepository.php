<?php

namespace FlashExpress\bi\App\Repository;


class VehicleRepository extends BaseRepository
{
    public $timezone;
    private $str;

    public function __construct($lang = 'zh-CN',$timezone)
    {
        parent::__construct($lang);
        $this->timezone =  $timezone;
        $this->staff = new StaffRepository();
    }

    /**
     * 创建
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addVehicleR($data)
    {
        $insetData = $data;
        $db = $this->getDI()->get('db');
        // 插入
        try {
            $db->begin();
            // 动态生成SQL语句(另一种语法)
            $success = $db->insertAsDict('vehicle_mileage', $insetData);
            if (!$success) {
                $db->rollback();
                return false;
            }
            $vehicleId = $db->lastInsertId();
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->write_log("[vehicle_mileage insert error]" . $e->getMessage());
            return false;
        }
        return $vehicleId;
    }

    /**
     * 车辆记录
     * @Return  array
     */
    public function getVehicleR($id)
    {
        $sql = " SELECT id,mileage_date,start_kilometres,end_kilometres,started_path,end_path,started_img,end_img,started_bucket,end_bucket,serial_no,
        status,is_push,apply_user,approve_user,CONVERT_TZ( created_at, '+00:00', '".$this->timezone."' ) AS created_at,
CONVERT_TZ( updated_at, '+00:00', '".$this->timezone."' ) AS updated_at  FROM vehicle_mileage where id = {$id}  ;";
        return $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
    }

    /**
     * 车辆里程记录
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getMileageR($mileagedate , $uid)
    {
        $sql = " SELECT * FROM staff_mileage_record where staff_info_id = {$uid}  and  mileage_date = '{$mileagedate}'  ;";
        $info_data = $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $info_data ? $info_data : (object)[];
    }

    /**
     * 车辆里程图片记录
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getMileageImgR($mileageId)
    {
        $sql = " SELECT * FROM sys_attachment where  oss_bucket_key = '{$mileageId}'  ;";
        $info_data = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $info_data ? $info_data : [];
    }

    /**
     * 插入或更新车辆里程
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function staffMileageRecordR($result)
    {
        $mileage_date = $result['mileage_date'];
        $uid = $result['apply_user'];
        $res = $this->dayMileageCountR($mileage_date , $uid); //里程表查询
        $db = $this->getDI()->get('db');
        //如果存在更新，如果不存在更新某一次里程
        $r = 0;
        $staff_mileage_record_data['create_channel'] = $create_channel  = 1;
        $staff_mileage_record_data['input_by'] = $input_by  = 'system';
        $staff_mileage_record_data['input_state'] = $input_state   = 2;
        if(empty($res)){
            $staff_mileage_record_data['start_kilometres'] = $result['start_kilometres'];
            $staff_mileage_record_data['end_kilometres'] = $result['end_kilometres'];
            $staff_mileage_record_data['staff_info_id'] = $result['apply_user'];
            $staff_mileage_record_data['mileage_date'] = $result['mileage_date'];
            $staff_store = $this->staff->checkoutStaff($result['apply_user']);//通过用户id拿到网点信息
            $staff_mileage_record_data['store_id'] = isset($staff_store['organization_id']) ? $staff_store['organization_id'] : 0;//网点编号

            $storePcode = $this->staff->getStaffStoreInfo($staff_mileage_record_data['store_id'] );//网点id拿到网点信息中的省一级
            $staff_province = $this->staff->getProvince($storePcode['province_code']); //通过省拿到大区
            $staff_mileage_record_data['sorting_no'] = isset($staff_province['sorting_no']) ? $staff_province['sorting_no'] : '=';//分拣区编号

            $insetSql = $this->getInsertDbSql('staff_mileage_record', $staff_mileage_record_data);
            $r = $db->execute($insetSql);
            $mileageId = $db->lastInsertId();
            $startimgRes = $this->addAttachmentR($mileageId , $result['started_bucket'], $result['started_path'] );
            $endimgRes = $this->addAttachmentR($mileageId , $result['end_bucket'] , $result['end_path']);
        }else{
            $setsql = "";
            //判断如果下班里程为空，更新下班里程
            if(empty($res['end_kilometres'])){
                $setsql = " end_kilometres = '{$result['end_kilometres']}' ";
                $bucket_name = $result['end_bucket'];
                $object_key  = $result['end_path'];
            }else if(empty($res['start_kilometres'])){
                $setsql = " start_kilometres = '{$result['start_kilometres']}'  ";
                $bucket_name = $result['started_bucket'];
                $object_key  = $result['started_path'];
            }
            $mileageId = isset($res['id']) ? $res['id'] : 0;
            if($setsql){
                $setsql .= " , create_channel = '{$create_channel}',  input_by = '{$input_by}' , input_state=  '{$input_state}' ";
                $up_sql = "update staff_mileage_record set  ". $setsql ." where staff_info_id = '{$uid}' and mileage_date = '{$mileage_date}' ;";
                $r = $db->execute($up_sql);
                $this->addAttachmentR($mileageId , $bucket_name , $object_key );
            }
        }
        return $mileageId;
    }


    /**
     * 创建里程图片
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addAttachmentR( $mileageId, $bucket_name, $object_key )
    {
        $insetData['id'] = md5(time().random_int(1000,9999));
        $insetData['oss_bucket_type'] = "STAFF_MILEAGE_WORK_RECORD";
        $insetData['oss_bucket_key'] = $mileageId;//外键
        $insetData['bucket_name'] = $bucket_name;
        $insetData['object_key'] = $object_key;
        $insetData['original_name'] = "补里程图片";
        $db = $this->getDI()->get('db');
        // 插入
        try {
            $db->begin();
            // 动态生成SQL语句(另一种语法)
            $success = $db->insertAsDict('sys_attachment', $insetData);
            if (!$success) {
                $db->rollback();
                return false;
            }
            $lastId = $db->lastInsertId();
            $db->commit();
        } catch (\Exception $e) {
            echo $e->getMessage();
            $db->rollback();
            return false;
        }
        return $lastId;
    }

    /**
     * 车辆里程补卡申请记录次数
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getMileageCountR( $uid , $mileage_date = '')
    {
        $addsql = empty($mileage_date) ? " " : " AND `mileage_date` >= '".getCurMonthFirstDay($mileage_date)."'   AND  `mileage_date` <= '".getCurMonthLastDay($mileage_date)."' " ;
        $sql = " SELECT count(*) as record_count FROM vehicle_mileage where status in (1,2) AND apply_user = {$uid}  ".$addsql." ;";
        $info_data = $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $info_data;
    }
    /**
     * 车辆里程单条记录次数
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function dayMileageCountR( $mileagedate = '', $uid )
    {
        $sql = " SELECT * FROM staff_mileage_record where staff_info_id = {$uid}  and  mileage_date = '{$mileagedate}'  ;";
        $info_data = $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $info_data;
    }
    /**
     * @param taff_audit_tool_log  日志表组装数据
     * @param vehicle_mileage更改状态 模型
     */
    public function cancel($log, $vehicle_mileage)
    {
        $insetSql = $this->getInsertDbSql('staff_audit_tool_log', $log);
        $up_sql = "update vehicle_mileage set status = {$vehicle_mileage['status']} ,reject_reason = '{$vehicle_mileage['reject_reason']}' , approve_user = {$vehicle_mileage['uid']} where id = '{$vehicle_mileage['id']}' ";
        $db = $this->getDI()->get('db');
        try {
            $db->begin();
            $db->execute($insetSql);
            $db->execute($up_sql);
            $db->commit();
        } catch (\Exception $e) {
            $ControllerBase = new \FlashExpress\bi\App\Controllers\ControllerBase;
            $ControllerBase->wLog('记录失败日志', $log);
            $db->rollback();
            return false;
        }
        return true;
    }

    /**
     * @param taff_audit_tool_log  日志表组装数据
     */
    public function insertLog($vehicleArr = [], $uinfo = [])
    {
        $log['staff_id'] = $vehicleArr['apply_user'];     //申请人
        $log['type'] = 11;
        $log['original_type'] = $vehicleArr['status'];     //原始状态
        $log['to_status_type'] = $vehicleArr['status'];     //修改后状态
        $log['original_id'] = $vehicleArr['id'];     //关联id
        $log['operator'] = $uinfo['id'];     //操作人
        $log['operator_name'] = $uinfo['name'];     //操作人名称
        $insetSql = $this->getInsertDbSql('staff_audit_tool_log', $log);
        $db = $this->getDI()->get('db');
        $res = $db->execute($insetSql);
        return $res;
    }


    /**
     * 获取类型
     * @return array
     */
    public function getOilType()
    {
        $data = [
            '1'  => $this->getTranslation()->_('7141'),
            '2'  => $this->getTranslation()->_('7142'),
            '3'  => $this->getTranslation()->_('7143'),
            '4'  => $this->getTranslation()->_('7144'),
        ];
        return $data;
    }

    /**
     * 创建
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addVehicleInfoR($data)
    {
        if (isset($data['plate_number']) && !empty($data['plate_number'])){
            $data['plate_number'] = nameSpecialCharsReplace($data['plate_number']);
        }
        $insetData = $data;
        // 插入
        try {
            $db = $this->getDI()->get('db');
            $db->begin();
            // 动态生成SQL语句(另一种语法)
            $success = $db->insertAsDict('vehicle_info', $insetData);
            if (!$success) {
                $db->rollback();
                return false;
            }
            $vehicleId = $db->lastInsertId();
            $db->commit();
        } catch (\Exception $e) {
            echo $e->getMessage();
            $db->rollback();
            return false;
        }
        return $vehicleId;
    }
    /**
     * 更新
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function updateVehicleInfo($data)
    {
        if (isset($data['plate_number']) && !empty($data['plate_number'])){
            $data['plate_number'] = nameSpecialCharsReplace($data['plate_number']);
        }

        $insetData = $data;
        $db = $this->getDI()->get('db');
        // 插入
        try {
            $db->begin();
            $id = $insetData['uid'];
            unset($insetData['uid']);
            $success = $db->updateAsDict('vehicle_info', $insetData, [
                'conditions' => "uid = $id",
            ]);
            if (!$success) {
                $db->rollback();
                return false;
            }
            $db->commit();
            return true;
        } catch (\Exception $e) {
            echo $e->getMessage();
            $db->rollback();
            return false;
        }
    }

    /**
     * 车辆信息记录
     * @Access  public
     * @Param   request
     * @return mixed
     */
    public function getVehicleInfoR($uid)
    {
        $sql = " SELECT * FROM vehicle_info where uid = {$uid}";
        $info_data = $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $info_data;
    }
    /**
     * 插入或更新车辆里程(bi使用)
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function updateStaffMileageR($paramIn)
    {
        $mileage_date = $paramIn['mileage_date'];
        $uid = $paramIn['staff_info_id'];
        $res = $this->dayMileageCountR($mileage_date , $uid); //里程表查询
        $db = $this->getDI()->get('db');
        //如果存在更新，如果不存在更新某一次里程
        $r = 0;
        $staff_mileage_record_data['create_channel'] = $create_channel  = 2;
        if(empty($res)){
            $staff_mileage_record_data['start_kilometres'] = $paramIn['start_kilometres'];
            $staff_mileage_record_data['end_kilometres'] = $paramIn['end_kilometres'];
            $staff_mileage_record_data['staff_info_id'] = $paramIn['staff_info_id'];
            $staff_mileage_record_data['mileage_date'] = $paramIn['mileage_date'];
            $staff_store = $this->staff->checkoutStaff($paramIn['staff_info_id']);//通过用户id拿到网点信息
            $staff_mileage_record_data['store_id'] = isset($staff_store['organization_id']) ? $staff_store['organization_id'] : 0;//网点编号
            $storePcode = $this->staff->getStaffStoreInfo($staff_mileage_record_data['store_id'] );//网点id拿到网点信息中的省一级
            $staff_province = $this->staff->getProvince($storePcode['province_code']); //通过省拿到大区
            $staff_mileage_record_data['sorting_no'] = isset($staff_province['sorting_no']) ? $staff_province['sorting_no'] : '=';//分拣区编号
            $insetSql = $this->getInsertDbSql('staff_mileage_record', $staff_mileage_record_data);
            $r = $db->execute($insetSql);
            $mileageId = $db->lastInsertId();
        }else if( empty($res['prepaid_slip_no'] )){
            $setsql = " start_kilometres = '{$paramIn['start_kilometres']}' ,end_kilometres = '{$paramIn['end_kilometres']}'  ";
            $mileageId = isset($res['id']) ? $res['id'] : 0;
            if($setsql){
                $setsql .= " , create_channel = '{$create_channel}'";
                $up_sql = "update staff_mileage_record set  ". $setsql ." where staff_info_id = '{$uid}' and mileage_date = '{$mileage_date}' ;";
                $r = $db->execute($up_sql);
            }
        }else{ //获取里程信息,当日里程是否结算
            return 0 ;
        }
        return $mileageId;
    }

    /**
     * 创建
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addVehicleInfoLog($data)
    {
        $insetData = $data;
        // 插入
        $this->str = 'vehicle_info_log';
        try {
            $db = $this->getDI()->get('db');
            // 动态生成SQL语句(另一种语法)
            $success = $db->insertAsDict('' . $this->str . '', $insetData);
            if (!$success) {
                $db->rollback();
                return false;
            }
            $vehicleId = $db->lastInsertId();
        } catch (\Exception $e) {
            echo $e->getMessage();
            return false;
        }
        return $vehicleId;
    }
     /** 车辆信息日志记录
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getVehicleInfoLog($staff_id)
    {
        try{
            $sql = " SELECT staff_id FROM vehicle_info_log where uid = {$staff_id}  ;";
            $info_data = $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
            return $info_data;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log($e->getMessage(),'error');
            return [];
        }
    }


    /**
     * 车辆里程补卡申请记录次数【Van快递员新增“修改里程表数”使用】
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getMileageModifyCountR( $uid , $mileage_date = '')
    {
        $addsql = empty($mileage_date) ? " " : " AND `mileage_date` >= '".getCurMonthFirstDay($mileage_date)."'   AND  `mileage_date` <= '".getCurMonthLastDay($mileage_date)."' " ;
        $sql = " SELECT count(*) as record_count FROM approve_modify_mileage where   staff_info_id = {$uid}  ".$addsql." ;";
        $info_data = $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $info_data;
    }
    /**
     * 创建【Van快递员新增“修改里程表数”使用】
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addMileageModifyR($data)
    {
        $insetData = $data;
        $db = $this->getDI()->get('db');
        // 插入
        try {
            $db->begin();
            // 动态生成SQL语句(另一种语法)
            $success = $db->insertAsDict('approve_modify_mileage', $insetData);
            if (!$success) {
                $db->rollback();
                return false;
            }
            $recordId = $db->lastInsertId();
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            return false;
        }
        return $recordId;
    }

    /**
     * 车辆里程补卡申请记录详情【Van快递员新增“修改里程表数”使用】
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getMileageModifyDetailR( $uid , $mileage_date = '')
    {
        $addsql = empty($mileage_date) ? " "  :  " AND `mileage_date` = '".$mileage_date."' " ;
        $sql = " SELECT * FROM approve_modify_mileage where   staff_info_id = {$uid}  ".$addsql." ;";
        $info_data = $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $info_data;
    }


}
