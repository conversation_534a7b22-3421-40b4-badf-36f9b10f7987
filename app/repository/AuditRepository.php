<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\BusinessTripModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditLeaveSplitModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\HrStaffTransferModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Server\AssetServer;
use FlashExpress\bi\App\Server\Penalty\AttendancePenaltyServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use Phalcon\DiInterface;

class AuditRepository extends BaseRepository
{

    public function initialize()
    {
        parent::initialize();
    }


    public function __construct($lang = 'zh-CN')
    {
        parent::__construct($lang);
    }



    /**
     * @var array
     * 1:年假 2003 2:带薪事假2004 3:病假 2005 4:产假 2006 5:陪产假 2007 6:国家军训假 2008
     * 7:家人去世假 2009 8:Leave for sterilization 2010 9:受训假 2011 10:婚假 2012
     * 11:出家假 2013 12:不带薪事假 2014 13:调休 2015 14:其他 2016  15:休息日 2017  16 公司培训假 2018
     * 20 单亲育儿假 21 女性特殊假 22家庭暴力假 23紧急假 24 无薪休假 25 隔离假 26 带薪病假（新冠治疗）

     *
     * '2003'             => '年假',
     * '2004'             => '带薪事假',
     * '2005'             => '病假',
     * '2006'             => '产假',
     * '2007'             => '陪产假',
     * '2008'             => '国家军训假',
     * '2009'             => '家人去世假',
     * '2010'             => 'Leave for sterilization',
     * '2011'             => '个人受训假',
     * '2012'             => '婚假',
     * '2013'             => '出家假',
     * '2014'             => '不带薪事假',
     * '2015'             => '调休',
     * '2016'             => '其他',
     * '2017'             => '休息日',
     * '2018'             => '公司培训假',
     * '2020'             => '陪产假',
     */

    public static $leave_type = array(
        enums::LEAVE_TYPE_1  => '2003',//年假
        enums::LEAVE_TYPE_2  => '2004',//带薪事假
        enums::LEAVE_TYPE_3  => '2005',//带薪病假
        enums::LEAVE_TYPE_4 => '2006',//产假
        enums::LEAVE_TYPE_5 => '2007',//陪产假
        enums::LEAVE_TYPE_6 => '2008',//国家军训假
        enums::LEAVE_TYPE_7 => '2009',//家人去世假
        enums::LEAVE_TYPE_8 => '2010',//绝育手术假
        enums::LEAVE_TYPE_9 => '2011',//个人受训假
        enums::LEAVE_TYPE_10 => '2012',//婚假
        enums::LEAVE_TYPE_11 => '2013',//出家假
        enums::LEAVE_TYPE_12 => '2014',//不带薪事假
        enums::LEAVE_TYPE_13 => '2015',//调休 （已废弃）
        enums::LEAVE_TYPE_14 => '2016',//其他 （废弃）
        enums::LEAVE_TYPE_15 => '2017',//休息日
        enums::LEAVE_TYPE_16 => '2018',//公司培训假
        enums::LEAVE_TYPE_17 => '2020',//产检
        enums::LEAVE_TYPE_18 => '2021',//无薪病假
        enums::LEAVE_TYPE_19 => '2022',//跨国探亲假
        enums::LEAVE_TYPE_20 => 'leave_20',//单亲育儿假
        enums::LEAVE_TYPE_21 => 'leave_21',//女性特殊假
        enums::LEAVE_TYPE_22 => 'leave_22',//家庭暴力假
        enums::LEAVE_TYPE_23 => 'leave_23',//紧急假
        enums::LEAVE_TYPE_24 => 'leave_24',//无薪休假 老挝新增的 产品说跟不带薪事假不一样 也不知道哪不一样
        enums::LEAVE_TYPE_25 => 'leave_25',//隔离假
        enums::LEAVE_TYPE_26 => 'leave_26',//新冠治疗
        enums::LEAVE_TYPE_27 => 'leave_27',//住院假
        enums::LEAVE_TYPE_28 => 'leave_28',//体恤假
        enums::LEAVE_TYPE_29 => 'leave_29',//考试假
        enums::LEAVE_TYPE_30 => 'leave_30',//孩子结婚假
        enums::LEAVE_TYPE_31 => 'leave_31',//31丧家 leave_31
        enums::LEAVE_TYPE_32 => 'leave_32',//32流产假 leave_32
        enums::LEAVE_TYPE_33 => 'leave_33',//33其他家人丧家 leave_33
        enums::LEAVE_TYPE_34 => 'leave_34',//34儿子割礼 leave_34
        enums::LEAVE_TYPE_35 => 'leave_35',//35儿童洗礼 leave_35
        enums::LEAVE_TYPE_36 => 'leave_36',//36朝觐假 leave_36
        enums::LEAVE_TYPE_37 => 'leave_37',//37 灾难假
        enums::LEAVE_TYPE_38 => '2005',//38 病假主类型 泰国新增 作为 带薪和不带薪病假的前置类型
        enums::LEAVE_TYPE_39 => 'leave_39',//39 针对无底薪员工新增请假类型
        enums::LEAVE_TYPE_40 => 'leave_40',//40 针对无底薪员工新增请假类型 泰国
        enums::LEAVE_TYPE_41 => 'leave_41',//41 针对无底薪员工新增请假类型 菲律宾
        enums::LEAVE_TYPE_42 => 'leave_42',//42 马来 国民假

    );


    //获取 基础信息表 假期类型天数
    public function get_leave_days_by_type($leave_type, $job_id = 0)
    {
        if (empty($leave_type))
            return false;
        $sql = " select days from staff_days_for_leave where leave_type = {$leave_type} ";
        //只有年假  才会带职位 其他没有
        if (!empty($job_id) && $leave_type == 1)
            $sql .= " and job_title = {$job_id}";

        $day = $this->getDI()->get('db')->fetchColumn($sql);
        return $day;

    }

    //获取所有类型假期 的额度 不包括年假 年假特殊
    public function get_all_leave_days()
    {
        $sql        = "select leave_type, days from staff_days_for_leave where leave_type != 1 ";
        $data       = $this->getDI()->get('db')->query($sql);
        $data       = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $returnData = array();
        if (!empty($data)) {
            $returnData = array_column($data, 'days', 'leave_type');
        }
        return $returnData;

    }

    //根据入职时间 和职位 判断 员工 当年 应有年假 年假 根据职位 分7天 9天 12天 每个月份入职 假期
    //https://shimo.im/sheets/94eQG9bXB8oMZlnT/EI6r3
    public static $years_holiday_left = array(
        7  => array(
            1  => 7,
            2  => 6.5,
            3  => 6,
            4  => 5,
            5  => 4.5,
            6  => 4,
            7  => 3.5,
            8  => 3,
            9  => 2.5,
            10 => 2,
            11 => 1,
            12 => 0.5,
        ),
        9  => array(
            1  => 9,
            2  => 8,
            3  => 7.5,
            4  => 7,
            5  => 6,
            6  => 5,
            7  => 4.5,
            8  => 4,
            9  => 3,
            10 => 2.5,
            11 => 1.5,
            12 => 1,
        ),
        12 => array(
            1  => 12,
            2  => 11,
            3  => 10,
            4  => 9,
            5  => 8,
            6  => 7,
            7  => 6,
            8  => 5,
            9  => 4,
            10 => 3,
            11 => 2,
            12 => 1,
        ),
    );


    public function leave_days_by_grade($grade = 0){
        if(empty($grade) || $grade <= 14)
            return 7;
        if($grade > 14 && $grade <= 16)
            return 9;
        if($grade >= 17)
            return 12;

        return 7;
    }

    public function max_leave_days_by_grade($grade = 0){
        if(empty($grade) || $grade < 19)
            return 15;
        else
            return 17;
    }


    /**
     * 获取 补卡/请假类型
     * @Access  public
     * @Param   request
     * @Return  array
     * type 1 带薪 2 不带薪
     *
     * 1:年假 2:带薪事假 3:病假 4:产假 5:陪产假 6:国家军训假
     * 7:家人去世假 8:Leave for sterilization 9:个人受训假 10:婚假
     * 11:出家假 12:不带薪事假 13:调休 14:其他 15:休息日 16：公司培训假 17：陪产假
     */
    public function getTypeBook_all()
    {

        $audit_server = $this->class_factory('AuditServer');
        $data = $audit_server->type_book_history();

        return $data;
    }

    /**
     * 查询时间内是否有补卡记录
     * @Access  public
     * @Param   $paramIn Array
     * @Return  array 员工补卡记录
     */
    public function getAttendanceCardData($paramIn = [])
    {
        $staffId        = $paramIn['staff_id'];
        $attendanceType = $paramIn['attendance_type'];
        $day            = $paramIn['date_at'];
        $punchCardSql   = "SELECT * FROM `staff_audit` 
                           WHERE `staff_info_id` =" . $staffId . " 
                           AND `attendance_date`='" . $day . "'
                           AND `attendance_type`=" . $attendanceType . " AND `status` in(1,2)";
        $data           = $this->getDI()->get('db')->query($punchCardSql);
        $returnData     = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }


    /**
     * @param array $insetData
     * @param array $staffData
     * @param array $insert
     * @return bool
     */
    public function auditInsert($insetData = [], $staffData = [], $insert = array())
    {
        $insetSql = $this->getInsertDbSql('staff_audit', $insetData);

        $db = $this->getDI()->get('db');
        $db->begin();
        $data = $db->execute($insetSql);
        if (!$data) {
            $db->rollback();
            return false;
        }
        $auditId = $db->lastInsertId();
        //如果是请假新增记录 需要插入拆分表数据 staff_audit_leave_split
        if (!empty($insert)) {
            foreach ($insert as &$v) {
                $v['audit_id'] = $auditId;
            }
            $this->batch_insert('staff_audit_leave_split', $insert);
        }

        $db->commit();

        return $auditId;
    }

    /**
     * 图片插入
     * @Access  public
     * @Param   $insetData Array 数据录入
     * @Return  array 员工补卡记录
     */
    public function auditImgInsert($insetData = [])
    {
        $insetSql = $this->getInsertDbSql('staff_audit_images', $insetData);
        $data     = $this->getDI()->get('db')->query($insetSql);
        return $data;
    }


    /**
     * @param array $paramIn
     * @param mixed $status
     * @return mixed
     */
    public function getLeaveData($paramIn ,$status = null)
    {
        //默认取 1，2
        $conditionsStatus = array(enums::APPROVAL_STATUS_PENDING,enums::APPROVAL_STATUS_APPROVAL);
        //指定单个类型获取数据
        if(!empty($status) && !is_array($status)){
            $conditionsStatus = array($status);
        }
        //获取指定多个状态数据
        if(is_array($status)){
            $conditionsStatus = $status;
        }
        $staffId        = $paramIn['staff_id'];
        $leaveStartTime = $paramIn['leave_start_time'];
        $leaveEndTime   = $paramIn['leave_end_time'];

        //查询 周期内 离职的员工
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('s.date_at,sum(s.type) as sum_type,group_concat(a.leave_type) as type_str');
        $builder->from(['s' => StaffAuditLeaveSplitModel::class]);
        $builder->leftJoin(StaffAuditModel::class, 'a.audit_id = s.audit_id', 'a');
        $builder->andWhere("s.staff_info_id = :staff_id:",['staff_id' => $staffId]);
        $builder->betweenWhere('s.date_at', $leaveStartTime, $leaveEndTime);
        $builder->inWhere('a.status',$conditionsStatus);
        $builder->andWhere("a.parent_id = 0");//排除子记录
        $builder->groupBy('s.date_at');
//        var_dump($builder->getQuery()->getSql()) ;exit;
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * @param $auditId
     * @return array
     */
    public function getLeaveSplitData($auditId)
    {
        $data = StaffAuditLeaveSplitModel::find(
            [
                'conditions' => "audit_id=:audit_id:",
                'bind' => ['audit_id'=> $auditId],
                'columns' => ['date_at'],
                'order' => 'date_at asc',
            ]
        )->toArray();

        return $data;
    }
    /**
     *
     * 根据主键获取记录
     * @param $audit_id
     * @param $audit_type   审核类型 1补卡 2请假 3申请LH费
     * @return mixed
     *
     */
    public function getInfoById($audit_id)
    {
        $sql        = "select * from staff_audit where audit_id = {$audit_id} ";
        $data       = $this->getDI()->get('db')->query($sql);
        $returnData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /**
     *
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getAttendanceCarMonthData($paramIn = [])
    {
        $month        = date('Y-m', strtotime($paramIn['reissue_card_date']));
        $staffId      = $paramIn['staff_id'];
        $punchCardSql = "SELECT count(audit_id) as count FROM `staff_audit` WHERE 
                         `staff_info_id` =" . $staffId . "  and `audit_type` = 1 
                         AND date_format(attendance_date, '%Y-%m') = '" . $month . "' AND status in(1,2)";
        $data         = $this->getDI()->get('db')->query($punchCardSql);
        $returnData   = $data->fetch(\Phalcon\Db::FETCH_ASSOC);

        if(isCountry('TH') && $month == '2023-04'){
            //临时增加补卡机会
            if (in_array($staffId,[634965  ,83695  ,75515  ,43072])) {
                $returnData['count'] = max($returnData['count'] - 1, 0);
            }
        }



        return $returnData['count'];

    }

    //根据工号日期 查询对应月 补卡次数 超过3次不能补卡
    public function getReissueTimes($staffId,$date){
        $start = date('Y-m-01',strtotime($date));
        $end = date('Y-m-d',strtotime("{$date} last day of"));//每月最后一天
        return StaffAuditModel::count([
            'conditions' => 'staff_info_id = :staff_id: and attendance_date between :start: and :end: and audit_type = 1 and status in (1,2)',
            'bind' => [
                'staff_id' => $staffId,
                'start' => $start,
                'end' => $end,
            ]
        ]);
    }

    /**
     * 获取审批单详情
     * @Access  public
     * @Param   $paramIn
     * @Return  array
     */
    public function auditDetail($paramIn = [], $type = 2)
    {
        $auditId      = $paramIn['audit_id'];
        $staffId      = isset($paramIn['staff_id']) && !empty($paramIn['staff_id']) ? $paramIn['staff_id'] : 0;
        $punchCardSql = "
                         --
                         SELECT 
                            `audit_id`,
                            `serial_no`,
                            `staff_info_id`,
                            `manager_id`,
                            `audit_type`,
                            `attendance_type`,
                            `workflow_role`,
                            `leave_type`,
                            `attendance_date`,
                            `reissue_card_date`,
                            `leave_start_time`,
                            `leave_start_type`,
                            `leave_end_time`,
                            `leave_end_type`,
                            `leave_day`,
                            sub_status,
                            `status`,
                            `approver_id`,
                            `approver_name`,
                            `lh_date`,
                            `lh_time`,
                            `lh_plate_number`,
                            `audit_reason`,
                            `reject_reason`,
                            `template_comment`,
                            other_content,
                            paid_leave_reason,
                            source_type,
                            CONVERT_TZ( created_at, '+00:00', '{$this->timezone}' ) AS created_at,
                            CONVERT_TZ( updated_at, '+00:00', '{$this->timezone}' ) AS updated_at 
                        FROM `staff_audit` WHERE `audit_id` =" . $auditId;
        $data         = $this->getDI()->get('db')->query($punchCardSql);
        $returnData   = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        if (!empty($returnData) && $type == 1) {
            if ($returnData['staff_info_id']) {
                $sql            = 'select * from hr_staff_items where item = "MANGER" and staff_info_id = ' . $returnData['staff_info_id'];
                $data           = $this->getDI()->get('db_rby')->query($sql);
                $staffItemsData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
                if ($staffItemsData && isset($staffItemsData['value']) && $staffItemsData['value']) {
                    $returnData['approver_id']   = $staffItemsData['value'];
                    $staffInfoSql                = "SELECT * FROM `staff_info` WHERE `id` =" . $staffItemsData['value'];
                    $data                        = $this->getDI()->get('db_fle')->query($staffInfoSql);
                    $staffInfoData               = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
                    $returnData['approver_name'] = $staffInfoData['name'];
                }
            }

            if ($returnData['audit_type'] == 2) {
                $returnData['leave_start_time'] = date('Y-m-d', strtotime($returnData['leave_start_time']));
                $returnData['leave_end_time']   = date('Y-m-d', strtotime($returnData['leave_end_time']));
                $returnData['leave_type_text']  = $this->typeName($returnData['audit_type'])[$returnData['leave_type']] ?? $returnData['leave_type'];

                //获取所属网点信息：大区名称-片区名称-网点名称
                $returnData['cs_department'] = $this->getSysStoreRelationInfo($returnData['staff_info_id']);
            } else {
                $returnData['leave_type_text'] = $this->typeName($returnData['audit_type'])[$returnData['attendance_type']];
                $returnData['attendance_date'] = date('Y-m-d H:i', strtotime($returnData['attendance_date']));
            }
            $returnData['img_path'] = [];
            $auditImgSql            = "SELECT * FROM `staff_audit_images` 
                                                     WHERE `audit_id` =" . $returnData['audit_id'];
            $imgDataObj             = $this->getDI()->get('db')->query($auditImgSql);
            $imgData                = $imgDataObj->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if (!empty($imgData)) {
                $returnData['img_path'] = $imgData;
            }
            $staffData                = $this->checkoutStaff($returnData['staff_info_id']);
            $returnData['staff_name'] = '';
            if (!empty($staffData)) {
                $returnData['staff_name'] = $staffData['name'];
            }
            //查询网点
            $returnData = array_merge($returnData, $this->getPositionData($returnData['staff_info_id'], 'staff_'), $this->getPositionData($returnData['approver_id'], 'approver_'));

            //查询是否是审核
            $returnData['is_audit'] = 4;
            if ($staffId) {
                //查询是否有审核权限
                $staffAllData = (new \FlashExpress\bi\App\Server\StaffServer)->getHigherStaffId($returnData['staff_info_id']);
                if (!empty($staffAllData)) {
                    if ($staffAllData['value'] == $staffId) {
                        $returnData['is_audit'] = 1;
                    }
                } else {
                    if ($returnData['status'] == 1) {
                        if ($staffId != $returnData['staff_info_id']) {
                            $returnData['is_audit'] = 3;
                        } else {
                            $returnData['is_audit'] = 2;
                        }
                    }
                }
            }
        }
        return $returnData;
    }

    /**
     * 请假申请获取申请人所属网点相关信息
     * @Access public
     * @param  int $staffInfoId  员工id
     * @return string
     */
    public function getSysStoreRelationInfo(int $staffInfoId)
    {
        if(!$staffInfoId)
        {
            return '--';
        }
        $builder = $this->modelsManager->createBuilder();
        //查询系统网点信息
        $storeInfo = $builder->columns('hsi.sys_store_id, ss.name, manage_region, manage_piece')
            ->from(['hsi' => HrStaffInfoModel::class])
            ->leftJoin(SysStoreModel::class, 'ss.id = hsi.sys_store_id', 'ss')
            ->where('hsi.staff_info_id = :staff_id:', ['staff_id' => $staffInfoId])
            ->getQuery()->getSingleResult();

        if(!$storeInfo) {
            return '--';
        }
        $storeInfo = $storeInfo->toArray();

        if($storeInfo['sys_store_id'] == -1) {
            return enums::HEAD_OFFICE;
        }

        $res = '';
        //获取管理大区
        if(isset($storeInfo['manage_region']) && $storeInfo['manage_region']) {
             $regionInfo = SysManageRegionModel::findFirst([
                'columns' => 'name',
                'conditions' => 'id = :manage_region_id:',
                'bind' => ['manage_region_id' => $storeInfo['manage_region']]
             ]);
             if($regionInfo){
                 $res = $regionInfo->name;
             }
        }
        //获取管理片区
        if(isset($storeInfo['manage_piece']) && $storeInfo['manage_piece']) {
            $pieceInfo = SysManagePieceModel::findFirst([
                'columns' => 'name',
                'conditions' => 'id = :manage_piece_id:',
                'bind' => ['manage_piece_id' => $storeInfo['manage_piece']]
            ]);
            if($pieceInfo){
                $res = empty($res) ? $pieceInfo->name : $res . '/' . $pieceInfo->name;
            }
        }
        return empty($res) ? $storeInfo['name'] : $res . '<br />' . $storeInfo['name'];
    }

    /**
     * 员工校验
     * @Access  public
     * @Param   $staffId 员工ID
     * @Return  array 员工信息
     */
    public function checkoutStaff($staffId = '')
    {
        $returnData = [];
        if (!empty($staffId)) {
            $staffInfoSql  = "SELECT * FROM `staff_info` WHERE `id` =" . $staffId;
            $data          = $this->getDI()->get('db_fle')->query($staffInfoSql);
            $staffInfoData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
            $returnData    = $staffInfoData;
        }
        return $returnData;
    }

    /**
     * 修改审批状态
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function auditEditStatus($paramIn = [])
    {
        //[1]参数定义
        $status       = $paramIn['status'];
        $auditId      = $paramIn['audit_id'];
        $rejectReason = $paramIn['reject_reason'];
        $approverId   = $paramIn['approver_id'];
        $approverName = $paramIn['approver_name'];

        //[2]修改语句拼装
        $updataData = [
        	'status'=>$status,
	        'approver_id'=>$approverId,
	        'approver_name'=>$approverName,
        ];
	    if ($status == 3) {
		    $updataData['reject_reason'] = $rejectReason;
	    }
	    $returnData = $this->getDI()->get('db')->updateAsDict(
		    'staff_audit',
		    $updataData,
	    		    [
	    			    'conditions' => 'audit_id = ?',
	    			    'bind' => [$auditId],
	    		    ]
	    );
        return $returnData;
    }

    /**
     * 补卡回调
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function editWorkAttendance($paramIn = [])
    {
        //[1]参数定义
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        $attendanceType = $paramIn['attendance_type'];
        $attendanceDate = date('Y-m-d H:i:s', strtotime($paramIn['reissue_card_date']) - $add_hour * 3600);
        $day            = $paramIn['attendance_date'];
        $staffId        = $paramIn['staff_info_id'];

        //[2]查询此员工当天是否有打卡记录
        $staffInfoAttendanceData = StaffWorkAttendanceModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and attendance_date = :date:',
            'bind' => ['staff_id' => $staffId,'date' => $day]
        ]);

        [$organization_id,$organization_type] = $this->getOrganization($staffId,$day);

        $started_at = $end_at = NULL;
        $flag = false;//是否推送处罚
        if (empty($staffInfoAttendanceData)) {
            $liveFlag = false;
            //主播职位
            $transfer = HrStaffTransferModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id: and stat_date = :date_at:',
                'bind' => [
                    'staff_id' => $staffId,
                    'date_at' => $day,
                ]
            ]);
            $liveJobId = (new SettingEnvServer())->getSetVal('free_shift_position');
            $liveJobId = empty($liveJobId) ? [] : explode(',', $liveJobId);
            if(!empty($transfer) && in_array($transfer->job_title,$liveJobId)){
                $liveFlag = true;
            }

            $flag = true;
            $insertData = [
                'staff_info_id'   => $staffId,
                'organization_id' => $organization_id,
                'organization_type' => $organization_type,
                'attendance_date' => $day,
                'shift_start'     => $liveFlag ? '' : $paramIn['shift_start'],
                'shift_end'       => $liveFlag ? '' : $paramIn['shift_end'],
                'shift_id'        => $liveFlag ? 0 : $paramIn['shift_id'] ?? 0,
            ];
            if ($attendanceType == 1) {
                $insertData['started_state'] = 3;
                $insertData['started_at']    = $attendanceDate;
                $started_at = $attendanceDate;
            } else {
                $insertData['end_state'] = 3;
                $insertData['end_at']    = $attendanceDate;
                $end_at = $attendanceDate;
            }
            //work day
            $staffRe = new StaffRepository($this->lang);
            $staffInfo = $staffRe->getStaffPosition($staffId);
            $insertData['working_day'] = $staffRe->get_is_working_day($staffInfo,$day);

            $insetSql   = $this->getInsertDbSql('staff_work_attendance', $insertData);
            $log_sql    = $insetSql;
            $this->getDI()->get('logger')->write_log("editWorkAttendance {$log_sql}", 'info');
            $returnData = $this->getDI()->get('db')->query($insetSql);
        } else {
            //新增逻辑 如果有打卡记录 上班取最早 下班取最晚 https://flashexpress.feishu.cn/docx/LOindv49toEYBCxUrpecamUfnGQ
            $newTime = strtotime($attendanceDate);
            $oldTime = strtotime($staffInfoAttendanceData->started_at);
            //上班补卡
            if($attendanceType == StaffWorkAttendanceModel::ATTENDANCE_ON){
                $staffInfoAttendanceData->started_state = StaffWorkAttendanceModel::STATE_MAKE_UP_CARD;
                if (empty($staffInfoAttendanceData->started_at) || (!empty($staffInfoAttendanceData->started_at) && $newTime < $oldTime)) {
                    $staffInfoAttendanceData->started_at = $attendanceDate;
                    $started_at = $attendanceDate;
                    $flag = true;
                }
            }

            //下班补卡
            $oldTime = strtotime($staffInfoAttendanceData->end_at);
            if($attendanceType == StaffWorkAttendanceModel::ATTENDANCE_OFF){
                $staffInfoAttendanceData->end_state = StaffWorkAttendanceModel::STATE_MAKE_UP_CARD;
                if(empty($staffInfoAttendanceData->end_at) || (!empty($staffInfoAttendanceData->end_at) && $newTime > $oldTime)){
                    $staffInfoAttendanceData->end_at = $attendanceDate;
                    $end_at = $attendanceDate;
                    $flag = true;
                }
            }
            $returnData = $staffInfoAttendanceData->update();
        }

        $attendancePenaltyServer = new AttendancePenaltyServer($this->lang, $this->timezone);
        $cardData['staff_id']        = $staffId;
        $cardData['attendance_date'] = $day;
        $cardData['shift_type']      = empty($staffInfoAttendanceData) ? StaffWorkAttendanceModel::SHIFT_TYPE_ONLY : $staffInfoAttendanceData->shift_type;
        $cardData['started_at']      = $started_at;
        $cardData['end_at']          = $end_at;
        //向FBI 生产 补卡消息-处罚
        if($flag){
            $attendancePenaltyServer->sendCardReplacementMsg($cardData);
            $this->getDI()->get('logger')->write_log("StaffAuditToolLog editWorkAttendance-" . json_encode($paramIn), 'info');
        }
        return $returnData;
    }


    //获取 员工的 组织信息
    public function getOrganization($staffId,$date){
        //补卡标记 所属网点 查询 transfer表
        $organization_id = $organization_type = null;
        if($date >= date('Y-m-d')){//今天之后的 查 hr staff info
            $staffInfo = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id:',
                'bind' => [
                    'staff_id' => $staffId,
                ]
            ]);
            if(!empty($staffInfo)){
                $organization_id = ($staffInfo->sys_store_id == -1) ? $staffInfo->sys_department_id : $staffInfo->sys_store_id;
                $organization_type = ($staffInfo->sys_store_id == -1) ? HrStaffInfoModel::ORGANIZATION_DEPARTMENT : HrStaffInfoModel::ORGANIZATION_STORE;
            }

        }else{//过去的时间 查transfer
            $staffInfo = HrStaffTransferModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id: and stat_date = :date:',
                'bind' => [
                    'staff_id' => $staffId,
                    'date' => $date
                ]
            ]);

            if(!empty($staffInfo)){
                $organization_id = ($staffInfo->store_id == -1) ? $staffInfo->sys_department_id : $staffInfo->store_id;
                $organization_type = ($staffInfo->store_id == -1) ? HrStaffInfoModel::ORGANIZATION_DEPARTMENT : HrStaffInfoModel::ORGANIZATION_STORE;
            }
        }
        return [$organization_id,$organization_type];
    }

    /**
     * 统计待审核的数量
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function waitAuditNum($paramIn = [])
    {
        //[1]参数定义
        $waitNum = 0;
        $staffId = $paramIn['staff_id'];

        //[2]数据查询
        $auditStaffId  = $staffId;
        $auditStaffSql = "select `staff_info_id` from `hr_staff_items` where `value`= '{$auditStaffId}' AND `item`='MANGER'";
        $data          = $this->getDI()->get('db_rby')->query($auditStaffSql);
        $staffAllData  = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (!empty($staffAllData)) {
            $staffIds  = implode(',', array_unique(array_column($staffAllData, 'staff_info_id')));
            $auditSql  = "SELECT count(audit_id) as count FROM `staff_audit` WHERE `staff_info_id` in (" . $staffIds . ") 
            AND status=1";
            $data      = $this->getDI()->get('db')->query($auditSql);
            $auditData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
            $waitNum   = isset($auditData['count']) && !empty($auditData['count']) ? $auditData['count'] : 0;
        }

        return $waitNum;
    }

    /**
     * 获取类型名称 type 1 上班打卡 下班打卡  2 请假类型
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function typeName($type = 1)
    {
        if ($type == 1) {
            $data = [
                '1' => $this->getTranslation()->_('2001'),
                '2' => $this->getTranslation()->_('2002'),
                '3' => $this->getTranslation()->_('reissue_second_in'),
                '4' => $this->getTranslation()->_('reissue_second_out'),
            ];
        } else {
            $res  = $this->getTypeBook_all();
            $data = array_column($res, 'msg', 'code');

//            $data = [
//                '1'  => $this->getTranslation()->_('2003'),
//                '2'  => $this->getTranslation()->_('2004'),
//                '3'  => $this->getTranslation()->_('2005'),
//                '4'  => $this->getTranslation()->_('2006'),
//                '5'  => $this->getTranslation()->_('2007'),
//                '6'  => $this->getTranslation()->_('2008'),
//                '7'  => $this->getTranslation()->_('2009'),
//                '8'  => $this->getTranslation()->_('2010'),
//                '9'  => $this->getTranslation()->_('2011'),
//                '10' => $this->getTranslation()->_('2012'),
//                '11' => $this->getTranslation()->_('2013'),
//                '12' => $this->getTranslation()->_('2014'),
//                '13' => $this->getTranslation()->_('2015'),
//                '14' => $this->getTranslation()->_('2016'),
//                '15' => $this->getTranslation()->_('2017'),
//                '16' => $this->getTranslation()->_('2018'),
//                '18' => $this->getTranslation()->_('2021'),
//            ];
        }
        return $data;
    }
    //个人代理的补卡类型 不考虑双班次
    public function reissueTypeUnpaid(){
        $data = [
            '1' => $this->getTranslation()->_('up_unpaid'),
            '2' => $this->getTranslation()->_('down_unpaid'),
        ];
        return $data;
    }
    public function reissueTypeUnpaidPh(){
        $data = [
            '1' => $this->getTranslation()->_('up_unpaid_ph'),
            '2' => $this->getTranslation()->_('down_unpaid_ph'),
        ];
        return $data;
    }

    /**
     * 查询请假
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getLevelData($paramIn = [], $type = 1)
    {
        $staffId = $paramIn['staff_id'];
        $day     = $paramIn['day'];

        if (empty($day)) {
            return [];
        }

        $levelSql = "
            --
            SELECT
                leave_type 
                ,(CASE
                WHEN  date_format(leave_start_time, '%Y-%m-%d') = '{$day}'  
                     AND date_format(leave_end_time, '%Y-%m-%d') <> '{$day}'  
                     AND leave_start_type = 1 THEN  3
                WHEN  date_format(leave_start_time, '%Y-%m-%d')='{$day}'  
                     AND date_format(leave_end_time, '%Y-%m-%d') <> '{$day}'  
                     AND leave_start_type = 2 THEN  2
                WHEN  date_format(leave_start_time, '%Y-%m-%d') <> '{$day}'  
                     AND date_format(leave_end_time, '%Y-%m-%d') <> '{$day}'  
                    THEN  3
                WHEN  date_format(leave_start_time, '%Y-%m-%d') <> '{$day}'  
                     AND date_format(leave_end_time, '%Y-%m-%d') = '{$day}'  
                     AND leave_end_type = 1 THEN  1
                WHEN  date_format(leave_start_time, '%Y-%m-%d') <> '{$day}'  
                     AND date_format(leave_end_time, '%Y-%m-%d') = '{$day}'  
                     AND leave_end_type = 2 THEN  3 
                WHEN  date_format(leave_start_time, '%Y-%m-%d') = '{$day}'  
                    AND date_format(leave_end_time, '%Y-%m-%d') = '{$day}'  
                    AND leave_end_type = 1 THEN  1 
                WHEN  date_format(leave_start_time, '%Y-%m-%d') = '{$day}'  
                    AND date_format(leave_end_time, '%Y-%m-%d') = '{$day}'  
                    AND leave_start_type = 2 THEN  2 
                WHEN  date_format(leave_start_time, '%Y-%m-%d') = '{$day}' 
                    AND date_format(leave_end_time, '%Y-%m-%d') = '{$day}'  
                    AND  leave_start_type = 1 AND leave_end_type = 2 THEN  3 
                   END) as level_state
            FROM `staff_audit` 
            WHERE `staff_info_id` = {$staffId}
                AND audit_type = 2
                AND leave_start_time <= '" . $day . ' 09:00:00' . "' AND leave_end_time >= '" . $day . ' 12:00:00' . "'
                AND `status` = 2";
        $levelObj = $this->getDI()->get('db')->query($levelSql);
        if ($type == 2) {
            $levelData = $levelObj->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        } else {
            $levelData = $levelObj->fetch(\Phalcon\Db::FETCH_ASSOC);
        }

        return $levelData;
    }

    /**
     *
     * 获取某一天 待审核 或者一审核 的考勤补卡 记录
     * @param $staff_id
     * @param $date
     * @param $type
     * @return mixed
     */
    public function get_attendance_info($staff_id, $date, $type)
    {
        $sql       = "select * from staff_audit where staff_info_id= {$staff_id} and audit_type = 1 and attendance_date = '{$date}' and  attendance_type = {$type} and status in (1,2)";
        $data      = $this->getDI()->get('db')->query($sql);
        $auditData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $auditData;

    }

    /**
     * 查询LH申请记录
     * @Access  public
     * @Param   $paramIn Array
     * @Return  array
     */
    public function getLhData($paramIn = [])
    {
        $lhDate     = $paramIn['lh_date'];
        $staffId    = $paramIn['staff_id'];
        $lhSql      = "SELECT * FROM `staff_audit` 
                           WHERE `staff_info_id` =" . $staffId . " 
                           AND `lh_date`='" . $lhDate . "'
                           AND `audit_type`= 3 AND `status` in(1,2)";
        $data       = $this->getDI()->get('db')->query($lhSql);
        $returnData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /**
     * 查询部门网点职位
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getPositionData($staffId = '', $flag = 'staff_')
    {
        $returnData = [
            $flag . 'department_name' => '',
            $flag . 'position_name'   => '',
            $flag . 'store_name'      => '',
        ];
        if (!empty($staffId)) {
            //查询部门与查询职位
            $staffInfoSql = "
                              --
                              SELECT hr_job_title.job_name,hr_staff_info.sys_department_id
                              FROM `hr_staff_info`
                              LEFT JOIN hr_job_title ON hr_staff_info.job_title=hr_job_title.id
                              WHERE hr_staff_info.`staff_info_id` =" . $staffId;

            $data           = $this->getDI()->get('db_rby')->query($staffInfoSql);
            $staffInfoData  = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
            $departmentSql  = "select id,name from sys_department";
            $dataObj        = $this->getDI()->get('db_fle')->query($departmentSql);
            $departmentData = $dataObj->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $departmentData = array_column($departmentData, NULL, 'id');
            if (!empty($staffInfoData)) {
                $returnData[$flag . 'position_name']   = $staffInfoData['job_name'];
                $returnData[$flag . 'department_name'] = isset($departmentData[$staffInfoData['sys_department_id']]['name']) ? $departmentData[$staffInfoData['sys_department_id']]['name'] : '';
            }

            //查询网店
            $staffInfoSql  = "
                              --
                              SELECT staff_info.organization_id,sys_store.name  as store_name FROM `staff_info`
                              LEFT JOIN sys_store ON staff_info.organization_id=sys_store.id
                              WHERE staff_info.`id` =" . $staffId . ' AND organization_type=1';
            $data          = $this->getDI()->get('db_fle')->query($staffInfoSql);
            $staffInfoData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
            if (!empty($staffInfoData)) {
                $returnData[$flag . 'store_name'] = $staffInfoData['store_name'];
            }
            $this->getDI()->get('logger')->write_log("AuditRe:getPositionData-" . json_encode($returnData), 'info');
        }
        return $returnData;
    }

    /**
     * 请假回写打卡表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function editWorkByLevel($paramIn = [])
    {
        //[1]参数定义
        $levelDay            = $paramIn['levelDay'];
        $attendanceStartDate = $paramIn['levelDay'] . ' 9:00:00';
        $attendanceEndDate   = $paramIn['levelDay'] . ' 18:00:00';
        $levelTypeDay        = $paramIn['level_type_day'];
        $staffId             = $paramIn['staff_info_id'];
        $type                = isset($paramIn['type']) ? $paramIn['type'] : 1;

        //[2]查询此员工当天是否有打卡记录
        $selectSql               = "select * from staff_work_attendance where staff_info_id=" . $staffId . " 
                                    AND attendance_date='" . $levelDay . "'";
        $data                    = $this->getDI()->get('db')->query($selectSql);
        $staffInfoAttendanceData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        if (empty($staffInfoAttendanceData)) {
            $insertData = [
                'staff_info_id'   => $staffId,
                'attendance_date' => $levelDay
            ];
            if ($levelTypeDay == 1) {
                $insertData['started_state'] = 4;
                $insertData['started_at']    = $attendanceStartDate;
            } elseif ($levelTypeDay == 2) {
                $insertData['end_state'] = 4;
                $insertData['end_at']    = $attendanceEndDate;
            } else {
                $insertData['started_state'] = 4;
                $insertData['started_at']    = $attendanceStartDate;
                $insertData['end_state']     = 4;
                $insertData['end_at']        = $attendanceEndDate;
            }
            $insetSql   = $this->getInsertDbSql('staff_work_attendance', $insertData);
            $returnData = $this->getDI()->get('db')->query($insetSql);
        } else {
            $updateSql = "update staff_work_attendance set ";
            if ($levelTypeDay == 1) {
                $updateSql .= "started_state=4,started_at='" . $attendanceStartDate . "'";
            } else {
                if ($levelTypeDay == 2) {
                    $updateSql .= "end_state=4,end_at='" . $attendanceEndDate . "'";
                } else {
                    $updateSql .= "started_state=4,end_state=4";
                    if ($type != 3) {
                        $updateSql .= ",started_at='" . $attendanceStartDate . "',end_at='" . $attendanceEndDate . "'";
                    }
                }
            }
            $updateSql  .= " WHERE staff_info_id=" . $staffId . " 
                    AND attendance_date='" . $levelDay . "'";
            $returnData = $this->getDI()->get('db')->query($updateSql);
        }
        return $returnData;
    }

    /**
     * 根据日期查询请假
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function selectLevelByDate($day, $staffId)
    {
        $punchCardSql = "
                        --
                        SELECT *,
                        (CASE 
                           -- 开始时间等于的当天
                           WHEN  date_format(leave_start_time, '%Y-%m-%d') = '" . $day . "'  
                                 AND date_format(leave_end_time, '%Y-%m-%d') <> '" . $day . "'  
                                 AND leave_start_type = 1 THEN  3
                           WHEN  date_format(leave_start_time, '%Y-%m-%d')='" . $day . "'  
                                 AND date_format(leave_end_time, '%Y-%m-%d') <> '" . $day . "'  
                                 AND leave_start_type = 2 THEN  2
                           WHEN  date_format(leave_start_time, '%Y-%m-%d') <> '" . $day . "'  
                                 AND date_format(leave_end_time, '%Y-%m-%d') <> '" . $day . "'  
                                THEN  3
                           WHEN  date_format(leave_start_time, '%Y-%m-%d') <> '" . $day . "'  
                                 AND date_format(leave_end_time, '%Y-%m-%d') = '" . $day . "'  
                                 AND leave_end_type = 1 THEN  1
                           WHEN  date_format(leave_start_time, '%Y-%m-%d') <> '" . $day . "'  
                                 AND date_format(leave_end_time, '%Y-%m-%d') = '" . $day . "'  
                                 AND leave_end_type = 2 THEN  3 
                            WHEN  date_format(leave_start_time, '%Y-%m-%d') = '" . $day . "'  
                                AND date_format(leave_end_time, '%Y-%m-%d') = '" . $day . "'  
                                AND leave_end_type = 1 THEN  1 
                            WHEN  date_format(leave_start_time, '%Y-%m-%d') = '" . $day . "'  
                                AND date_format(leave_end_time, '%Y-%m-%d') = '" . $day . "'  
                                AND leave_start_type = 2 THEN  2 
                            WHEN  date_format(leave_start_time, '%Y-%m-%d') = '" . $day . "' 
                                AND date_format(leave_end_time, '%Y-%m-%d') = '" . $day . "'  
                                AND  leave_start_type = 1 AND leave_end_type = 2 THEN  3 
                        END) as level_type_day
                        FROM `staff_audit` WHERE  status =2 
                        And audit_type = 2 
                        And
                        (
                       '" . $day . "'  BETWEEN date_format(leave_start_time, '%Y-%m-%d') 
                       AND
                       date_format(leave_end_time, '%Y-%m-%d') 
                       )
                        ";
        if ($staffId) {
            $punchCardSql .= " And `staff_info_id` =" . $staffId;
        }
        $data       = $this->getDI()->get('db')->query($punchCardSql);
        $returnData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /**
     * 根据员工ID查询打卡数据
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function selectPunchCardDataByLevelStaffId($day, $staffId = [])
    {
        $selectSql               = "select * from staff_work_attendance where staff_info_id in(" . implode(',', $staffId) . ") 
                    AND attendance_date='" . $day . "'";
        $data                    = $this->getDI()->get('db')->query($selectSql);
        $staffInfoAttendanceData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $staffInfoAttendanceData;
    }

    /**
     * @param $audit_log  model->staff_audit_tool_log  日志表组装数据
     * @param $change_status  model->staff_audit 更改状态 模型
     */
    public function cancel($audit_log, $change_status)
    {
        if ($audit_log)
            $insetSql = $this->getInsertDbSql('staff_audit_tool_log', $audit_log);

        $up_sql = "update staff_audit set status = {$change_status['status']} ";

        //bi 轮休操作 需要标记 是否是轮休撤销了请假
        if(!empty($change_status['reject_reason']))
            $up_sql .= ", reject_reason = '{$change_status['reject_reason']}' ";

        $up_where = " where audit_id = {$change_status['audit_id']} ";

        $db       = $this->getDI()->get('db');
        try {
            $db->begin();
            if (isset($insetSql) && $insetSql)
                $db->execute($insetSql);
            $db->execute($up_sql . $up_where);
//            $auditId = $db->lastInsertId();
            $db->commit();
        } catch (\Exception $e) {
            $ControllerBase = new \FlashExpress\bi\App\Controllers\ControllerBase;
            $ControllerBase->wLog('记录请假撤销失败日志', $audit_log);
            $db->rollback();
            return false;
        }
        return true;
    }


    /**
     *
     * 根据日期和id获取该员工是否存在申请lh  判断 是否可申请ot
     * @param $staff_id
     * @param $lh_date
     * @return mixed
     */
    public function getLhInfo($staff_id, $lh_date)
    {
        $sql = " select count(1) from staff_audit where  staff_info_id = {$staff_id} and audit_type = 3 
                and lh_date =  '{$lh_date}' and status in (1,2)";
        return $this->getDI()->get('db')->fetchColumn($sql);
    }

    //补申请方法获取真实lh 申请 按日期返回
    public function getActLh($staff_id, $start_date, $end_date)
    {
        $sql = "select *,lh_date as date_at from staff_audit where staff_info_id = {$staff_id} 
                and lh_date >='{$start_date}' 
                and lh_date <= '{$end_date}' and status in (1,2)";

        $data = $this->getDI()->get('db')->query($sql);
        return $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }

    //补申请需求  员工是否有真实请假记录
    public function getActLeave($staff_id, $start_time, $end_time)
    {
        $sql = "select * ,date(leave_start_time) start_date ,date(leave_end_time) end_date
                from staff_audit where staff_info_id = {$staff_id}  and audit_type = 2 
                and leave_start_time <= '{$start_time}' and leave_end_time >= '{$end_time}'
                and status in (1,2) ";

        $data = $this->getDI()->get('db')->query($sql);
        return $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);

    }

    //指定补卡区间内 该员工所有请假记录
    public function getAllLeaveList($staff_id, $start_date, $end_date)
    {
        $sql = "
                select * ,date(leave_start_time) start_date ,date(leave_end_time) end_date
                from staff_audit where staff_info_id = {$staff_id}  and audit_type = 2 
                and (
                (date(leave_start_time) <= '{$start_date}' and date(leave_end_time) >= '{$start_date}')
                  or (date(leave_start_time) <= '{$end_date}' and date(leave_end_time) >= '{$end_date}') )
                ";

        $data = $this->getDI()->get('db')->query($sql);
        return $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }

    /**
     * 补申请需求 查询是否存在补卡记录
     * @param $staff_id
     * @param $attendance_date
     * @param $attendance_type 考勤类型 1上班打卡 2下班打卡
     */
    public function getAttendanceAudit($staff_id, $attendance_date, $attendance_type = '')
    {
        $sql = "select *
                from staff_audit where staff_info_id = {$staff_id}  and audit_type = 1
                and attendance_date = '{$attendance_date}' and status in (1,2) ";

        if (!empty($attendance_type))
            $sql .= " and attendance_type = {$attendance_type}";
        $data = $this->getDI()->get('db')->query($sql);
        return $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }

    /**
     * @param $staff_id
     * @param $start_date
     * @param $end_date
     */
    public function getAllAttendanceList($staff_id, $start_date, $end_date)
    {
        $sql  = "select * from staff_audit
                where staff_info_id = {$staff_id}  and audit_type = 1
                and attendance_date >= '{$start_date}' and attendance_date <= '{$end_date}'
                and status in (1,2)
                ";
        $data = $this->getDI()->get('db')->query($sql);
        return $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }

    /**
     * 获取审批权限
     * @param int staff_id
     * @param int submitter_id
     * @param int status
     * @return int
     */
    public function getAuditPermission($paramIn = [])
    {
        $staffId     = $paramIn['staff_id']; //当前用户
        $submitterId = $paramIn['submitter_id'];//提交记录的人
        $status      = $paramIn['status'];//审批状态
        //查询上级主管
        $staffAllData = (new \FlashExpress\bi\App\Server\StaffServer)->getHigherStaffId($submitterId);

        $is_audit = 4;
        if (!empty($staffAllData)) {
            //if($staffAllData['value'] == $staffId) {
            if (in_array($staffId, $staffAllData)) {
                $is_audit = 1;
            }
        } else {
            if ($status == 1) {
                if ($staffId != $submitterId) {
                    $is_audit = 3;
                } else {
                    $is_audit = 2;
                }
            }
        }
        return $is_audit;
    }


    /**
     * 物料审批权限
     * @param int staff_id
     * @param int submitter_id
     * @param int status
     * @return int
     */
    public function wmsAuditPermission($paramIn = [])
    {
        $staffId     = $paramIn['staff_id']; //当前用户
        $submitterId = $paramIn['submitter_id'];//提交记录的人
        $status      = $paramIn['status'];//审批状态
        //查询采购
        $buyerArr = UC('wmsRole')['buyer_id'];//物料采购
        $is_audit = 4;
        if (in_array($staffId, $buyerArr)) {
            $is_audit = 1;
        } else {
            if ($status == 1) {
                if ($staffId != $submitterId) {
                    $is_audit = 3;
                } else {
                    $is_audit = 2;
                }
            }
        }
        return $is_audit;
    }

    /**
     * 获取规定时间内的 所有请假 计算剩余假期用 后期会改成staff_audit_leave_split 获取数据 现在数据 不完整
     * @param $staff_id
     * @param $start_date
     * @param $end_date
     * @param $type
     */
    public function get_leave_days($staff_id, $start_date, $end_date, $type = 0)
    {

//        $sql = "select staff_info_id, leave_type,leave_day
//                ,leave_start_time,leave_end_time
//                from staff_audit
//                where staff_info_id = {$staff_id} and audit_type = 2 and status in (1,2)
//                and leave_start_time between '{$start_date}' and '{$end_date}'
//                and leave_end_time between '{$start_date}' and '{$end_date}'";


        $sql = "select staff_info_id, leave_type,leave_day
                ,leave_start_time,leave_end_time,leave_start_type,leave_end_type
                from staff_audit 
                where staff_info_id = {$staff_id} and audit_type = 2 and status in (1,2)
                ";

        //新需求 请假判定 逻辑 年假改为 创建时间 其他假期 不变
        if (empty($type)) {
            $where = " and ( 
                      ( 
                        (
                            leave_start_time between '{$start_date}' and '{$end_date}'
                            or leave_end_time between '{$start_date}' and '{$end_date}'
                            or (leave_start_time < '{$start_date}' and leave_end_time > '{$end_date}')
                            or (leave_start_time > '{$start_date}' and leave_end_time < '{$end_date}')
                        ) and leave_type != 1)
                        or ( created_at between '{$start_date}' and '{$end_date}' and leave_type = 1 )
                      
                      )";
        } else if ($type == 1) {
            $where = " and ( created_at between '{$start_date}' and '{$end_date}' and leave_type = 1 )
                      ";
        } else {
            $where = " and ( 
                        (
                            leave_start_time between '{$start_date}' and '{$end_date}'
                            or leave_end_time between '{$start_date}' and '{$end_date}'
                            or (leave_start_time < '{$start_date}' and leave_end_time > '{$end_date}')
                            or (leave_start_time > '{$start_date}' and leave_end_time < '{$end_date}')
                        ) 
                        and leave_type = {$type})
                    ";
        }

        $data = $this->getDI()->get('db')->query($sql . $where);
        return $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);

    }

    //根据拆分表 计算 已用年假
    public function get_year_applied_days($staff_id, $year = '')
    {
        if (empty($staff_id))
            return false;
        if (empty($year))
            $year = date('Y', time());

        $sql = " select sum(if(s.`type`=0,1,0.5)) as num
                from staff_audit_leave_split s
                join staff_audit a on a.audit_id = s.audit_id
                where a.staff_info_id = {$staff_id}
                and a.audit_type = 2 and leave_type = 1 
                and a.status in (1,2) and s.year_at = {$year}

                ";
        return $this->getDI()->get('db')->fetchColumn($sql);

    }

    //根据拆分表 计算 已用年假 定时任务用

    /**
     * @param $staff_id
     * @param string $year
     * @param string $invalid_date
     * @param int $out_id 计算马来需要排除的 audit id
     * @return bool
     */
    public function get_last_year_split($staff_id, $year = '',$invalid_date = '',$out_id = 0)
    {
        if (empty($staff_id))
            return false;

        $sql  = " select sum(if(s.`type`=0,1,0.5)) as num
                ,group_concat(s.id) as split_id
                from staff_audit_leave_split s
                join staff_audit a on a.audit_id = s.audit_id
                where a.staff_info_id = {$staff_id}
                and a.audit_type = 2 and leave_type = 1 
                and a.status in (1,2) and s.year_at = {$year}

                ";

        $bind = array();
        if(!empty($invalid_date)){
            $sql .= " and s.date_at >= :invalid_date ";
            $bind['invalid_date'] = $invalid_date;
        }

        if(!empty($out_id)){
            $sql .= " and a.audit_id != :out_id ";
            $bind['out_id'] = $out_id;
        }

        $data = $this->getDI()->get('db')->query($sql,$bind)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $data;

    }

    //获取 不同类型 当年 已用天数
    public function get_used_leave_days($staff_id, $year = '', $type_str = '')
    {
        if (empty($staff_id))
            return false;

        $sql = " select sum(if(s.`type`=0,1,0.5)) as num
                ,group_concat(s.id) as split_id
                ,a.leave_type
                ,group_concat(distinct a.audit_id) audit_ids
                from staff_audit_leave_split s
                join staff_audit a on a.audit_id = s.audit_id
                where a.staff_info_id = {$staff_id}
                and a.audit_type = 2 
                and a.status in (1,2) 

                ";

        if(!empty($year))
            $sql .= " and s.year_at = {$year} ";

        if (!empty($type_str))
            $sql .= " and a.leave_type in ({$type_str})";

        $sql .= " group by a.leave_type ";
//        echo $sql;exit;
        $data = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $data;

    }


    //获取 时间区间的请假和类型 考勤日历用
    public function get_leave_date($staff_id, $start_date, $end_date, $type = '')
    {

        $sql = "select sum(s.type) type, s.date_at,max(a.leave_type) leave_type
                from staff_audit_leave_split s
                join staff_audit a on s.staff_info_id = a.staff_info_id and  (a.audit_id = s.audit_id or (s.audit_id = 0 and a.leave_type = 15 and date(a.`leave_start_time`) = s.date_at))                
                where a.staff_info_id = {$staff_id}
                and a.audit_type = 2 
                and a.status = 2 
                and s.date_at between '{$start_date}' and '{$end_date}'
                and a.parent_id = 0
                ";

        if (!empty($type))
            $sql .= " and a.leave_type = {$type} ";

        $sql  .= " group by s.date_at ";
        $data = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $data;
    }

    public function getLeaveDataForSalary($staff_id, $start_date, $end_date): array
    {
        $sql        = "select a.leave_type,a.leave_start_time,a.leave_end_time,a.leave_start_type,a.leave_end_type,s.type as day_type, s.date_at
                from staff_audit_leave_split s
                join staff_audit a on s.staff_info_id = a.staff_info_id and a.audit_id = s.audit_id              
                where a.staff_info_id = {$staff_id}
                and a.audit_type = 2 
                and a.status = 2 
                and s.date_at between '{$start_date}' and '{$end_date}'
                ";
        // th  12  LW 无薪假(不带薪事假), 18 USL 无薪病假，  4_2 other_UL 无薪产假 ， 9 LW 个人受训假
        // ph  4 LW 产假，  21 LW 女性特殊假，  23 LW 紧急假 ， 26 C19SL疫情确诊假 ， 25 ISL隔离假
        // my  12 LW 无薪假(不带薪事假)
        // la  24 LW 无薪假(不带薪事假)
        // vn  12 LW 无薪假(不带薪事假), 18 LW 无薪病假,4 LW 无薪产假，17 LW 无薪产检假, 5 LW 陪产假 (无薪)
        // id 12 LW  无薪假(不带薪事假)
        if (isCountry()) {
            $sql .= ' and a.leave_type in (12,18,4,9)';
        }
        if (isCountry('PH')) {
            $sql .= ' and a.leave_type in (4,21,23,26,25)';
        }
        if (isCountry('MY') || isCountry('ID')) {
            $sql .= ' and a.leave_type in (12)';
        }
        if (isCountry('LA')) {
            $sql .= ' and a.leave_type in (24)';
        }
        if (isCountry('VN')) {
            $sql .= ' and a.leave_type in (12,18,4,17,5)';
        }
        $sql        .= "order by s.date_at,s.type";
        $data       = $this->db->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $returnData = [];
        foreach ($data as $item) {
            $item['leave_start_time']                        = date('Y-m-d', strtotime($item['leave_start_time']));
            $item['leave_end_time']                          = date('Y-m-d', strtotime($item['leave_end_time']));
            $returnData[$item['date_at']][$item['day_type']] = $item;
        }
        return $returnData;
    }
    //获取去年剩余年假 新版获取去年剩余年假
    public function get_last_year($staff_id, $year_time)
    {
        $sql = "select left_days from staff_last_year_days where staff_info_id = {$staff_id} and year_time = {$year_time}";
        return $this->getDI()->get('db')->fetchColumn($sql);
    }

    //获取去年剩余年假 新版获取去年剩余年假
    public function get_last_year_info($staff_id, $year_time)
    {
        $sql = "select * from staff_last_year_days where staff_info_id = {$staff_id} and year_time = {$year_time}";
        return $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
    }

    /**
     * 部分请假 记录 限制一次  获取该员工全表是否存在 申请记录
     * @param $staff_id
     * @param $leave_type
     */
    public function get_leave_count($staff_id, $leave_type)
    {
        $sql  = "select sum(leave_day)
                from staff_audit 
                where staff_info_id = {$staff_id} and audit_type = 2 and status in (1,2)
                and leave_type = {$leave_type}";
        return $this->getDI()->get('db')->fetchColumn($sql);
    }


    //bi工具 查询用户 请假
    public function get_leave_by_month($staff_id, $condition)
    {
        $sql = "select staff_info_id, leave_start_time, leave_end_time,leave_type,leave_day ,approver_id,approver_name
                ,date_format(CONVERT_TZ(created_at,  '+00:00', '{$this->timezone}'),'%Y-%m-%d %H:%i:%s') created_at
                from staff_audit 
                where staff_info_id = {$staff_id} and audit_type = 2 and status in (1,2) ";
        if (!empty($condition['month'])) {
            $month = $condition['month'];
            $sql   .= " and date_format(leave_start_time,'%Y-%m') = '{$month}' ";

        }
        if (!empty($condition['type']))
            $sql .= " and leave_type = {$condition['type']}";
        $data = $this->getDI()->get('db')->query($sql);
        return $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }


    /**
     * 创建订单
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addOrderInfo($orderData, $orderDetailData, $assetType)
    {
        $insetData = $orderData;
        $db        = $this->getDI()->get('db');
        // 插入
        try {
            $success = $db->insertAsDict('assets_order', $insetData);
            if (!$success) {
                return false;
            }
            $resumeId = $db->lastInsertId();
            foreach ($orderDetailData as $key => $value) {
                $orderDetailData[$key]['order_id'] = $resumeId;
            }
            //订单物品详情
            $insetOrderDetail = $this->batch_insert('assets_order_detail', $orderDetailData);
            if (!$insetOrderDetail) {
                return false;
            }
            $wfRole            = $assetType == AssetServer::ASSET_TYPE[0] ? 'as_' . $resumeId : 'asp_' . $resumeId;
            $updateOrderDetail = $this->updateInfoByTable('assets_order', 'id', $resumeId, ['wf_role' => $wfRole]);
            if (!$updateOrderDetail) {
                return false;
            }

        } catch (\Exception $e) {
            return false;
        }
        return $resumeId;
    }

    /**
     * 获取员工最近一次请假数据[通过审批的]
     * @param $paramIn array
     * @return $data array
     */
    public function getLatestLeaveDataByStaffId($staff_id)
    {
        $punchCardSql = " 
                        SELECT *
                        FROM staff_audit_leave 
                        WHERE staff_info_id = {$staff_id} AND status = 2
                        ORDER BY leave_end_time DESC LIMIT 1
                        ";
        return $this->getDI()->get('db')->query($punchCardSql)->fetch(\Phalcon\Db::FETCH_ASSOC);
    }

    /**
     * 查询员工单日请假信息
     * @param $staff_id
     * @param $date_at
     */
    public function getStaffLeaveInfoByDate($staff_id, $date_at)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['salp.type','salp.date_at' ,'salp.staff_info_id','sa.leave_type']);
        $builder->from(['salp' => StaffAuditLeaveSplitModel::class]);
        $builder->leftJoin(StaffAuditModel::class, 'sa.audit_id = salp.audit_id ', 'sa');
        $builder->where('salp.staff_info_id = :staff_info_id: and salp.date_at = :date_at:  and  sa.audit_type = 2 and sa.status = 2 and sa.parent_id = 0', ['staff_info_id' => $staff_id,'date_at'=>$date_at]);
        $builder->orderBy('salp.type asc');
        return $builder->getQuery()->execute()->toArray();
    }


}
