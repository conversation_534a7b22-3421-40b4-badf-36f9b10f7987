<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\backyard\HrInterviewOfferModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferSignApproveModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;

class OfferRepository extends BaseRepository {

    public $timezone;
    public $db;

    public function __construct($timezone) {
        parent::__construct();
        $this->timezone = $timezone;
        $this->db = $this->getDI()->get('db');
    }

    /**
     * 检查Offer
     * @param type $paramIn
     * @return type
     */
    public function checkOffer($paramIn = []) {
        //offer id
        $offerId = $paramIn['id'] ?? '';
        //面试id
        $interviewId = $paramIn['interview_id'];
        //简历id
        $resumeId = $paramIn['resume_id'] ?? '';
        //状态 1=发送 2=取消发送
        $status = isset($paramIn['status']) ? $paramIn['status'] : "1,2";

        if ($interviewId) {
            $sql = "select * from hr_interview_offer where status in (" . $status . ") and interview_id=" . $interviewId;
        } elseif ($resumeId) {
            $sql = "select * from hr_interview_offer where status in (" . $status . ") and resume_id=" . $resumeId;
        } elseif ($offerId) {
            $sql = "select * from hr_interview_offer where status in (" . $status . ") and id=" . $offerId;
        } else {
            return [];
        }
        $data = $this->db->query($sql);
        $reData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);

        return $reData;
    }


    /**
     * 通过resume_id获取offer表信息
     * @param $params
     * @return mixed
     */
    public function getOfferByResumeId($resume_id){
        return HrInterviewOfferModel::findFirst([
            'columns' => "hc_id,DATE_FORMAT(work_time,'%Y-%m-%d') as work_time",
            'conditions' => 'resume_id = :resume_id:',
            'bind' => ['resume_id' => $resume_id]
        ]);
    }
}
