<?php
/**
 * Author: Bruce
 * Date  : 2024-07-09 19:47
 * Description:
 */

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\Models\backyard\StaffPickupDeliveryDataModel;

class StaffPickupDeliveryDataRepository extends BaseRepository
{
    /**
     * 获取指定 日期区间的 揽派件
     * @param $staff_info_id
     * @param $start_date
     * @param $end_date
     * @return array
     */
    public static function getStaffData($staff_info_id, $start_date, $end_date)
    {
        if(empty($staff_info_id) || empty($start_date) || empty($end_date)) {
            return [];
        }


        $staffPickupDeliveryData = StaffPickupDeliveryDataModel::find([
            'conditions' => 'staff_info_id = :staff_info_id: and stat_date >= :start_date: and stat_date <= :end_date: and is_deleted = :is_deleted:',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
                'start_date'    => $start_date,
                'end_date'      => $end_date,
                'is_deleted'    => StaffPickupDeliveryDataModel::IS_DELETED_NO,
            ],
        ])->toArray();
        $staffPickupDeliveryData = !empty($staffPickupDeliveryData) ? array_column($staffPickupDeliveryData, null,
            'stat_date') : [];

        $return_data             = [];

        $dateList = DateHelper::DateRange(strtotime($start_date), strtotime($end_date));
        arsort($dateList);
        foreach ($dateList as $key => $oneDate) {
            $_data['date']           = $oneDate;
            $_data['date_m']         = date('m/d', strtotime($oneDate));
            $_data['pickup_count']   = !empty($staffPickupDeliveryData[$oneDate]) ? $staffPickupDeliveryData[$oneDate]['pickup_count'] : 0;//揽件
            $_data['delivery_count'] = !empty($staffPickupDeliveryData[$oneDate]) ? $staffPickupDeliveryData[$oneDate]['delivery_count'] : 0;//派件
            $return_data[] = $_data;
        }
        return $return_data;
    }

}