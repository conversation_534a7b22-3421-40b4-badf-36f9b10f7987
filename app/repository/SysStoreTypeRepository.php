<?php

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\SysStoreTypeModel;

class SysStoreTypeRepository extends BaseRepository
{

    public static function getList(): array
    {
        $types = SysStoreTypeModel::find([
            'columns' => 'id,code',
            'order'   => 'id+0 ASC',
        ])->toArray();
        return array_values(array_map(function ($v) {
            return [
                'label' => $v['code'],
                'value' => intval($v['id']),
            ];
        }, $types));
    }
}