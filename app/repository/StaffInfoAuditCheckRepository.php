<?php
/**
 * Author: Bruce
 * Date  : 2024-04-25 22:04
 * Description:
 */

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\StaffInfoAuditCheckModel;

class StaffInfoAuditCheckRepository extends BaseRepository
{
    /**
     * AI识别员工信息数据---单条
     * @param string $columns
     * @param string $conditions
     * @param array $bind
     * @return array
     */
    public static function getStaffAuditCheckInfo($columns = '*', $conditions = '', $bind = [])
    {
        $data = StaffInfoAuditCheckModel::findFirst([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);

        return !empty($data) ? $data->toArray() : [];
    }

}