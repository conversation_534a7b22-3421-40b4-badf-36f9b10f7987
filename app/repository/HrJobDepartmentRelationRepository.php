<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrJobDepartmentRelationModel;

class HrJobDepartmentRelationRepository extends BaseRepository
{
    public $timezone;

    public function __construct($timezone)
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    /**
     * @description 获取指定部门、职位对应的职级范围
     * @param $department_id
     * @param $job_title_id
     * @param $column_id
     * @return mixed
     * @throws BusinessException
     */
    public static function getJobDepartmentRelationColumn($department_id, $job_title_id, $column_id)
    {
        $info = HrJobDepartmentRelationModel::findFirst([
            'conditions' => 'department_id = :department_id: and job_id = :job_id:',
            'bind' => [
                'department_id' => $department_id,
                'job_id' => $job_title_id
            ]
        ]);
        if (empty($info)) {
            return null;
        }

        if (!isset($info->$column_id)) {
            throw new BusinessException("Column id '$column_id' not found");
        }
        return $info->$column_id;
    }

    /**
     * @description 获取指定部门、职位对应的职级范围
     * @param $department_id
     * @param $job_title_id
     * @return array
     * @throws BusinessException
     */
    public static function getJobGrade($department_id, $job_title_id): array
    {
        $result = self::getJobDepartmentRelationColumn($department_id, $job_title_id, 'job_level');
        return !is_null($result) ? explode(',', $result): [];
    }

    public static function getRelationsList($params, $columns = ['*'])
    {
        if (empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind       = [];

        if (!empty($params['department_ids'])) {
            $conditions         .= ' and department_id in ({department_ids:array})';
            $bind['department_ids'] = $params['department_ids'];
        }

        if (!empty($params['position_type'])) {
            $conditions .= ' and  position_type = :position_type:';
            $bind['position_type'] = $params['position_type'];
        }

        return HrJobDepartmentRelationModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => $columns,
        ])->toArray();
    }
}