<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\backyard\WorkPassModel;

class WorkPassRepository extends BaseRepository {
    
    const CYCLE = 14;

    public $timezone;

    public function __construct($lang = 'zh-CN', $timezone) {
        parent::__construct($lang);
        $this->timezone = $timezone;
    }

    /**
     * 读取最新一条记录
     * @param int $staff_id
     * @return array
     * @throws \FlashExpress\bi\App\library\Exception\InnerException
     */
    public function getOneByStaffId($staff_id, $order_type = 'id desc') {

        $result = WorkPassModel::findFirst([
                    'conditions' => 'staff_id = :staff_id: AND deleted = 0',
                    'bind' => ['staff_id' => $staff_id],
                    'order' => $order_type,
                ]);

        return $result;
    }

    /**
     * 新增记录
     * @param int $staff_id
     * @return array
     */
    public function addRecord($staff_id) {

        $current_date = date('Y-m-d');

        $model = new WorkPassModel();
        $model->staff_id = $staff_id;
        $model->expiration_date = date('Y-m-d', strtotime(self::CYCLE . ' days'));
        $create_res = $model->save();

        return $model->toArray();
    }

}
