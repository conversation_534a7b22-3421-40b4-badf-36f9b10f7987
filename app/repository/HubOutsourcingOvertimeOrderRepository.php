<?php
/**
 * Author: Bruce
 * Date  : 2023-12-11 22:13
 * Description:
 */

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\HubOutsourcingOvertimeOrderModel;

class HubOutsourcingOvertimeOrderRepository extends BaseRepository
{
    public $timezone;

    public function __construct($timezone)
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    public static function getHubOtOrderInfo($orderNos)
    {
        return HubOutsourcingOvertimeOrderModel::find([
            'conditions' => 'outsourcing_order_serial_no in ({order_nos:array})',
            'bind'       => [
                'order_nos' => $orderNos,
            ],
         ])->toArray();
    }

}