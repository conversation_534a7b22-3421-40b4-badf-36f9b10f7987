<?php


namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\StaffVehicleStatusRecord;

class StaffVehicleStatusRecordRepository extends BaseRepository
{
    /**
     * @return \Phalcon\Mvc\Model\ResultsetInterface
     */
    public function findAll()
    {
        return StaffVehicleStatusRecord::find();
    }

    /**
     * @param $id
     * @return \Phalcon\Mvc\Model
     */
    public function findOne($id)
    {
        return StaffVehicleStatusRecord::findFirst($id);
    }

    /**
     * @param $cond
     * @return \Phalcon\Mvc\Model\ResultsetInterface
     */
    public function findAllByCondition($cond)
    {
        $query = StaffVehicleStatusRecord::query();
        $query->where('1=1');
        foreach ($cond as $k=>$v){
            $query->andWhere("$k=:$k:",["$k"=>$v]);
        }
        return $query->execute();
    }

    /**
     * @param $data
     * @return bool
     */
    public function insert($data)
    {
        $m = new StaffVehicleStatusRecord();
        return $m->save($data);
    }

    /**
     * @param $model StaffVehicleStatusRecord
     * @param $data
     * @return bool
     */
    public function update($model, $data)
    {
        return $model->save($data);
    }
}