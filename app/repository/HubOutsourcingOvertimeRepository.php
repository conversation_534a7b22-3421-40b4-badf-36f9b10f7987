<?php

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HubOutsourcingOvertimeModel;

class HubOutsourcingOvertimeRepository extends BaseRepository
{
    public $timezone;

    public function __construct($timezone)
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    /**
     * 新建外协加班
     * @Access  public
     * @param array $paramIn
     * @return int
     */
    public function addOutsourcingOT(array $paramIn = []): int
    {
        $insertSql = $this->getInsertDbSql('hub_outsourcing_overtime', $paramIn);
        $this->getDI()->get('db')->query($insertSql);
        return $this->getDI()->get('db')->lastInsertId();
    }

    /**
     * 根据id获取详细信息
     * @param int $id
     * @return array
     */
    public function getOutsourcingOTById(int $id = 0): array
    {
        if (0 >= $id) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(
            "main.id,
                    main.serial_no,
                    main.store_id,
                    main.attendance_date,
                    main.ot_date,
                    main.start_time,
                    main.end_time,
                    main.duration,
                    main.outsourcing_company_id,
                    main.reason,
                    main.img,
                    main.source,
                    main.demand_num,
                    main.apply_staff_id,
                    main.reject_reason,
                    main.date_at,
                    main.osm_state,
                    main.apply_state,
                    main.shift_id,
                    CONVERT_TZ(created_at,'+00:00', '".$this->timezone."' ) AS created_at, 
                    CONVERT_TZ(updated_at,'+00:00', '".$this->timezone."' ) AS updated_at");
        $builder->from(['main' => HubOutsourcingOvertimeModel::class]);
        $builder->andWhere("main.id = :id:", ['id' => $id]);
        $result = $builder->getQuery()->execute()->getFirst();
        return $result ? $result->toArray() : [];
    }

    /**
     * 根据id更新数据
     * @param array $update_data
     * @param int $id
     * @return mixed
     */
    public function updateOutsourcingOTById(array $update_data, int $id)
    {
        return $this->getDI()->get('db')->updateAsDict('hub_outsourcing_overtime', $update_data,
            [
                'conditions' => 'id = ?',
                'bind'       => [$id],
            ]
        );
    }

    /**
     * 获取待审批和已通过的信息
     * @param $params
     * @return mixed
     */
    public static function getOutsourcingOTList($params)
    {
        return HubOutsourcingOvertimeModel::find([
            'conditions' => 'store_id = :store_id: and ot_date = :ot_date: and shift_id = :shift_id: and apply_state in ({apply_states:array})',
            'bind'       => [
                'store_id' => $params['store_id'],
                'ot_date'  => $params['ot_date'],
                'shift_id' => $params['shift_id'],
                'apply_states' => [enums::APPROVAL_STATUS_PENDING, enums::APPROVAL_STATUS_APPROVAL],
            ],
        ])->toArray();
    }

}