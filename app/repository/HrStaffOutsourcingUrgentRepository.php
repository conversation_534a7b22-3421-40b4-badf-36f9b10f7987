<?php

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\HrStaffOutsourcingModel;
use FlashExpress\bi\App\Models\backyard\HrStaffOutsourcingUrgentModel;

class HrStaffOutsourcingUrgentRepository extends BaseRepository
{
    public $timezone;

    public function __construct($timezone)
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    /**
     * 创建外协员工申请
     */
    public function insert($insert)
    {
        $insertSql = $this->getInsertDbSql((new HrStaffOutsourcingUrgentModel)->getSource(), $insert);
        $this->getDI()->get('db')->query($insertSql);
        return $this->getDI()->get('db')->lastInsertId();
    }

    /**
     * @param array $params
     * @return array
     */
    public function getOutsourcingByTime(array $params): array
    {
        $result = [];
        if (empty($params['b_time']) || empty($params['e_time']) || empty($params['os_type']) || empty($params['status'])) {
            return $result;
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'hou.id',
            'hou.serial_no',
            'ho.id',
            'ho.os_type',
            'ho.status',
            'ho.employment_date',
            'ho.shift_begin_time',
            'ho.shift_end_time',
            'ho.demend_num',
            'ho.final_audit_num',
            'ho.store_id'
        ]);
        $builder->from(['hou' => HrStaffOutsourcingUrgentModel::class]);
        $builder->innerJoin(HrStaffOutsourcingModel::class, 'ho.serial_no = hou.serial_no', 'ho');
        $builder->where('hou.shift_begin_time >= :b_time:', ['b_time' => $params['b_time']]);
        $builder->andWhere('hou.shift_begin_time < :e_time:', ['e_time' => $params['e_time']]);
        $builder->andWhere('ho.os_type = :os_type:', ['os_type' => $params['os_type']]);
        $builder->andWhere('ho.status = :status:', ['status' => $params['status']]);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 根据班次id、提交日期获取数据
     * @param array $params
     * @return array
     */
    public function getOutsourcingUrgentByShiftIdAndSubmitDate(array $params): array
    {
        $result = [];
        if (empty($params['shift_id']) || empty($params['submit_date']) || empty($params['store_id'])) {
            return $result;
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['hou.id', 'hou.serial_no', 'hou.shift_id', 'hou.submit_date']);
        $builder->from(['hou' => HrStaffOutsourcingUrgentModel::class]);
        $builder->join(HrStaffOutsourcingModel::class, 'hso.serial_no = hou.serial_no', 'hso');
        $builder->andWhere('hou.shift_id = :shift_id:', ['shift_id' => $params['shift_id']]);
        $builder->andWhere('hou.submit_date = :submit_date:', ['submit_date' => $params['submit_date']]);
        $builder->andWhere('hso.store_id = :store_id:', ['store_id' => $params['store_id']]);
        $builder->andWhere('hso.status in ({audit_status:array})', ['audit_status' => [HrStaffOutsourcingModel::AUDIT_STATUS_1, HrStaffOutsourcingModel::AUDIT_STATUS_2]]);
        return $builder->getQuery()->execute()->toArray();
    }
}