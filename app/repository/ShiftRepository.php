<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrShiftModel;
use FlashExpress\bi\App\Models\backyard\HrShiftV2ExtendModel;
use FlashExpress\bi\App\Models\backyard\HrShiftV2Model;
use FlashExpress\bi\App\Server\SettingEnvServer;

class ShiftRepository extends BaseRepository
{
    public $timezone;

    public function __construct($timezone)
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    /**
     * 启用的班次ids
     * @var array
     */
    public $enable_shift_ids = [];

    public function setEnableShiftIds(array $enable_shift_ids): ShiftRepository
    {
        $this->enable_shift_ids = $enable_shift_ids;
        return $this;
    }


    /**
     * v2版本班次信息  目前仅有马来在用
     * @param $shift_id
     * @param $shift_extend_id
     * @return array
     */
    public function getV2ShiftInfo($shift_id, $shift_extend_id = 0): array
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            's.id',
            's.shift_name',
            'se.shift_type',
            'se.first_start',
            'se.first_end',
            'se.second_start',
            'se.second_end',
            'se.id as shift_extend_id',
        ]);
        $builder->from(['s' => HrShiftV2Model::class]);
        $builder->leftJoin(HrShiftV2ExtendModel::class, 's.id = se.shift_id', 'se');
        $builder->where('s.id = :id: and  s.is_deleted = :is_deleted:',
            ['id' => $shift_id, 'is_deleted' => 0]);

        if (empty($shift_extend_id)) {
            $builder->andWhere('se.state = :state: ', ['state' => HrShiftV2ExtendModel::STATE_USING]);
        } else {
            $builder->andWhere('se.id = :shift_extend_id: ', ['shift_extend_id' => $shift_extend_id]);
        }

        if(!empty($this->enable_shift_ids)){
            $builder->andWhere('s.id in ({enable_shift_ids:array})',['enable_shift_ids'=>$this->enable_shift_ids]);
        }

        $result = $builder->getQuery()->execute()->getFirst();

        if (empty($result)) {
            return [];
        }
        $result                    = $result->toArray();
        $result['shift_work_time'] = $result['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE ? $result['first_start'].'-'.$result['first_end'].' '.$result['second_start'].'-'.$result['second_end'] : $result['first_start'].'-'.$result['first_end'];

        return $result;
    }

    /**
     * v2版本班次列表  目前仅有马来在用
     * @return mixed
     */
    public function getV2ShiftList()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            's.id',
            's.shift_name',
            'se.shift_type',
            'se.first_start',
            'se.first_end',
            'se.second_start',
            'se.second_end',
            'se.id as shift_extend_id',
        ]);
        $builder->from(['s' => HrShiftV2Model::class]);
        $builder->leftJoin(HrShiftV2ExtendModel::class, 's.id = se.shift_id', 'se');
        $builder->where('s.is_deleted = :is_deleted: AND se.state = :state: ',
            ['is_deleted' => 0, 'state' => HrShiftV2ExtendModel::STATE_USING]);
        $builder->orderBy('se.first_start asc');
        if(!empty($this->enable_shift_ids)){
            $builder->andWhere('s.id in ({enable_shift_ids:array})',['enable_shift_ids'=>$this->enable_shift_ids]);
        }
        $result = $builder->getQuery()->execute()->toArray();
        foreach ($result as &$item) {
            $item['shift_work_time'] = $item['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE ? $item['first_start'].'-'.$item['first_end'].' '.$item['second_start'].'-'.$item['second_end'] : $item['first_start'].'-'.$item['first_end'];
        }
        return $result;
    }
}