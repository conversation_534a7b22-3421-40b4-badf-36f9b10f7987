<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2020/7/3
 * Time: 下午4:50
 */

namespace FlashExpress\bi\App\Repository;

class StaffDeviceInfoRepository extends BaseRepository{

    protected $table = 'staff_device_info' ;


    public function get_info($staff_id){
        $sql = " select * from staff_device_info where staff_info_id = {$staff_id}";
        return $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
    }

    public function insert($insert){
        $sql = $this->getInsertDbSql($this->table,$insert);
        return $this->getDI()->get('db')->execute($sql);
    }


    public function update($insert){
        return $this->updateInfoByTable($this->table,'id',$insert['id'],$insert);
    }


    //同一设备 一小时之内 不允许打卡两次 同一工号打卡 可以 但不支持更新
    public function get_device_one_hour($client_id, $staff_id = 0){
        //当前时间 一小时前
        $start = gmdate('Y-m-d H:i:s',strtotime('-1 hour'));
        $end = gmdate('Y-m-d H:i:s',time());

        $sql = "select count(1) from staff_work_attendance where 
                (
                  (started_clientid = '{$client_id}' and (started_at between '{$start}' and '{$end}' or end_at between '{$start}' and '{$end}') )
                  or  (end_clientid = '{$client_id}' and (started_at between '{$start}' and '{$end}' or end_at between '{$start}' and '{$end}') )
                )";
        if(!empty($staff_id))
            $sql .= " and staff_info_id != '{$staff_id}'";

        return $this->getDI()->get('db_rby')->fetchColumn($sql);
    }

    /**
     * @param $client client id
     * @param $date 打卡日期
     * @param $type 1 上班 2 下班
     * @return int
     */
    public function get_click_num($client,$date,$type){
        $sql = "select count(1) from staff_work_attendance where 
                attendance_date = '{$date}'  
                ";
        if($type == 1)
            $sql .= " and started_clientid = '{$client}'";
        if($type == 2)
            $sql .= " and end_clientid = '{$client}'";
        return $this->getDI()->get('db')->fetchColumn($sql);

    }
}