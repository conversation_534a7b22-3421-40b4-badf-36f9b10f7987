<?php
/**
 * Author: Bruce
 * Date  : 2024-02-24 11:43
 * Description:
 */

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\SysProvinceModel;

class SysProvinceRepository extends BaseRepository
{
    public $timezone;

    public function __construct($timezone)
    {
        parent::__construct();
        $this->timezone = $timezone;
    }

    public static function findFirstByCode($code, $isDelete = false): array
    {
        $condition = "code = :code:";
        $bind      = ['code' => $code];
        if($isDelete !== false){
            $condition .= " AND deleted = :deleted:";
            $bind['deleted'] = $isDelete;
        }

        $city = SysProvinceModel::findFirst([
            'columns'    => 'code, name ',
            'conditions' => $condition,
            'bind'       => $bind,
        ]);
        return empty($city) ? [] : $city->toArray();
    }
}