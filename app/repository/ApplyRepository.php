<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2019/4/19
 * Time: 下午7:02
 */

namespace FlashExpress\bi\App\Repository;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;

class ApplyRepository extends BaseRepository
{


    public function initialize()
    {
        parent::initialize();
    }


    /**
     * 补发申请插入
     * @Access  public
     * @Param   $insetData Array 数据录入
     * @Return  array 员工补卡记录
     */
    public function applyInsert($insetData = [])
    {

        $db = $this->getDI()->get('db');
        try {
            //申请表插入数据
            $insetSql = $this->getInsertDbSql('staff_apply', $insetData);
            $db->begin();
            $flag = $db->execute($insetSql);
            if (!$flag) {
                $db->rollback();
                return false;
            }
            $auditId = $db->lastInsertId();
            $db->commit();
            return $auditId;
        } catch (\Exception $e) {
            echo $e->getMessage();exit;
            $db->rollback();
            return false;
        }
        return false;

    }


    //验证 晚班 申请 时间段 是否存在lh申请
    public function checkLH($staff_id,$start_time,$end_time){
        $sql = "select count(1) from staff_apply where staff_info_id = {$staff_id} and apply_category = 3
                and date(start_time) >= '{$start_time}' and date(start_time) <= '{$end_time}' ";
        $db = $this->getDI()->get('db');

        return $db->fetchColumn($sql);
    }

    //验证 lh申请时间是否存在晚班申请
    public function checkNightWork($staff_id,$lh_time){

        $sql = " select count(1) from staff_apply 
                where staff_info_id = {$staff_id} and apply_category = 2
                and date(start_time) >= '{$lh_time}' and date(end_time) <= '{$lh_time}'";
        $db = $this->getDI()->get('db');
        return $db->fetchColumn($sql);
    }


    //获取点击日期 已申请的记录
    public function checkDate($staff_id,$date){

        $sql = "select staff_info_id, apply_category
                from staff_apply where staff_info_id = {$staff_id} 
                and date(start_time) <= '{$date}' and date(end_time) >= '{$date}'";

        $db = $this->getDI()->get('db')->query($sql);
        return $db->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }

    //获取日历 规定时间的 申请记录
    public function getList($staff_id,$version){
        $where = '';
        if($version == 1){//请假和补卡
            $where = "  apply_category in (1,4) and version = 1";
        }else if($version == 2){//lh ot
            $where = "  apply_category in (2,3) and version = 2";
        }else if($version == 3){
            $where = "  apply_category = 3 and version = 3";
        }

        $sql = "select apply_id,apply_category,date(start_time) start_date,date(end_time) end_date,date_at
                ,start_time,end_time
                from staff_apply where staff_info_id = {$staff_id} and {$where}
                ";
        $db = $this->getDI()->get('db')->query($sql);
        return $db->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }

    //验证申请类型当天是否存在记录
    public function checkRow($staff_id,$category,$start_date,$end_date){
        $sql = " select count(1) from staff_apply
                where staff_info_id = {$staff_id} and apply_category = {$category} 
                and (
                (date(start_time) <= '{$start_date}' and date(end_time) >= '{$start_date}')
                or (date(start_time) <= '{$end_date}' and date(end_time) >= '{$end_date}')
                )";
        $db = $this->getDI()->get('db');

        return $db->fetchColumn($sql);
    }

    public function checkLhRow($staff_id,$category,$date){
        $sql = " select count(1) from staff_apply
                where staff_info_id = {$staff_id} and apply_category = {$category} 
                and date_at = '{$date}'";
        $db = $this->getDI()->get('db');

        return $db->fetchColumn($sql);
    }


    /**
     * 验证当天是否已经提交 补卡补申请  apply
     * @param $staff_id
     * @param $date_at
     * @param $apply_type 考勤类型 1上班打卡 2下班打卡
     */
    public function checkSign($staff_id, $date_at){
        $sql = " select count(1) from staff_apply 
                where staff_info_id = {$staff_id} and apply_category = 4
                and date_at = '{$date_at}'
                ";
        $db = $this->getDI()->get('db');
        return $db->fetchColumn($sql);

    }
    //根据id 获取详情
    public function getInfo($id){
        $sql = "select * from staff_apply where apply_id = {$id}";
        $db = $this->getDI()->get('db')->query($sql);
        return $db->fetch(\Phalcon\Db::FETCH_ASSOC);
    }

    //根据id 更新字段
    public function updateInfo($key, $data){
        if(empty($data))
            return false;

        $format = $this->formatUpdate($data);
        $up_sql = "update staff_apply set {$format} where apply_id = {$key}";
        $db = $this->getDI()->get('db');
        return  $db->execute($up_sql);
    }

    public function delInfo($id){
        if(empty($id))
           return false;
        $del_sql = "delete from staff_apply where apply_id = {$id}";
        $db = $this->getDI()->get('db');
        return  $db->execute($del_sql);

    }


    //紧急事故添加操作
    public function add_emergency($data){
        $db = $this->getDI()->get('db');
        try {
            $insetSql = $this->getInsertDbSql('emergency_record', $data);
            return $db->execute($insetSql);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * @description 获取申请对象信息
     * @param $audit_type
     * @param $audit_value
     * @param bool $is_lock
     * @return mixed
     */
    public function getApplyObject($audit_type, $audit_value, bool $is_lock = false)
    {
        return AuditApplyModel::findFirst([
            'conditions' => 'biz_type = :audit_type: and biz_value = :audit_value:',
            'bind'=> [
                'audit_type'  => $audit_type,
                'audit_value' => $audit_value,
            ],
            'for_update' => $is_lock,
        ]);
    }

    /**
     * 获取审批对象信息
     * @param $audit_type
     * @param $audit_value
     * @param bool $is_lock
     * @return mixed
     */
    public function getApprovalObject($audit_type, $audit_value, bool $is_lock = false)
    {
        return AuditApprovalModel::find([
            'conditions' => 'biz_type = :audit_type: and biz_value = :audit_value:',
            'bind'=> [
                'audit_type'  => $audit_type,
                'audit_value' => $audit_value,
            ],
            'for_update' => $is_lock,
        ]);
    }
}