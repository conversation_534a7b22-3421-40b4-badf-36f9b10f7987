<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\AuditCCModel;

class AuditApprovalRepository extends BaseRepository
{

    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 创建审批人
     * @param array $insert
     * @return void
     */
    public function InsertApproval($insert)
    {
        try {
            $this->batch_insert('staff_audit_approval', $insert);
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("auditApprovalRe:InsertApproval-" . $e->getMessage());
        }
    }

    /**
     * 获取审批数据
     * @param $params
     * @param $columns
     * @return array
     */
    public static function getAuditApprovalList($params, $columns = ['*'], $isDeleted = false)
    {
        if(empty($params['biz_type'])) {
            return [];
        }

        $conditions = 'biz_type = :biz_type:';
        $bind['biz_type'] = $params['biz_type'];

        if(!empty($params['state'])) {
            $conditions .= ' and state = :state:';
            $bind['state'] = $params['state'];
        }

        if(!empty($params['biz_value']) && !is_array($params['biz_value'])) {
            $conditions .= ' and biz_value = :biz_value:';
            $bind['biz_value'] = $params['biz_value'];
        }

        if(!empty($params['biz_value']) && is_array($params['biz_value'])) {
            $conditions .= ' and biz_value in ({biz_value:array})';
            $bind['biz_value'] = $params['biz_value'];
        }

        if($isDeleted) {
            $conditions .= ' and deleted = :deleted:';
            $bind['deleted'] = enums::DELETED_NO;
        }

        if(!empty($params['created_at'])) {
            $conditions .= ' and created_at < :created_at:';
            $bind['created_at'] = gmdate('Y-m-d H:i:s', strtotime($params['created_at']));
        }

        return AuditApprovalModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => $columns,
        ])->toArray();
    }

    public static function getAuditCCList($params, $columns = ['*'], $isDeleted = false)
    {
        if(empty($params['biz_type'])) {
            return [];
        }

        $conditions = 'biz_type = :biz_type:';
        $bind['biz_type'] = $params['biz_type'];

        if(!empty($params['follow_staff_id'])) {
            $conditions .= ' and follow_staff_id = :follow_staff_id:';
            $bind['follow_staff_id'] = $params['follow_staff_id'];
        }

        if(!empty($params['id'])) {
            $conditions .= ' and id = :id:';
            $bind['id'] = $params['id'];
        }

        if(!empty($params['biz_value'])) {
            $conditions .= ' and biz_value = :biz_value:';
            $bind['biz_value'] = $params['biz_value'];
        }

        if($isDeleted) {
            $conditions .= ' and deleted = :deleted:';
            $bind['deleted'] = enums::DELETED_NO;
        }

        return AuditCCModel::find([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();

    }
}
