<?php

namespace FlashExpress\bi\App\Repository;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrEconomyAbilityModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewModel;
use FlashExpress\bi\App\Models\backyard\HrJdModel;
use FlashExpress\bi\App\Models\backyard\HrLogModel;
use FlashExpress\bi\App\Models\backyard\HrResumeExtentModel;
use FlashExpress\bi\App\Models\backyard\HrResumeRecommendModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\InternalPositionHcModel;
use FlashExpress\bi\App\Models\backyard\SysProvinceModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\VehicleInfoModel;
use FlashExpress\bi\App\Models\backyard\SysCityModel;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\SysServer;
use FlashExpress\bi\App\Server\SysStoreServer;
use Phalcon\DiInterface;
use Phalcon\Paginator\Adapter\Model;

class ResumeRecommendRepository extends BaseRepository
{

    //by推荐简历表状态
    const RECOMMEND_STATE_EDITING = 1;//编辑中
    const RECOMMEND_STATE_SUBMITTED = 2;//待提交
    //type 页签类型  1 待提交 2 已提交 3 被驳回
    const PAGE_TYPE_SUBMITTED = 1;//待提交
    const PAGE_TYPE_SUBMIT = 2;//已提交
    const PAGE_TYPE_REJECT = 3;//被驳回

    //by推荐简历表是否删除
    const IS_DELETE_NO = 1;//未删除
    const IS_DELETE_YES = 2;//已删除

    //期待职位是否是快递职位
    const EXPROESS_JOB_YES = 1;//快递职位
    const EXPROESS_JOB_OTHER = 2;//其他职位

    public function initialize()
    {
        parent::initialize();
    }


    public function __construct($lang = 'zh-CN')
    {
        parent::__construct($lang);
    }

    public function getState(){
        $stateArr = [
            HrResumeModel::APPROVE_STATE_STAY=>$this->getTranslation()->_('resume_recommend_approve_state_'.HrResumeModel::APPROVE_STATE_STAY),
            HrResumeModel::APPROVE_STATE_PASS=>$this->getTranslation()->_('resume_recommend_approve_state_'.HrResumeModel::APPROVE_STATE_PASS),
            HrResumeModel::APPROVE_STATE_REJECT=>$this->getTranslation()->_('resume_recommend_approve_state_'.HrResumeModel::APPROVE_STATE_REJECT),
            HrInterviewModel::WAITING_INTERVIEW=>$this->getTranslation()->_('4802'),
            HrInterviewModel::INTERVIEW_IN_THE=>$this->getTranslation()->_('4803'),
            HrInterviewModel::INTERVIEW_ON_BEHALF_OFFER=>$this->getTranslation()->_('resume_recommend_status_'.HrInterviewModel::INTERVIEW_ON_BEHALF_OFFER),
            HrInterviewModel::INTERVIEW_LSSUED_OFFER=>$this->getTranslation()->_('resume_recommend_status_'.HrInterviewModel::INTERVIEW_LSSUED_OFFER),
            HrInterviewModel::INTERVIEW_REJECTED=>$this->getTranslation()->_('resume_recommend_status_'.HrInterviewModel::INTERVIEW_REJECTED),
            HrInterviewModel::INTERVIEW_CANCELLED=>$this->getTranslation()->_('resume_recommend_status_'.HrInterviewModel::INTERVIEW_CANCELLED),
            HrInterviewModel::INTERVIEW_DELETED=>$this->getTranslation()->_("4808"),
            HrInterviewModel::INTERVIEW_EMPLOYEED=>$this->getTranslation()->_('4809'),
        ];
        return $stateArr;
    }

    /**
     * 获取简历详情
     * @param $paramsIn
     */
    public function getInfo( $paramsIn ){

        $recommendId = $paramsIn['recommend_id'];
        $recommend = HrResumeRecommendModel::findFirst(
            [
                'conditions' => 'id = :id:',
                'bind' => [ 'id'=>$recommendId ],
            ]
        )->toArray();

        if( empty( $recommend ) ){
            return [];
        }
        $estimate_store_text = '';

        $storeInfo = (new SysStoreServer())->getStoreByid($recommend['estimate_store_id']);

        if( $recommend['estimate_store_id'] == -1 ){
            $estimate_store_text = enums::HEAD_OFFICE;
        }
        if( !empty( $storeInfo ) ){
            $estimate_store_text = $storeInfo['name'];
        }
        $job_seekers_info = json_decode($recommend['job_seekers_info'],true);
        $config        = $this->getDI()->getConfig();
        $img_prefix = $config->application['img_prefix'];
        if (isCountry("PH")){
            $phone = !empty( $recommend['phone'] ) ? substr($recommend['phone'],1) : '';
        }else{
            $phone = !empty( $recommend['phone'] ) ? $recommend['phone'] : '';
        }
        if (isCountry(['TH','MY'])){
            $recommendKdJd = (new SettingEnvServer())->getSetVal('h5_th_head_office_no_like_job');
        }else{
            $recommendKdJd = (new SettingEnvServer())->getSetVal('by_recommender_resume_jd_kd');
        }
        $recommendKdJd = !empty( $recommendKdJd ) ? explode(',',$recommendKdJd) : [] ;

        $hrJd = HrJdModel::findFirst([
            'conditions' => ' job_id = :job_id: ',
            'bind' => ['job_id' => $recommend['expect_job']],
        ]);


        $info = [
            'recommend_id' =>  $recommend['id'],
            'hc_id' =>  empty( $recommend['hc_id'] ) ? '' :$recommend['hc_id'] ,
            'resume_id' =>  $recommend['resume_id'],
            'last_name' =>  $recommend['last_name'],
            'first_name'=>  $recommend['first_name'],
            'phone'     =>  $phone,
            'estimate_store_id' =>  $recommend['estimate_store_id'],
            'estimate_store_text' => $estimate_store_text,
            'expect_job' =>  $recommend['expect_job'],
            'expect_job_name' =>  $hrJd ? $hrJd->job_name : '',
            'job_type' =>  in_array($recommend['expect_job'], $recommendKdJd) ? 1: 2,
            'is_owner_or_cr' =>  $recommend['is_owner_or_cr'],
            'reject_reason' =>  empty($recommend['reject_reason']) ? '' : $recommend['reject_reason'],
            'img_prefix'    => $img_prefix,
        ];

        if (isCountry(['TH','MY'])){
            $reserve_type_list = (new SysServer())->getReserveTypeList();
            $reserve_type_arr = array_column($reserve_type_list,"value","key");
            $info['reserve_type'] = $job_seekers_info['reserve_type'] ?? '';
            $info['reserve_type_name'] = isset($job_seekers_info['reserve_type']) ? $reserve_type_arr[$job_seekers_info['reserve_type']] : '';
        }

        $job_seekers_info['work_province_name'] = $job_seekers_info['work_city_name'] = '';

        if (!empty($job_seekers_info['work_province_code'])) {
            $province_info = SysProvinceModel::findFirst([
                'conditions' => 'code = :code:',
                'bind'       => ['code' => $job_seekers_info['work_province_code']],
                'columns'    => 'code,name',
            ]);

            $job_seekers_info['work_province_name'] = $province_info->name ?? '';

            if (!empty($job_seekers_info['work_city_code'])) {
                $city_info = SysCityModel::findFirst([
                    'conditions' => 'code = :code:',
                    'bind'       => ['code' => $job_seekers_info['work_city_code']],
                    'columns'    => 'code,name',
                ]);

                $job_seekers_info['work_city_name'] = $city_info->name ?? '';
            }
        }

        $info = array_merge($info,$job_seekers_info);
        return $info;

    }


    /**
     * 获取简历列表
     * @param $paramIn
     */
    public function getResumeRecommendList($paramIn){

        $noPage = $paramIn["no_page"] ?? 0;
        $pageNum = $paramIn["page_num"] ?? 1;
        $pageSize = $paramIn["page_size"] ?? 10;

        $returnData = [
            'list' => [],
            'pagination' => [
                'count' => 0,
                'pageCount' => 0,
                'pageNum' => $pageNum,
                'pageSize' => $pageSize,
            ],
        ];
        $userinfo = $paramIn['userinfo'];

        //获取待提交列表
        if(  $paramIn['type'] == self::PAGE_TYPE_SUBMITTED ){

            $data = $this->getSubmittedList($paramIn,$userinfo,$pageNum,$pageSize);

        }

        //获取已提交列表
        if( in_array($paramIn['type'] , [self::PAGE_TYPE_SUBMIT,self::PAGE_TYPE_REJECT] ) ){

            $data = $this->getSubmitList( $paramIn,$userinfo,$pageNum,$pageSize,$paramIn['type'] );

        }
        $returnData['list'] = $data['list'];
        $returnData['pagination']['count'] = $data['count'];
        $returnData['pagination']['pageCount'] = $data['pageCount'];
        return $returnData;
    }

    /**
     * 获取app 外层显示总数
     * @param $userInfo
     * @return array
     */
    public function getShowTotal($userInfo){

        //by_recommender_resume_positions
        //是否是网点主管角色
        $data['is_show'] = 0;
        $data['num']  = 0;

        if(!is_cli()){
            //演示APP 调用返回 0
            $headerData       = $this->request->getHeaders();
            if((isset($headerData['X-Demonstration-App']) || $this->request->get('x-demonstration-app'))){
                return $data;
            }

        }

        //获取推荐简历期待职位入口权限 配置岗位
        $recommendPositions = (new SettingEnvServer())->getSetVal('by_recommender_resume_positions');

        $recommendPositions = !empty( $recommendPositions ) ? explode(',',$recommendPositions) : [] ;
        $job_title = !empty( $userInfo['job_title'] ) ? $userInfo['job_title'] : '';
        //        foreach ( $positions as $k=>$v ){
        if( in_array( $job_title,$recommendPositions ) ){
            $data['is_show'] = 1;
            //                continue;
        } else if (in_array('ALL', $recommendPositions)) {
            $data['is_show'] = 1;
        }
        //        }

        //        if( in_array(RolesModel::ROLE_DOT_ADMIN,$userInfo['positions']) ){
        //            $data['is_show'] = 1;
        //        }
        $cnSub = $this->getResumeSumittedNum($userInfo);
        $cnRe = $this->getResumeRejectNum($userInfo);
        $data['num'] = $cnRe+$cnSub;
        return $data;

    }

    /**
     * 获取推荐简历驳回数量
     * @param $paramIn
     */
    public function getResumeRejectNum( $paramIn ){

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as total');
        $builder->from(['rr' => HrResumeRecommendModel::class])
            ->leftjoin(HrResumeModel::class,"rr.resume_id = resume.id","resume")
            ->where('rr.recommender_staff_id = :recommender_staff_id: and rr.is_delete = :is_delete:',
                ['recommender_staff_id' => $paramIn['staff_id'], 'is_delete'=>self::IS_DELETE_NO ]) ;
        $builder->andWhere('rr.state = :state:',['state'=>HrResumeRecommendModel::RECOMMEND_STATE_SUBMIT] );
        $builder->andWhere('resume.approve_state = :approve_state:',['approve_state'=>HrResumeModel::APPROVE_STATE_REJECT] );
        $totalInfo = $builder->getQuery()->getSingleResult();
        $count = intval($totalInfo->total);
        return $count;
    }

    /**
     * 获取待提交简历数量
     * @param $paramIn
     */
    public function getResumeSumittedNum($paramIn){
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as total');
        $builder->from(['rr' => HrResumeRecommendModel::class])
            ->where('rr.recommender_staff_id = :recommender_staff_id: and rr.is_delete = :is_delete:',
                ['recommender_staff_id' => $paramIn['staff_id'],'is_delete'=>self::IS_DELETE_NO]);
        $builder->inWhere('rr.state',[HrResumeRecommendModel::RECOMMEND_STATE_SUBMITTED,HrResumeRecommendModel::RECOMMEND_STATE_EDITING] );
        //获取总数
        $totalInfo = $builder->getQuery()->getSingleResult();
        $count = intval($totalInfo->total);
        return $count;
    }


    /**
     * 获取简历已提交 被驳回 列表
     * @param $paramIn
     * @param $userinfo
     * @param $pageNum
     * @param $pageSize
     * @return array
     */
    public function getSubmitList( $paramIn,$userinfo,$pageNum,$pageSize,$pageType ){

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as total');
        $builder->from(['rr' => HrResumeRecommendModel::class])
            ->leftjoin(HrResumeModel::class,"rr.resume_id = resume.id","resume")
            ->where('rr.recommender_staff_id = :recommender_staff_id: and rr.is_delete = :is_delete:',
                ['recommender_staff_id' => $userinfo['staff_id'], 'is_delete'=>self::IS_DELETE_NO ]) ;
        $builder->andWhere('rr.state = :state:',['state'=>HrResumeRecommendModel::RECOMMEND_STATE_SUBMIT] );

        if( $pageType == self::PAGE_TYPE_SUBMIT ){
            $builder->inWhere('resume.approve_state',[HrResumeModel::APPROVE_STATE_STAY,HrResumeModel::APPROVE_STATE_PASS] );
            $builder->orderBy('rr.submit_datetime desc');
        }
        if( $pageType == self::PAGE_TYPE_REJECT ){
            $builder->andWhere('resume.approve_state = :approve_state:',['approve_state'=>HrResumeModel::APPROVE_STATE_REJECT] );
            $builder->orderBy('rr.audit_time desc');
        }

        //获取总数
        $totalInfo = $builder->getQuery()->getSingleResult();
        $count = intval($totalInfo->total);
        $returnData['count'] = $count;
        $returnData['pageCount'] = ceil($count/$pageSize);
        $builder->columns(
            "rr.id as recommend_id,
	                rr.resume_id as rec_resume_id,
	                rr.state,
	                DATE_FORMAT( CONVERT_TZ( rr.audit_time, '+00:00', '" . $this->timezone . "' ), '%Y-%m-%d' ) AS audit_time,
	                DATE_FORMAT( CONVERT_TZ( rr.created_at, '+00:00', '" . $this->timezone . "' ), '%Y-%m-%d' ) AS created_at,
	                DATE_FORMAT( CONVERT_TZ( rr.updated_at, '+00:00', '" . $this->timezone . "' ), '%Y-%m-%d' ) AS updated_at,
	                DATE_FORMAT( CONVERT_TZ( rr.submit_datetime, '+00:00', '" . $this->timezone . "' ), '%Y-%m-%d' ) AS submit_datetime,
	                rr.phone,
	                rr.last_name,
	                rr.first_name,
	                resume.approve_state,
	                resume.id as resume_id,
	                resume.hc_id as hc_id,
	                resume.approve_reject_reason
	                "
        );
        $builder->limit($pageSize,($pageNum - 1) * $pageSize);
        $data = $builder->getQuery()->execute()->toArray();
        //查询简历表简历状态
        $resumeIds = array_column($data,'resume_id');
        $interverInfo = $resumeInfo = [];
        if( !empty( $resumeIds ) ){
            foreach ($data as $v){
                if ($v['resume_id'] && $v['hc_id']){
                    $_interverInfo = HrInterviewModel::findFirst(
                        [
                            'conditions'=>"resume_id = :resume_id: and hc_id = :hc_id:",
                            'bind'       => ['resume_id' => $v['resume_id'],'hc_id'=>$v['hc_id']],
                            'columns' => "resume_id,state",
                        ]
                    );
                    if (!empty($_interverInfo)){
                        $interverInfo[$v['resume_id']] = $_interverInfo->state;
                    }
                }
            }

            //查询简历状态是否淘汰
            $resumeInfo = HrResumeModel::find(
                [
                    'conditions'=>"id in ({resume_ids:array})",
                    'bind'       => ['resume_ids' => $resumeIds],
                    'columns' => "id,is_out",
                ]
            )->toArray();

            $resumeInfo = array_column($resumeInfo,'is_out','id');

        }
        $time = [];$submitTime = [];
        $stateArr = $this->getState();
        foreach ( $data as $k=>$val ){
            $time[] = $val['audit_time'];
            $submitTime[] = empty( $val['submit_datetime'] ) ? '' : $val['submit_datetime'];
            //处理by推荐简历列表展示状态
            $state = $val['approve_state'];
            if( !empty( $interverInfo ) ){

                if( !empty( $interverInfo[$val['resume_id']] ) && $interverInfo[$val['resume_id']] != HrInterviewModel::NO_COMMUNICATION ){
                    $state = $interverInfo[$val['resume_id']];
                }

            }
            $data[$k]['state'] = !empty( $stateArr[$state] ) ? $stateArr[$state] : '';

            if( !empty( $resumeInfo ) ){
                $isOut = [
                    HrResumeModel::RESUME_IS_OUT_YES => $this->getTranslation()->_('resume_is_out_'.HrResumeModel::RESUME_IS_OUT_YES),
                ];
                if( !empty( $resumeInfo[$val['resume_id']] ) && $resumeInfo[$val['resume_id']] == HrResumeModel::RESUME_IS_OUT_YES ){
                    $state = $isOut[ $resumeInfo[$val['resume_id']] ];
                    $data[$k]['state'] = $state;
                }

            }

            if (isCountry("PH")){
                $data[$k]['phone'] = !empty( $val['phone'] ) ? substr($val['phone'],1) : '';
            }else{
                $data[$k]['phone'] = !empty( $val['phone'] ) ? $val['phone'] : '';
            }

            $data[$k]['submit_datetime'] = !empty( $val['submit_datetime'] ) ? $val['submit_datetime'] : '';

        }


        if( $pageType == self::PAGE_TYPE_SUBMIT ){
            array_multisort($submitTime,SORT_DESC,$data);
        }
        if( $pageType == self::PAGE_TYPE_REJECT ){
            array_multisort($time,SORT_DESC,$data);
        }
        $returnData['list'] = $data;
        return $returnData;
    }

    /**
     * 获取待提交列表
     * @param $paramIn
     * @param $userinfo
     * @param $pageNum
     * @param $pageSize
     * @return array
     */
    public function getSubmittedList( $paramIn,$userinfo,$pageNum,$pageSize ){

        //获取展示状态
        $stateArr = [
            HrResumeRecommendModel::RECOMMEND_STATE_EDITING=>$this->getTranslation()->_('resume_recommend_status_'.self::RECOMMEND_STATE_EDITING),
            HrResumeRecommendModel::RECOMMEND_STATE_SUBMITTED=>$this->getTranslation()->_('resume_recommend_status_'.self::RECOMMEND_STATE_SUBMITTED),
        ];
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as total');
        $builder->from(['rr' => HrResumeRecommendModel::class])
            ->where('rr.recommender_staff_id = :recommender_staff_id: and rr.is_delete = :is_delete:',
                ['recommender_staff_id' => $userinfo['staff_id'],'is_delete'=>self::IS_DELETE_NO]);
        $builder->inWhere('rr.state',[self::RECOMMEND_STATE_EDITING,self::RECOMMEND_STATE_SUBMITTED]);
        $builder->orderBy('rr.updated_at desc');

        //获取总数
        $totalInfo = $builder->getQuery()->getSingleResult();
        $count = intval($totalInfo->total);
        $returnData['count'] = $count;
        $returnData['pageCount'] = ceil($count/$pageSize);
        $builder->columns(
            "rr.id as recommend_id,
	                rr.resume_id,
	                rr.state,
	                DATE_FORMAT( CONVERT_TZ( rr.audit_time, '+00:00', '" . $this->timezone . "' ), '%Y-%m-%d' ) AS audit_time,
	                DATE_FORMAT( CONVERT_TZ( rr.created_at, '+00:00', '" . $this->timezone . "' ), '%Y-%m-%d' ) AS created_at,
	                DATE_FORMAT( CONVERT_TZ( rr.updated_at, '+00:00', '" . $this->timezone . "' ), '%Y-%m-%d' ) AS updated_at,
	                rr.phone,
	                rr.last_name,
	                rr.first_name"
        );
        $builder->limit($pageSize,($pageNum - 1) * $pageSize);
        $data = $builder->getQuery()->execute()->toArray();

        foreach ( $data as $k=>$v ){
            $data[$k]['state'] = !empty( $stateArr[$v['state']] ) ? $stateArr[$v['state']] : '';
            if (isCountry("PH")){
                $data[$k]['phone'] = !empty( $v['phone'] ) ? substr($v['phone'],1) : '';
            }else{
                $data[$k]['phone'] = !empty( $v['phone'] ) ? $v['phone'] : '';
            }
        }
        $returnData['list'] = $data;

        return $returnData;
    }

    /**
     * 查询承诺书待签字
     * @param $userinfo
     * @return array
     */
    public function getResumeExtend($staff_id){

        //        $userinfo['staff_id'] = 118988;
        $entry_info = $this->getEntryInfo($staff_id);

        if( isset($entry_info['resume_id']) &&  $entry_info['resume_id'] ){

            $extend = HrResumeExtentModel::findFirst([
                'columns'    => 'commitment_sign_state,commitment_url,resume_id',
                'conditions' => 'resume_id = :resume_id:',
                'bind'       => [
                    'resume_id' => $entry_info['resume_id'],
                ],
            ]);
            $extend = !empty( $extend ) ? $extend->toArray() : [];
            return $extend;

        }
        return [];

    }

    /**
     * 获取推荐简历详情
     * @param $resume_id
     * @return array
     */
    public function getResumeRecommendInfo($resume_id){

        $recommend = HrResumeRecommendModel::findFirst([
            'columns'    => 'expect_job,resume_id',
            'conditions' => 'resume_id = :resume_id:',
            'bind'       => [
                'resume_id' => $resume_id,
            ],
        ]);
        $recommend = !empty( $recommend ) ? $recommend->toArray() : [];
        return $recommend;
    }


    /**
     * 获取入职表简历ID
     * @param $staff_id
     * @return int
     */
    public function getEntryInfo($staff_id){
        $entry = HrEntryModel::findFirst([
            'conditions' => 'staff_id = :staff_id: and status = :status:',
            'bind'       => [
                'staff_id' => $staff_id,
                'status'   => enums::STATUS_EMPLOYEED,
            ],
        ]);

        if($entry) return $entry->toArray();

        return [];
    }

    /**
     * 获取简历基本信息
     * @param $resume_id
     * @return array
     */
    public function getResumeInfo($resume_id){
        $hr_resume = HrResumeModel::findFirst([
            'conditions' => 'id = :resume_id:',
            'bind'       => [
                'resume_id'   => $resume_id,
            ],
        ]);
        $return_arr = [];

        if($hr_resume){
            $return_arr = $hr_resume->toArray();
        }

        $hr_resume_extend = HrResumeExtentModel::findFirst([
            'conditions' => 'resume_id = :resume_id:',
            'bind'       => [
                'resume_id'   => $resume_id,
            ],
        ]);

        if($hr_resume_extend){
            $return_arr = array_merge($return_arr,$hr_resume_extend->toArray());
        }

        return $return_arr;
    }

    /**
     * 获取车辆型号
     * @param $staff_id
     * @return int
     */
    public function getStaffVehicleModel($staff_id){
        $info_data = VehicleInfoModel::findFirst([
            'conditions' => 'uid = :uid: ',
            'bind' => ['uid' => $staff_id],
            'columns' => ['vehicle_type','vehicle_model', 'vehicle_brand','vehicle_brand_text','vehicle_model_text'],
        ]);

        if (!empty($info_data)) {
            return $info_data->toArray();
        }

        return [];
    }

    /**
     * 获取车牌号
     * @param $resume_id
     * @return string
     */
    public function getHrEconomyAbility($resume_id){
        $hr_economy_ability = HrEconomyAbilityModel::findFirst([
            'conditions' => 'resume_id = :resume_id: ',
            'bind' => ['resume_id' => $resume_id],

        ]);

        if (!empty($hr_economy_ability)) {
            return $hr_economy_ability->toArray();
        }

        return [];
    }

    public function getHcByStore($params){
        $page_size = !empty( $params['page_size'] ) ? $params['page_size'] : 10;
        $page_num = !empty( $params['page_num'] ) ? $params['page_num'] : 1;;
        $myStoreId = !empty( $params['store_id'] ) ? $params['store_id'] : '';
        if (!empty($params['recommend_source']) && $params['recommend_source'] == HrResumeModel::RECOMMEND_SOURCE_3){
            $info = $this->getHcByStoreCourierLogic($params);
        }else{
        $where = [
            'worknode_id' => $myStoreId,
            'state_code'  => HrHcModel::STATE_RECRUITING,
            'reason_type' => [HrHcModel::REASON_TYPE_RECRUIT, HrHcModel::REASON_TYPE_QUIT],
        ];

        $condition = "deleted = 1 and worknode_id = :worknode_id: and state_code = :state_code: and reason_type in  ({reason_type:array})";

        if (!empty($params['expect_job'])) {
            $condition       .= " AND job_id = :job_id:";
            $where['job_id'] = $params['expect_job'];
        }

        $info = HrHcModel::find(
            [
                'conditions' => $condition,
                'bind' => $where,
                'columns' => 'job_id,hc_id,priority_id,worknode_id',
                'order' => 'priority_id ASC,hc_id ASC',
//                'limit' => $page_size,
//                'offset' => ($page_num - 1) * $page_size
            ]
        );
        }
        $paginator = new Model(
            [
                'data'  => $info,
                'limit' => $page_size,
                'page'  => ($page_num - 1) * $page_size,
            ]
        );

        $estimate_store_text = '';
        $storeInfo = (new SysStoreServer())->getStoreByid($myStoreId);

        if( $myStoreId == -1 ){
            $estimate_store_text = enums::HEAD_OFFICE;
        }
        if( !empty( $storeInfo ) ){
            $estimate_store_text = $storeInfo['name'];
        }

        $job_ids = array_values(array_unique(array_column($info->toArray(), 'job_id')));
        $jd_list = $this->getHrJdByIds($job_ids);

        $returnData = [];
        foreach ( $info as $k=>$val ){
            $returnData[] =[
                'hc_id' => $val['hc_id'],
                'is_hot' => !empty($val['is_hot']) ? $val['is_hot'] : '',
                'job_name' => isset($jd_list[$val['job_id']]['job_name']) ? trim($jd_list[$val['job_id']]['job_name']) : "",
                'work_address' => $estimate_store_text,
                'estimate_store_id' => $myStoreId,
            ];
        }

        $page = [
            'page_size' => $page_size,
            'page_num' => $page_num,
            'count' => $paginator->getPaginate()->total_items,
            'page_count' => $paginator->getPaginate()->total_pages,
        ];
        $rst = [
            'page' => $page,
            'list' => $returnData ?? '',
        ];
        return $rst;
    }
    
    public function getHcByStoreCourierLogic($params)
    {
        $builder    = $this->modelsManager->createBuilder();
        $builder->columns('position_hc.is_hot,hc.hc_id,hc.job_id,hc.department_id,hc.worknode_id,hc.type,hc.surplusnumber,hc.demandnumber,hc.state_code,hc.priority_id,hc.job_title');
        $builder->orderby('position_hc.sort ASC,hc.priority_id ASC,hc.surplusnumber ASC,hc.hc_id desc');
        $builder->from(['position_hc' => InternalPositionHcModel::class]);
        $builder->leftjoin(HrHcModel::class, 'position_hc.hc_id = hc.hc_id', 'hc');
        $builder->where("hc.deleted = 1 and hc.state_code = :state_code: and hc.reason_type in  ({reason_type:array})",
            [
                'state_code'  => HrHcModel::STATE_RECRUITING,
                'reason_type' => [HrHcModel::REASON_TYPE_RECRUIT, HrHcModel::REASON_TYPE_QUIT],
            ]);
        $builder->andWhere('hc.worknode_id = :store_id:', ['store_id' => $params['store_id']]);
        if (!empty($params['expect_job'])) {
            $builder->andWhere('hc.job_id = :job_id:', ['job_id' => $params['expect_job']]);
        }
        return $builder->getQuery()->execute();
    }

    public function getHrJdByIds(array $job_ids): array
    {
        $jd_list = [];
        if (!empty($job_ids)) {
            $jd_list = HrJdModel::find([
                'conditions' => "job_id IN ({job_id:array}) AND state = 1",
                'bind'       => [
                    'job_id' => $job_ids,
                ],
                'columns'    => 'job_id,job_name',
            ])->toArray();
            $jd_list = array_column($jd_list, null, 'job_id');
        }
        return $jd_list;
    }

    /**
     * 其他网点列表
     * @param $params
     * @return array
     */
    public function getHcListV1($params)
    {
        $page_size = !empty($params['page_size']) ? $params['page_size'] : 10;
        $page_num  = !empty($params['page_num']) ? $params['page_num'] : 1;;
        $myStoreId = !empty($params['store_id']) ? $params['store_id'] : '';

        //获取网点信息
        $storeInfo = SysStoreModel::findFirst([
            'conditions' => "id = :store_id:",
            'bind'       => [
                'store_id' => $myStoreId,
            ],
        ]);
        //记录执行sql
        $this->logger->write_log(sprintf("getHcList-storeInfo: %s", $myStoreId ?? "store id is null"), 'info');

        // 第一步 模糊查询出网点
        $hc_ids = [];
        if (!empty($params['search_store'])) {
            // 模糊查询出符合条件的网点
            // 第一步关联hc的目的时 滤掉网点下没有hc的数据，减少后续的循环。
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('store.id,store.name');
            $builder->from(['store' => SysStoreModel::class]);
            $builder->leftjoin(HrHcModel::class, 'store.id = hc.worknode_id', 'hc');
            if (empty($params['recommend_source']) || $params['recommend_source'] != HrResumeModel::RECOMMEND_SOURCE_3){
                // 产品说 快递员推荐 也需要显示自己的网点数据
                $builder->andWhere("hc.worknode_id != :no_store_id:", ['no_store_id' => $myStoreId]);
            }
            $builder->andWhere('hc.state_code = :state_code:', ['state_code' => HrHcModel::STATE_RECRUITING]);
            $builder->inWhere('hc.reason_type', [HrHcModel::REASON_TYPE_RECRUIT, HrHcModel::REASON_TYPE_QUIT]);
            $builder->andWhere('store.name like :store_name:', ['store_name' => '%'.$params['search_store'].'%']);
            if (!empty($params['expect_job'])) {
                $builder->andWhere('hc.job_id = :hc_job_id:', ['hc_job_id' => $params['expect_job']]);
            }
            //$builder->groupBy('store.id');
            $store_list = $builder->getQuery()->execute()->toArray();
            $store_ids  = array_values(array_unique(array_column($store_list, 'id')));

            // 查找出每一个网点下的符合提交 且 排序后的第一条数据
            foreach ($store_ids as $key => $store_id) {
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('hc.surplusnumber,hc.approval_completion_time, hc.hc_id,hc.job_id,hc.department_id,hc.worknode_id,hc.city_code');
                $builder->from(['hc' => HrHcModel::class]);
                if (empty($params['recommend_source']) || $params['recommend_source'] != HrResumeModel::RECOMMEND_SOURCE_3){
                    $builder->andWhere("hc.worknode_id != :no_store_id:", ['no_store_id' => $myStoreId]);
                }
                $builder->andWhere('hc.state_code = :state_code:', ['state_code' => HrHcModel::STATE_RECRUITING]);
                $builder->inWhere('hc.reason_type', [HrHcModel::REASON_TYPE_RECRUIT, HrHcModel::REASON_TYPE_QUIT]);
                $builder->andWhere("hc.worknode_id = :store_id:", ['store_id' => $store_id]);
                if (!empty($params['expect_job'])) {
                    $builder->andWhere('hc.job_id = :hc_job_id:', ['hc_job_id' => $params['expect_job']]);
                }
                $builder->orderBy('hc.priority_id ASC, hc.surplusnumber DESC, hc.approval_completion_time ASC');
                $builder->limit(1);
                $data = $builder->getQuery()->execute()->toArray();
                if (isset($data[0]['hc_id'])) {
                    $hc_ids[] = $data[0]['hc_id'];
                }
            }

            // 没有找到hc 则返回空
            if (empty($hc_ids)) {
                return [
                    'page' => [
                        'page_size'  => $page_size,
                        'page_num'   => $page_num,
                        'count'      => 0,
                        'page_count' => 0,
                    ],
                    'list' => $returnData ?? [],
                ];
            }
        }

        $lng = $lat = 0;
        if (!empty($storeInfo)) {
            $storeInfo = $storeInfo->toArray();
            //网点地址经度
            $lng = $storeInfo['lng'];
            //网点地址纬度
            $lat = $storeInfo['lat'];
        }
        $builder  = $this->modelsManager->createBuilder();
        $distance = "ROUND(
		6378.138 * 2 * ASIN(
                SQRT(
                    POW( SIN( ( ".$lat." * PI() / 180 - store.lat * PI() / 180 ) / 2 ), 2 ) + COS( ".$lat." * PI() / 180 ) * COS( store.lat * PI() / 180 ) * POW( SIN( ( ".$lng." * PI() / 180 - store.lng * PI() / 180 ) / 2 ), 2 ) 
                ) 
            ) * 1000 
        ) AS distance ";
        $builder->columns('count(1) as total');
        if (!empty($params['recommend_source']) && $params['recommend_source'] == HrResumeModel::RECOMMEND_SOURCE_3){
            $builder->from(['position_hc' => InternalPositionHcModel::class]);
            $builder->leftjoin(HrHcModel::class, 'position_hc.hc_id = hc.hc_id', 'hc');
            $builder->leftjoin(SysStoreModel::class, 'store.id = hc.worknode_id', 'store');
            $builder->orderby('position_hc.sort ASC,hc.priority_id ASC,hc.surplusnumber desc,hc.hc_id desc');
        }else{
            $builder->from(['store' => SysStoreModel::class]);
            $builder->leftjoin(HrHcModel::class, 'store.id = hc.worknode_id', 'hc');
            $builder->orderBy('hc.priority_id ASC, hc.surplusnumber DESC, hc.approval_completion_time ASC');
            $builder->where("hc.worknode_id != :store_id:", ['store_id' => $myStoreId]);
        }
        $builder->andWhere('hc.state_code = :state_code:', ['state_code' => HrHcModel::STATE_RECRUITING]);
        $builder->inWhere('hc.reason_type', [HrHcModel::REASON_TYPE_RECRUIT, HrHcModel::REASON_TYPE_QUIT]);

        if (!empty($params['expect_job'])) {
            $builder->andWhere('hc.job_id = :hc_job_id:', ['hc_job_id' => $params['expect_job']]);
        }

        if (!empty($hc_ids)) {
            $builder->inWhere('hc.hc_id', $hc_ids);
        }

        // 为了准确计算总数，需要先查询所有符合条件的记录，然后应用having条件
        if (empty($params['search_store'])) {
            // 当没有搜索条件时，需要添加距离计算和having条件来保持与列表查询的一致性
            $builder->columns('hc.hc_id,'.$distance);
            if (!empty($params['recommend_source']) && $params['recommend_source'] == HrResumeModel::RECOMMEND_SOURCE_3){
                $builder->having('distance < 99999999999000');
            }else{
                $builder->having('distance < 30000');
            }
            $tempResult = $builder->getQuery()->execute()->toArray();
            $count = count($tempResult);
        } else {
            $totalInfo = $builder->getQuery()->getSingleResult();
            $count     = intval($totalInfo->total);
        }

        if (!empty($params['recommend_source']) && $params['recommend_source'] == HrResumeModel::RECOMMEND_SOURCE_3){
            $builder->columns('position_hc.is_hot,hc.hc_id,hc.hire_type,hc.job_id,hc.department_id,hc.worknode_id,store.id,store.name,hc.city_code,'.$distance);
            if (empty($params['search_store'])) {
                $builder->having('distance < 99999999999000');
            }
        }else{
            $builder->columns('hc.hc_id,hc.hire_type,hc.job_id,hc.department_id,hc.worknode_id,store.id,store.name,hc.city_code,'.$distance);
            if (empty($params['search_store'])) {
                $builder->having('distance < 30000');
            }
        }
        $builder->limit($page_size, ($page_num - 1) * $page_size);
        $data = $builder->getQuery()->execute()->toArray();

        $this->logger->write_log("getHcList-storeList: ".json_encode($builder->getQuery()->getSql(),
                JSON_UNESCAPED_UNICODE), 'info');
        $department_ids = array_column($data, 'department_id');
        $city_codes     = array_column($data, 'city_code');
        $departmentInfo = $cityInfo = [];
        if (!empty($department_ids)) {
            $departmentInfo = (new DepartmentRepository())->getDepartmentByIds($department_ids);
            $departmentInfo = array_column($departmentInfo, 'name', 'id');
        }
        if (!empty($city_codes)) {
            $cityInfo = (new SysCityModel())->getCitiesArrByCodeArr($city_codes, 'name,en_name,code');
        }

        $job_ids = array_values(array_unique(array_column($data, 'job_id')));
        $jd_list = $this->getHrJdByIds($job_ids);

        $returnData = [];
        foreach ($data as $key => $val) {
            $returnData[] = [
                'department'        => !empty($departmentInfo[$val['department_id']]) ? $departmentInfo[$val['department_id']] : '',
                'hc_id'             => $val['hc_id'],
                'hire_type'         => $val['hire_type'],
                'job_name'          => isset($jd_list[$val['job_id']]['job_name']) ? trim($jd_list[$val['job_id']]['job_name']) : '',
                'work_address'      => $val['name'],
                'estimate_store_id' => $val['worknode_id'],
                'city'              => !empty($cityInfo[$val['city_code']]) ? $cityInfo[$val['city_code']]['name'] : '',
                'distance'          => empty($val['distance']) ? '' : $val['distance'] / 1000,
                'is_hot'            => !empty($val['is_hot']) ? $val['is_hot'] : '',
            ];
        }
        $page = [
            'page_size'  => $page_size,
            'page_num'   => $page_num,
            'count'      => $count,
            'page_count' => ceil($count / $page_size),
        ];
        return [
            'page' => $page,
            'list' => $returnData ?? [],
        ];
    }

    /**
     * 获取日志信息
     * @param $conditions
     * @param $binds
     * @return mixed
     */
    public function getLogOneInfo($conditions, $binds)
    {
        $result =  HrLogModel::findFirst([
            'columns' => 'id, staff_info_id',
            'conditions' => $conditions,
            'bind' => $binds,
        ]);

        return !empty($result) ? $result->toArray() : [];
    }

}
