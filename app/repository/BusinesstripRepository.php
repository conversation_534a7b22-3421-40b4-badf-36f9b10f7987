<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\backyard\BusinessTripModel;

class BusinesstripRepository extends BaseRepository
{
    public $timezone;
    public function __construct($lang = 'zh-CN',$timezone)
    {
        parent::__construct($lang);
        $this->timezone =  $timezone;
    }
    public function initialize()
    {

        parent::initialize();
    }

    /**
     * 创建
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addTripR($data ,$tripImgData)
    {
        $insetData = $data;
        $db = $this->getDI()->get('db');
        // 插入
        try {
            $db->begin();
            // 动态生成SQL语句(另一种语法)
            $success = $db->insertAsDict('business_trip', $insetData);
            if (!$success) {
                $db->rollback();
                return false;
            }
            $tripId = $db->lastInsertId();
            $tripImgInster =array();
            //如果有上传附件图片
            if($tripImgData){
                foreach ($tripImgData as $k=>$v){
                    $tripImgInster[$k]['business_trip_id'] = $tripId;
                    $tripImgInster[$k]['img_path'] = $v;
                }
                $insetOrderDetail = $this->batch_insert('business_trip_img', $tripImgInster);
                if (!$insetOrderDetail) {
                    $db->rollback();
                    return false;
                }
            }

            $db->commit();
        } catch (\Exception $e) {
            echo $e->getMessage();
            $db->rollback();
            return false;
        }
        return $tripId;
    }

    /**
     * 出差记录
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getTripR($paramIn)
    {
        $id        = isset($paramIn['id']) ? $paramIn['id'] : $paramIn;
        $info_data = BusinessTripModel::findFirst([
            'conditions' => ' id= :id:',
            'bind'       => ['id' => $id],
        ]);
        $info_data = $info_data ? $info_data->toArray() : [];
        if (!empty($info_data)) {
            $add_hour                = $this->getDI()['config']['application']['add_hour'];
            $info_data['created_at'] = date('Y-m-d H:i:s', strtotime($info_data['created_at']) + $add_hour * 3600);
            $info_data['updated_at'] = date('Y-m-d H:i:s', strtotime($info_data['updated_at']) + $add_hour * 3600);
        }
        return $info_data;
    }
    /**
     * 出差记录附件图片
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getTripImgR($paramIn)
    {
        $id = isset($paramIn['id']) ? $paramIn['id'] : $paramIn;
        $sql = " SELECT * FROM business_trip_img where business_trip_id = {$id}  ;";
        $info_data = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $info_data;
    }

    /**
     * @param taff_audit_tool_log  日志表组装数据
     * @param business_trip更改状态 模型
     */
    public function cancel($log, $business_trip)
    {
        if ($log)
            $insetSql = $this->getInsertDbSql('staff_audit_tool_log', $log);
        $up_sql = "update business_trip set status = {$business_trip['status']} ,reason = '{$business_trip['reason']}' , approve_user = {$business_trip['uid']}, updated_at =  now() where id = '{$business_trip['id']}' ";
        $db = $this->getDI()->get('db');
        try {
            $db->begin();
            if (isset($insetSql) && $insetSql)
                $db->execute($insetSql);
            $db->execute($up_sql);
            $db->commit();
        } catch (\Exception $e) {
            $ControllerBase = new \FlashExpress\bi\App\Controllers\ControllerBase;
            $ControllerBase->wLog('记录失败日志', $log);
            $db->rollback();
            return false;
        }
        return true;
    }

    /**
     * @param taff_audit_tool_log  日志表组装数据
     */
    public function insertLog($tripArr = [], $uinfo = [])
    {
        $log['staff_id'] = $tripArr['apply_user'];     //申请人
        $log['type'] = 10;
        $log['original_type'] = $tripArr['status'];     //原始状态
        $log['to_status_type'] = $tripArr['status'];     //修改后状态
        $log['original_id'] = $tripArr['id'];     //关联id
        $log['operator'] = $uinfo['id'];     //操作人
        $log['operator_name'] = $uinfo['name'];     //操作人名称
        $insetSql = $this->getInsertDbSql('staff_audit_tool_log', $log);
        $db = $this->getDI()->get('db');
        $res = $db->execute($insetSql);
        return $res;
    }


    /**
     * 获取交通工具类型
     * @return array
     */
    public function getTransportationType()
    {
        $data = [
            '0'  => '', //没有
            '1'  => $this->getTranslation()->_('7111'),
            '2'  => $this->getTranslation()->_('7112'),
            '3'  => $this->getTranslation()->_('7113'),
            '4'  => $this->getTranslation()->_('7114'),
        ];
        return $data;
    }

    /**
     * 获取单程往返类型
     * @return array
     */
    public function getSingleroundtripType()
    {
        $data = [
            '1'  => $this->getTranslation()->_('7115'),
            '2'  => $this->getTranslation()->_('7116'),
        ];
        return $data;
    }


    //获取 该员工 指定日期 是否出差状态 考勤日历用
    public function trip_info($staff_id, $date){
        $sql = "select count(1) from business_trip 
        where apply_user = :staff_id and start_time <= :start_time and end_time >= :end_time
        and status = 2";
        $exist = $this->getDI()->get('db_rby')->fetchColumn($sql,['staff_id' => $staff_id,'start_time'=>$date,'end_time'=>$date]);
        return $exist;
    }

    /**
     * 获取出差详情
     * @param $staff_id
     * @param $date
     * @return array
     */
    public function getTripInfo($staff_id, $date): array
    {
        $model = BusinessTripModel::findFirst([
            'conditions' => 'apply_user = :staff_id: and start_time <= :start_time: and end_time >= :end_time: and status = 2',
            'bind'       => ['staff_id' => $staff_id, 'start_time' => $date, 'end_time' => $date],
        ]);
        if (empty($model)) {
            return [];
        }
        return $model->toArray();
    }

    /**
     * 获取出差详情
     * @param $staff_id
     * @param $date
     * @return array
     */
    public function getAllTripInfo($staff_id, $date): array
    {
        $model = BusinessTripModel::find([
            'conditions' => 'apply_user = :staff_id: and start_time <= :start_time: and end_time >= :end_time: and status = 2',
            'bind'       => ['staff_id' => $staff_id, 'start_time' => $date, 'end_time' => $date],
        ]);
        if (empty($model)) {
            return [];
        }
        return $model->toArray();
    }


    //获取 员工 区间时间 出差
    public function trip_dates($staff_id, $start, $end,string $status = '2'){
        $sql = "select start_time,end_time,business_trip_type,status from business_trip 
        where apply_user = {$staff_id} and status in ($status) and 
        (
            (start_time <= '{$start}' and end_time >= '{$start}') or 
            (start_time <= '{$end}' and end_time >= '{$end}') or
            (start_time <= '{$start}' and end_time >= '{$end}') or
            (start_time >= '{$start}' and end_time <= '{$end}')
        )
        ";
        $exist = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        $all_date = array();
        if(!empty($exist)){
            foreach ($exist as $row){
                 $circle_date = $row['start_time'];
                $all_date[$row['start_time']] =  ['business_trip_type'=>$row['business_trip_type'],'status'=>$row['status']];
                while ($circle_date < $row['end_time']){
                    $all_date[date('Y-m-d', strtotime("{$circle_date} +1 day"))] = ['business_trip_type'=>$row['business_trip_type'],'status'=>$row['status']];
                    $circle_date = date('Y-m-d', strtotime("{$circle_date} +1 day"));
                }
            }
        }
        return $all_date;
    }

}
