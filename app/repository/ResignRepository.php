<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\StaffLeaveReasonModel;
use MyCLabs\Enum\Enum;

class ResignRepository extends BaseRepository
{
    public $timezone;

    public function __construct($lang = 'zh-CN', $timezone = '+07:00')
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
    }

    /**
     * 出入离职申请
     */
    public function inserResign($paramIn = [])
    {
        try {
            $insertSql  = $this->getInsertDbSql('staff_resign', $paramIn);
            $this->getDI()->get('db')->query($insertSql);
            $auditId = $this->getDI()->get('db')->lastInsertId();
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("resignRe:inserResign-" . $e->getMessage());
            /* 有异常回滚 */
            $auditId = '';
        }
        return $auditId;
    }

    /**
     * 更新离职状态
     * @param int $auditId      主键id
     * @param int $status       审批状态
     * @param string $reason    驳回原因
     * @return boolean
     */
    public function updateResign($auditId, $status, $reason = '')
    {
        $update_sql =  "update staff_resign set status = '{$status}' , reject_reason = '{$reason}' where resign_id={$auditId}";
        $db = $this->getDI()->get('db');
        try{
            $db->begin();
            $db->execute($update_sql);
            $db->commit();
        }catch (\Exception $e){
            $db->rollback();
            $this->getDI()->get('logger')->write_log("resignRe:updateResign-" . $e->getMessage());
            return false;
        }
        return true;
    }

    /**
     * 更新离职审批人状态
     * @param array $paramIn    更新数据
     *        int   id          主键ID
     *        int   status      审批状态,见auditlistServer - status_array
     *        int   type        审批类型,见auditlistServer - type_array
     * @return boolean
     */
    public function updateResignApproval($paramIn =[])
    {
        $id     = $paramIn['id'] ?? 0;
        $status = $paramIn['status'] ?? 0;
        $type   = $paramIn['type'] ?? 0;
        $confirm= $paramIn['confirm'] ?? '';

        if (empty($id) || empty($status) || empty($type)) {
            return false;
        }

        $update_sql = "update staff_audit_approval set `status` = {$status}, `confirm` = '{$confirm}' where audit_id = {$id} and type = {$type} and status = 7";
        $db = $this->getDI()->get('db');
        try{

            $db->begin();
            $db->execute($update_sql);

            $db->commit();
            return true;
        }catch (\Exception $e){
            $db->rollback();
            $this->getDI()->get('logger')->write_log("ResignRepository:updateResignApproval:" . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取离职详情
     */
    public function getResignInfo($paramIn = [])
    {
        $resignId    = $paramIn['id'];

        $query_sql = "
            --
            select 
                resign_id
                ,serial_no
                ,submitter_id
                ,hire_date
                ,leave_date
                ,last_work_date
                ,resignation_notice
                ,resignation_work_day
                ,resignation_leave_day
                ,work_handover
                ,goods
                ,reason
                ,remark
                ,status
                ,DATE_FORMAT(CONVERT_TZ(created_at, '+00:00', '{$this->timezone}'), '%Y-%m-%d %H:%i:%s') AS created_at
                ,DATE_FORMAT(CONVERT_TZ(updated_at, '+00:00', '{$this->timezone}'), '%Y-%m-%d %H:%i:%s') AS updated_at
                ,cancel_reason
                ,reject_reason
                ,asset_tag
                ,leave_acceptance_book
                ,job_title as agent_job_title
                ,mobile as agent_mobile
                ,re_employment_hc_id
            from staff_resign
            where `resign_id` = '{$resignId}'";
        $data = $this->getDI()->get('db')->query($query_sql);
        $returnData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);

        $query_img_sql = "
                --
                select 
                    bucket_name
                    ,object_key
                from sys_attachment 
                where oss_bucket_key = {$resignId} 
                    and (oss_bucket_type = 'RESIGN_AUDIT' or oss_bucket_type = 'RESIGN_AUDIT_NW')
                    and deleted = 0 order by created_at DESC";
        $images = $this->getDI()->get('db')->query($query_img_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (!empty($images)) {
            $returnData['image_path'] = [];
            foreach ($images as $image) {
                if (isCountry('My')) {
                    $returnData['image_path'][] = env('img_prefix',
                            'http://fex-my-asset-dev.oss-ap-southeast-3.aliyuncs.com/') . $image['object_key'];
                } else {
                    $returnData['image_path'][] = convertImgUrl($image['bucket_name'], $image['object_key']);

                }
            }
        }
        //获取汽车图片
        $vehicle_images_path = $this->getSysAttachmentList($resignId);
        $returnData['vehicle_images_path'] = $vehicle_images_path;
        return $returnData;
    }

    /**
     * 获取正在审批或已经审批完成的请求数
     */
    public function getResignCnt($paramIn = [])
    {
        $leave_date = date('Y-m-d');
        $staff_info_id  = $paramIn['staff_info_id'];
        $sql = "
            --
            select 
                count(1) as cou
            from staff_resign 
            where 
                submitter_id = :staff_info_id and status in (1,2) and leave_date > :leave_date";
        $data = $this->getDI()->get('db')->query($sql, [
            'staff_info_id' => $staff_info_id,
            'leave_date'    => $leave_date,
        ])->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $data['cou'];
    }

    /**
     * 获取确认内容详情
     */
    public function getConfirmInfo($paramIn = [])
    {
        $id     = $paramIn['resign_id'];
        if (empty($id)){
            return [];
        }
        $query_sql = "
            --
            select
                level 
                ,confirm
            from staff_audit_approval
            where audit_id = {$id} and type = 13 order by id asc";
        $data = $this->getDI()->get('db')->query($query_sql);
        $returnData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        if (empty($returnData)) {
            return [];
        } else {
            return array_column($returnData, 'confirm', 'level');
        }
    }


    /**
     * 获取离职原因
     * @return array|mixed
     */
    public function getLeaveReasonMap()
    {
        $hcm_rpc = new ApiClient('hcm_rpc', '', 'get_leave_reason_map', $this->lang);
        $res     = $hcm_rpc->execute();
        return $res['result'] ?? [];
    }



    /**
     * 获取离职原因
     */
    public function getResignReason($notInCode=[])
    {
        $reasonMap = $this->getLeaveReasonMap();
        //上面这是新方法，从数据表中取，方便后续各国差异化
        $data = StaffLeaveReasonModel::find([
            'conditions' => 'type = :type: AND deleted=:deleted: and group_type not in ({group_type:array})',
            'bind'       => ['type' => Enums::STAFF_LEAVE_TYPE_1, 'deleted' => Enums::IS_DELETED_NO, 'group_type' => StaffLeaveReasonModel::$agent_leave_reason],
            'columns'    => ['code, t_key as reason'],
            'order'      => 'sort,id',
        ])->toArray();
        $list = [];
        foreach ($data as  $val) {
            if (in_array($val['code'], $notInCode)) {
                continue;
            }
            $list[] = [
                'code'   => (int)$val['code'],
                'reason' => $reasonMap[$val['code']] ?? $this->getTranslation()->_($val['reason']),
            ];
        }
        return $list;
    }

    /**
     * 获取离职原因
     */
    public function getAgentResignReason()
    {
        $data = StaffLeaveReasonModel::find([
            'conditions' => 'type = :type: AND deleted=:deleted: and group_type in ({group_type:array})',
            'bind'       => ['type' => Enums::STAFF_LEAVE_TYPE_1, 'deleted' => Enums::IS_DELETED_NO, 'group_type' => StaffLeaveReasonModel::$agent_leave_reason],
            'columns'    => ['group_type', 'code, t_key as reason'],
            'order'      => 'sort,id',
        ])->toArray();
        $list = [];
        foreach ($data as  $val) {
            $list[$val['group_type']]['group_type'] = $val['group_type'];
            $list[$val['group_type']]['group_type_text'] = $this->getTranslation()->_('resign_group_type_'.$val['group_type']);
            $list[$val['group_type']]['list'][] = [
                'code'   => (int)$val['code'],
                'reason' => $this->getTranslation()->_($val['reason']),
            ];
        }
        return array_values($list);
    }

    /**
     * 获取所有离职原因
     */
    public function getAllResignReason()
    {
        //上面这是新方法，从数据表中取，方便后续各国差异化
        $data = StaffLeaveReasonModel::find([
            'conditions' => 'type = :type:',
            'bind' => [ 'type'=>Enums::STAFF_LEAVE_TYPE_1 ],
            'columns' => [ 'code, t_key as reason' ],
            'order' => 'sort,id',
        ])->toArray();
        foreach ($data as $key=>$val){
            $data[$key]['code'] = (int)$val['code'];
            $data[$key]['reason'] = $this->getTranslation()->_($val['reason']);
        }
        return $data;
    }


    /**
     * 获取归还物品价格表
     */
    public function getGoodsPrice()
    {
        $data = [
            [
                'code'  => 1,
                'good'  => $this->getTranslation()->_('sim_card'),
                'price' => 3000,
            ],
            [
                'code'  => 2,
                'good'  => $this->getTranslation()->_('huawei_mobile_7pro'),
                'price' => 5000,
            ],
            [
                'code'  => 3,
                'good'  => $this->getTranslation()->_('idata_mobile'),
                'price' => 8000,
            ],
            [
                'code'  => 4,
                'good'  => $this->getTranslation()->_('TSH_mobile'),
                'price' => 500,
            ],
            [
                'code'  => 5,
                'good'  => $this->getTranslation()->_('bumbag'),
                'price' => 100,
            ],[
                'code'  => 6,
                'good'  => $this->getTranslation()->_('bluetooth_ptinter'),
                'price' => 3000,
            ],[
                'code'  => 7,
                'good'  => $this->getTranslation()->_('helmet'),
                'price' => 300,
            ],[
                'code'  => 8,
                'good'  => $this->getTranslation()->_('string_bag'),
                'price' => 100,
            ],
            [
                'code'  => 9,
                'good'  => $this->getTranslation()->_('scales'),
                'price' => 300,
            ],
            [
                'code'  => 10,
                'good'  => $this->getTranslation()->_('raincoat'),
                'price' => 200,
            ],
            [
                'code'  => 11,
                'good'  => $this->getTranslation()->_('motorcycle_bag'),
                'price' => '',
            ],
            [
                'code'  => 12,
                'good'  => $this->getTranslation()->_('motorcycle_boot'),
                'price' => '',
            ],
            [
                'code'  => 13,
                'good'  => $this->getTranslation()->_('jacket'),
                'price' => '',
            ],
            [
                'code'  => 14,
                'good'  => $this->getTranslation()->_('trousers'),
                'price' => '',
            ],
            [
                'code'  => 15,
                'good'  => $this->getTranslation()->_('t-shirt'),
                'price' => '',
            ],
            [
                'code'  => 16,
                'good'  => $this->getTranslation()->_('staff_card'),
                'price' => '',
            ],
            [
                'code'  => 17,
                'good'  => $this->getTranslation()->_('fuel_card'),
                'price' => '',
            ],
            [
                'code'  => 18,
                'good'  => $this->getTranslation()->_('laptop'),
                'price' => '',
            ],
            [
                'code'  => 19,
                'good'  => $this->getTranslation()->_('pc'),
                'price' => '',
            ],
            [
                'code'  => 20,
                'good'  => $this->getTranslation()->_('vehicle_sticker'),
                'price' => '',
            ],
            [
                'code'  => 21,
                'good'  => $this->getTranslation()->_('frame_box'),
                'price' => '',
            ],
            [
                'code'  => 99,
                'good'  => $this->getTranslation()->_('none'),
                'price' => '',
            ],
        ];
        return $data;
    }

    /**
     * 获取离职列表
     * @param array $paramIn 传入参数
     * @return array
     */
    public function getResignListByStatus($paramIn = []) :array
    {
        $status = $paramIn['status'] ?? 0;
        if (empty($status)) {
            return [];
        }

        $querySql = "
            --
            select 
                resign_id
                ,serial_no
                ,submitter_id
                ,hire_date
                ,hire_type
                ,leave_date
                ,last_work_date
                ,work_handover
                ,goods
                ,reason
                ,remark
                ,status
                ,DATE_FORMAT(CONVERT_TZ(created_at, '+00:00', '{$this->timezone}'), '%Y-%m-%d %H:%i:%s') AS created_at
                ,DATE_FORMAT(CONVERT_TZ(updated_at, '+00:00', '{$this->timezone}'), '%Y-%m-%d %H:%i:%s') AS updated_at
            from staff_resign
            where `status` = {$status} and hire_type in (13,14)";
        $data = $this->getDI()->get('db')->query($querySql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $data;
    }


    /**
     * 根据提交人id获取离职信息
     */
    public function getResignBySubmitterId($paramIn = [],$flag=false)
    {
        $submitter = $paramIn['submitter_id'] ?? '';
        if (empty($submitter)) {
            return [];
        }

        $querySql = "--
            SELECT 
                resign_id
                ,submitter_id
                ,hire_date
                ,leave_date
                ,last_work_date
                ,status
            from staff_resign
            where `submitter_id` = '{$submitter}'
        ";
        if($flag==true){
            $querySql.=" and (status =1 or status=2)
            order by resign_id desc
            limit 1";
        }else{
            $querySql.="
           order by resign_id desc
            limit 1";
        }
        $data = $this->getDI()->get('db')->query($querySql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $data;
    }


    /**
     * @description:获取离职申请的 图片
     * @param array $staff_info_id 传入参数 用户 id
     * @return array $returnData
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/6/24 17:40
     */
    public function getSysAttachmentList($staff_info_id)
    {

        $oss_bucket_type = ['RESIGN_VEHICLE_AUDIT'=>'vehicle_images','RESIGN_VEHICLE_TEST_AUDIT'=>'vehicle_test_images','RESIGN_VEHICLE_CLAIM_AUDIT'=>'vehicle_claim_images'];
        $sql = implode("','",array_flip($oss_bucket_type));
        $query_img_sql   = "
                --
                select 
                    bucket_name
                    ,object_key
                    ,oss_bucket_type
                from sys_attachment 
                where oss_bucket_key = {$staff_info_id}
                    and oss_bucket_type in ('{$sql}')
                    and deleted = 0";
        $images          = $this->getDI()->get('db')->query($query_img_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $returnData = ['vehicle_images'=>[],'vehicle_test_images'=>[],'vehicle_claim_images'=>[]];
        if (!empty($images)) {
            foreach ($images as $image) {
                array_push($returnData[$oss_bucket_type[$image['oss_bucket_type']]], convertImgUrl($image['bucket_name'], $image['object_key']));
            }
        }
        return $returnData;
    }

}
