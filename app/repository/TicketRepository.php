<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\HrEmailsModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Server\SettingEnvServer;

class TicketRepository extends BaseRepository
{
    private $db = null;

    public function __construct()
    {
        parent::__construct();
        $this->db = $this->getDI()->get("db");
    }


    public function initialize()
    {
        parent::initialize();
    }

    public function getNums($userInfo)
    {
        $data = [];
        $data['is_it'] = 0;
        $data['is_dm'] = 0;
        $data['reply_num'] = $this->getNum(enums::TICKET_STATUS_REPLY, $userInfo['id']);
        $data['audit_num'] = 0;

        if ($this->isIt($userInfo)) {
            $data['is_it'] = 1;
            $data['audit_num'] = $this->getNum(enums::TICKET_STATUS_WAIT_REPLY, $userInfo['id'], true);
        }
        return $data;
    }


    /**
     * 判断当前用户是否是IT部门
     * @param $userInfo
     * @return bool
     */
    public function isIt($userInfo)
    {
        /*if ($userInfo['organization_type'] != 2) {
            return false;
        }
        $department_id = $userInfo['organization_id'];
        $IT_department_id = env("IT_department_id", '21');
        $ids = (new DepartmentRepository())->getDepartIds($IT_department_id);
        if (in_array($department_id, $ids)) {
            return true;
        } else {
            return false;
        }*/
        //愿逻辑是只有泰国21部门就是it部门，现在为兼容各国差异化，通过setting_env配置判断是否有IT身份
        //*
        $audit_power = SettingEnvModel::findFirst([
            'conditions' => 'code = :code:',
            'bind' => [ 'code'=>enums::IT_TICKET_AUDIT_POWER ],
            'columns' => [ 'set_val' ],
        ])->toArray();
        $audit_power = json_decode($audit_power['set_val'], true);
        if( isset($audit_power['staff_ids']) && !empty($audit_power['staff_ids']) ){
            if( in_array($userInfo['id'], $audit_power['staff_ids']) ){
                return true;
            }
        }
        if( isset($audit_power['department_id']) && !empty($audit_power['department_id']) ){
            $department_ids = (new DepartmentRepository())->getDepartIds($audit_power['department_id']);
            if ( in_array($userInfo['organization_id'], $department_ids) && $userInfo['organization_type']==2 ) {
                return true;
            }
        }
        return false;
    }

    public function isDM($userInfo){
        $sql ="select  count(*) as num from sys_manage_piece   where manager_id =:staff_id";
        $sql2 ="select count(*) as num from sys_manage_region  where manager_id =:staff_id";
        $bind['staff_id'] = $userInfo['id'];
        $piece = $this->getDI()->get('db_rby')->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC, $bind);
        $region = $this->getDI()->get('db_rby')->fetchOne($sql2, \Phalcon\Db::FETCH_ASSOC, $bind);

        //新增逻辑，是否为ka 项目经理
        $is_project_manager = false;
        if(isCountry('TH') || isCountry('MY')) {
            $is_project_manager = $this->getKaManager($userInfo['id']);
        }

        $this->logger->write_log('[isDM]' . json_encode($userInfo), 'info');

        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind' => [ 'staff_info_id' => $userInfo['id'] ],
            'columns' => 'job_title'
        ]);
        $job_title = '';
        if( !empty( $staffInfo ) ){
            $staffInfo = $staffInfo->toArray();
            $job_title = $staffInfo['job_title'];
        }
        $settIng = (new SettingEnvServer())->getSetVal('cs_ticket_job_title_ids', ',');
        if (in_array($job_title, $settIng)) {
            return true;
        }

        $ticket_job_title = array_values(enums::$ticket_job_title);
        if($piece['num']>0 || $region['num'] > 0 || in_array( $job_title,$ticket_job_title ) || $is_project_manager){
           return true;
        }
        return false;
    }

    /**
     * 新增逻辑，是否为ka 项目经理
     * @param $userId
     * @return bool
     */
    public function getKaManager($userId)
    {
        $redis = $this->getDI()->get('redisLib');
        $list = $redis->get('get_project_manager_v1');
        if(empty($list)) {
            $list = $redis->get('get_project_manager_v2');
        }

        if(empty($list)) {
            $this->getDI()->get('logger')->write_log("API:FLE 获取 KA项目经理 为空，请检查...", 'notice');
            return false;
        }
        $kaManagerList = json_decode($list, true);

        return in_array($userId, $kaManagerList)  ? true : false;
    }

    public function getNum($status, $created_id, $audit = false)
    {
        $sql = "select count(*) as num from ticket where status=:status ";
        $bind['status'] = $status;
        if (!$audit) {
            $sql .= "and created_id=:created_id";
            $bind['created_id'] = $created_id;
        }
        $arr = $this->db->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC, $bind);
        return intval($arr['num']);
    }

    public function getNumByDay($day)
    {
        $sql = "select count(*) as num from ticket where created_at >= :begin_time and created_at <= :end_time ";
        $bind['begin_time'] = $day . " 00:00:00";
        $bind['end_time'] = $day . " 23:59:59";
        $arr = $this->db->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC, $bind);
        return intval($arr['num']);
    }


    public function add($paramIn)
    {
        $flag = $this->db->insertAsDict("ticket", $paramIn);
        $ticket_id = 0;
        if ($flag) {
            $ticket_id = $this->db->lastInsertId();
        }
        return $ticket_id;
    }

    public function update($ticket_id, $data)
    {
        return $this->db->updateAsDict("ticket", $data, ['conditions' => "id=?", 'bind' => [$ticket_id]]);
    }


    public function addLog($ticket_id, $data)
    {
        $data['ticket_id'] = $ticket_id;
        return $this->db->insertAsDict("ticket_log", $data);
    }

    /**
     * @param $paramIn 搜索条件
     * @param $audit true 工单列表 展示所有人  false 我的工单
     * @param $created_id  如果是我的工单 增加搜索自己申请的单子列表
     * @param $sort
     * @return array
     */
    public function list($paramIn, $audit, $created_id, $sort)
    {

        if (empty($paramIn['page_num'])) {
            $paramIn['page_num'] = 1;
        }

        if (empty($paramIn['page_size'])) {
            $paramIn['page_size'] = 20;
        }

        $pageNum = intval($paramIn['page_num']);
        $pageSize = intval($paramIn['page_size']);
        $start = ($pageNum - 1) * $pageSize;
        unset($paramIn['page_num'], $paramIn['page_size']);


//        $selectSql = "select * from ticket where 1=1 ";
        $countSql = "select count(*) as num from ticket t 
                      left join sys_store st on t.created_store_id = st.id 
                    ";


        $selectSql = "select 
                      t.*,st.category,min( tl.created_at ) first_deal_at 
                      from ticket t
                      left join sys_store st on t.created_store_id = st.id 
                      LEFT JOIN ticket_log tl ON t.id = tl.ticket_id AND t.first_deal_id = tl.created_id AND tl.status=2
                       ";


        $sql = " where 1=1 ";

        $orderSql = "";
        if (!empty($sort)) {
            $orderSql = " order by t.id desc ";
        }

        $limitSql = " limit $start,$pageSize";

        $bind = [];
        if (!$audit) {
            $sql .= " and t.created_id = :created_id";
            $bind['created_id'] = $created_id;
        }


        /**
         * oa 的搜索条件
        ticket_id: TH20220502
        item_type: 2
        created_store_id: TH01010115
        created_id: 20508
        status: 1
        updated_at_start: 2021-11-10 00:00:00
        updated_at_end: 2021-12-15 23:59:59
        created_at_start: 2021-11-09 00:00:00
        created_at_end: 2021-12-22 23:59:59
        pageSize: 100
        pageNum: 1
         *
         * -- by 增加的筛选 orz_type 1 网点 -1 总部
         */

        if(!empty($paramIn['ticket_id'])){
            $sql .= " and t.ticket_id = :ticket_id ";
            $bind['ticket_id'] = $paramIn['ticket_id'];
        }

        if(!empty($paramIn['updated_at_start'])){
            $sql .= " and t.updated_at >= :updated_at_start ";
            $bind['updated_at_start'] = $paramIn['updated_at_start'];
        }
        if(!empty($paramIn['created_at_start'])){
            $sql .= " and t.created_at >= :created_at_start ";
            $bind['created_at_start'] = $paramIn['created_at_start'];
        }

        if(!empty($paramIn['updated_at_end'])){
            $sql .= " and t.updated_at <= :updated_at_end ";
            $bind['updated_at_end'] = $paramIn['updated_at_end'];
        }
        if(!empty($paramIn['created_at_end'])){
            $sql .= " and t.created_at <= :created_at_end ";
            $bind['created_at_end'] = $paramIn['created_at_end'];
        }
        //工单状态
        if(!empty($paramIn['status'])){
            $sql .= " and t.status = :status ";
            $bind['status'] = $paramIn['status'];
        }
        //工单类型1
        if(!empty($paramIn['item_type'])){
//            $sql .= " and t.item_type in ({item_type:array}) ";
//            $bind['item_type'] = $paramIn['item_type'];

            $paramIn['item_type'] = array_map("intval",$paramIn['item_type']);
            $str = implode(',',$paramIn['item_type']);
            $sql .= " and t.item_type in ({$str}) ";
        }
        //申请人所属网点
        if(!empty($paramIn['created_store_id'])){
            $sql .= " and t.created_store_id = :created_store_id ";
            $bind['created_store_id'] = $paramIn['created_store_id'];
        }

        //申请人所属网点
        if(!empty($paramIn['created_id'])){
            $sql .= " and t.created_id = :created_id ";
            $bind['created_id'] = $paramIn['created_id'];
        }

        //store_category 需要关联网点表
        if(!empty($paramIn['store_category'])){
//            $sql .= " and st.category in ({store_category:array}) ";
//            $bind['store_category'] = $paramIn['store_category'];

            $paramIn['store_category'] = array_map("intval",$paramIn['store_category']);
            $str = implode(',',$paramIn['store_category']);
            $sql .= " and st.category in ({$str}) ";
        }

        if(!empty($paramIn['orz_type'])){
            //筛选总部
            if($paramIn['orz_type'] == '-1'){
                $sql .= " and t.created_store_id = :orz_type ";
                $bind['orz_type'] = $paramIn['orz_type'];
            }
            //非总部
            if($paramIn['orz_type'] == '1'){
                $sql .= " and t.created_store_id != :orz_type ";
                $bind['orz_type'] = '-1';
            }
        }

        // 设备所在网点
        if (!empty($paramIn['device_store_id'])) {
            $sql .= " and t.device_store_id = :device_store_id ";
            $bind['device_store_id'] = trim($paramIn['device_store_id']);
        }

//        foreach ($paramIn as $k => $v) {
//            if (empty($v)) {
//                continue;
//            }
//            switch ($k) {
//                case 'updated_at_start':
//                case 'created_at_start':
//                    $sql .= " and " . str_replace("_start", "", $k) . ">= :$k";
//                    $bind[$k] = $v;
//                    break;
//                case 'updated_at_end':
//                case 'created_at_end':
//                    $sql .= " and " . str_replace("_end", "", $k) . "<= :$k";
//                    $bind[$k] = $v;
//                    break;
//                default:
//                    $sql .= " and $k= :$k";
//                    $bind[$k] = $v;
//                    break;
//            }
//        }
//        echo $selectSql . $sql . $orderSql . $limitSql;exit;
//        var_dump($bind);exit;

        $groupSql = " GROUP BY t.id";

        $countArr = $this->db->fetchOne($countSql . $sql, \Phalcon\Db::FETCH_ASSOC, $bind);

        $temp = $this->db->query($selectSql . $sql . $groupSql . $orderSql . $limitSql, $bind)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        return ['dataList' => $temp, "pagination" => ['pageNum' => "" . $pageNum, "pageSize" => "" . $pageSize, "count" => $countArr['num']]];
    }

    public function detail($id, $audit, $created_id)
    {
        $sql = "select * from ticket where id=:id";
        $bind['id'] = $id;
        if (!$audit) {
            $sql .= " and created_id=:created_id";
            $bind['created_id'] = $created_id;
        }
        return $this->db->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC, $bind);
    }

    public function getLog($ticket_id)
    {
        //新的在前面
        $sql = "select * from ticket_log where ticket_id=" . $ticket_id . " order by id desc";
        return $this->db->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }


    public function getLogs($ticket_id, $pageNum, $pageSize)
    {
        if ($pageNum < 1) {
            $pageNum = 1;
        }

        if ($pageSize < 1) {
            $pageSize = 10;
        }

        $start = ($pageNum - 1) * $pageSize;

        $data = [];
        $sql = "select * from ticket_log where ticket_id=" . $ticket_id . " order by id desc limit " . $start . "," . $pageSize;
        $data['dataList'] = $this->db->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $data['pagination']['current_page'] = "" . $pageNum;
        $data['pagination']['per_page'] = "" . $pageSize;
        $sql = "select count(*) as num from ticket_log where ticket_id=" . $ticket_id;
        $data['pagination']['total_count'] = $this->db->fetchColumn($sql);

        return $data;
    }


    public function isAll($idArr)
    {
        $sql = "select count(*) as num from ticket where id in (" . getIdsStr($idArr) . ")";
        $arr = $this->db->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC);

        if ($arr['num'] == count($idArr)) {
            return true;
        } else {
            return false;
        }
    }

    public function batchClose($idArr, $mark, $created_id, $datetime)
    {
        $db = $this->db;

        try {
            // 开始事务
            $db->begin();

            $flag = $this->db->updateAsDict("ticket", ["status" => 3, "updated_at" => $datetime, "deal_id" => $created_id], ['conditions' => "id in (" . getIdsStr($idArr) . ")"]);

            if ($flag) {
                $insert = [];
                foreach ($idArr as $k => $v) {
                    $temp = [];
                    $temp['ticket_id'] = $v;
                    $temp['created_id'] = $created_id;
                    $temp['created_type'] = 2;
                    $temp['mark'] = $mark;
                    $temp['status'] = 3;
                    $temp['created_at'] = $datetime;
                    $insert[] = $temp;
                }
                $this->batch_insert("ticket_log", $insert);
            }
            $db->commit();
            $this->closeEmail($idArr);
        } catch (\Exception $e) {
            $db->rollback();
            return false;
        }
        return true;
    }

    /**
     * 关闭staff bi hr_emails的邮箱
     * @param [],or string
     */
    public function closeEmail($ticket_id)
    {
        if (is_array($ticket_id)) {
            $where = "id in (" . getIdsStr($ticket_id) . ")";
        } else {
            $where = "id = " . $ticket_id;
        }
        $sql = "select *  from ticket where " . $where;
        $list = $this->db->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC);
        foreach ($list as $v) {
            if ($v['item_type'] == 5 && $v['created_id'] == 10003) {
                $info = explode('-', $v['info']);
                $staff_id = $info[1];
                //bi.库关闭对应的邮箱
                if (isset($staff_id)) {
                    $model = HrEmailsModel::findFirst('staff_info_id=' . $staff_id);
                    if ($model) {
                        $model->state = 2;
                        $model->save();
                        $this->getDI()->get('logger')->write_log("close ticket email to hr_emails success staff_id:" . $staff_id, 'info');
                    }
                }

            }
        }
    }


    public function getItemType($id)
    {
        $sql = "select * from ticket_item_type where id=:id";
        $bind['id'] = $id;
        return $this->db->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC, $bind);
    }


    public function getItemTypes()
    {
        $sql = "select * from ticket_item_type order by `sort`";
        return $this->db->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC);
    }

}