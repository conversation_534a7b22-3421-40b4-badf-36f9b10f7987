<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrAnnexModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\HrResumeExtentModel;
use FlashExpress\bi\App\Models\backyard\HrResumeFilterModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;

class ResumeRepository extends BaseRepository {

    public $timezone;

    public function __construct($timezone) {
        parent::__construct();
        $this->timezone = $timezone;
    }

    /**
     * 设置简历状态为淘汰
     * @param type $params
     * @return boolean
     */
    public function out($params = []) {

        //$out_type = isset($params['out_type']) ? $params['out_type'] : 0;
        //$out_reason = isset($params['out_reason']) ? $params['out_reason'] : '';

        $hr_resume_model = HrResumeModel::findFirst([
                    'conditions' => ' id = :id:',
                    'bind' => [
                        'id' => $params['id']
                    ]
        ]);

        if ($hr_resume_model) {
            //因为$hr_resume_model->source 的结果是hr_resume表名，会把hr_resume表的source字段置为null，所以更改更新方式，同时不再更新不通过原因
            $db = $this->getDI()->get('db');
            $db->updateAsDict( 'hr_resume', [
                    'is_out' => 1,
                    'state_code' => 2,
                ], ['conditions' => 'id = ?',
                    'bind'       => [$params['id']],]
            );

            //不通过原因写入type_record表
            if( !empty($params['out_type_list']) ){
                $batch_data = [];
                foreach ($params['out_type_list'] as $key=>$val){
                    $batch_data[] = [
                        'business_type' => enums::RESUME_PASS_BUSINESS_TYPE,
                        'business_id' => $params['id'],
                        'resume_id' => $params['id'],
                        'out_type' => enums::INTERVIEW_OUT_TYPE_1,
                        'deleted' => 0,
                        'pass_type' => $val['pass_type'],
                        'pass_reason' => $val['out_type'],
                        'pass_remark' => !empty($val['pass_remark']) ? $val['pass_remark'] : '',
                    ];
                }
                if( $this->db->updateAsDict( 'hr_interview_pass_type_record', ['deleted'=>enums::DELETED_YES], "resume_id=".$params['id']) ){
                    $this->batch_insert('hr_interview_pass_type_record', $batch_data);
                }
            }

        }

        return ["resume_id"=>$hr_resume_model->id,"hc_id"=>$hr_resume_model->hc_id];
    }


    /**
     * 简历管理-获取附件列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getAnnexList($resume_id)
    {
        if(!$resume_id)
            return [];

        $builder = $this->modelsManager->createBuilder();
        $builder->columns("main.id,
                    main.first_name,
                    main.last_name,
                    main.credentials_num,
                    b.object_key,
                    b.original_name,
                    b.file_type ");
        $builder->from(['main' => HrResumeModel::class]);
        $builder->leftjoin(HrAnnexModel::class, 'main.id=b.oss_bucket_key', 'b');
        $builder->andWhere('b.type = 1');
        $builder->andWhere('b.deleted = 0');
        $builder->andWhere("b.file_type = 16");
        $builder->andWhere("main.id = :id:", ['id' => $resume_id]);
        $resule = $builder->getQuery()->execute()->getFirst();

        return $resule ? $resule->toArray() : [];
    }


    /**
     * 获取简历信息
     * @param $resume_id
     * @return mixed
     */
    public function getResumeInfoById($resume_id){

        return  HrResumeModel::findFirst([
            'conditions' => 'id =:id:',
            'bind' => ['id' => $resume_id],
        ]);
    }


    /**
     * 获取筛选简历列表
     * @param $params
     * @return array
     */
    public function getResumeFilterList($params)
    {
        $returnData = [
            'list' => [],
            'total'=> 0,
        ];

        $builder = $this->modelsManager->createBuilder();

        $builder->columns("count(1) as total");
        $builder->from(['main' => HrResumeFilterModel::class]);
        $builder->leftjoin(HrResumeModel::class, 'main.resume_id=resume.id', 'resume');
        $builder->leftjoin(HrHcModel::class, 'resume.hc_id=hc.hc_id', 'hc');
        $builder->andWhere("main.is_deleted = :is_deleted:",['is_deleted'=>HrResumeFilterModel::IS_DELETED_NO]);

        if(!empty($params['staff_info_id'])){
            $builder->andWhere("main.filter_staff_id = :staff_id:",['staff_id'=>$params['staff_info_id']]);
        }
        if(!empty($params['keyword'])){
            $builder->andWhere('main.hc_id = :hc_id: or resume.name like :name:', ['hc_id' => $params['keyword'],'name' => '%' . $params['keyword'] . '%']);
        }

        if(!empty($params['type'])){
            $filter_state_map=[
                1=>[HrResumeFilterModel::FILTER_STATE_STAY],
                2=>[HrResumeFilterModel::FILTER_STATE_PASS,HrResumeFilterModel::FILTER_STATE_REJECT],
                3=>[HrResumeFilterModel::FILTER_STATE_CANCEL],
            ];
            $builder->inWhere('main.filter_state', $filter_state_map[$params['type']]);
        }

        if(!empty($params['sub_type'])){
            $builder->andWhere('main.filter_state = :filter_state: ', ['filter_state' => $params['sub_type']]);
        }


        $totalInfo = $builder->getQuery()->getSingleResult();
        $returnData['total'] = intval($totalInfo->total);

        if (!$returnData['total']) {
            return  $returnData;
        }

        $builder->columns(
            "main.id,
            main.recommender_staff_id,
            main.filter_state,
            main.hc_id,
            main.recommend_time,
            DATE_FORMAT(CONVERT_TZ(main.recommend_time, '+00:00', '{$this->timezone}'),'%Y-%m-%d %H:%i:%s') as recommend_time,
            resume.name,
            resume.first_name,
            resume.last_name,
            hc.worknode_id,
            hc.job_title
            "
        );

        if(isset($params['page']) && isset($params['size'])){
            $page = intval($params['page']);
            $size = intval($params['size']);
            $offset = $size * ($page - 1);
            $builder->limit($size, $offset);
        }
        $builder->orderBy('main.id DESC');
        $returnData['list'] = $builder->getQuery()->execute()->toArray();

        return $returnData;
    }


    /**
     *  获取简历筛选表信息
     * @param $params
     * @return
     */
    public function getResumeFilterInfo($params)
    {

        $builder = $this->modelsManager->createBuilder();


        $builder->from(['main' => HrResumeFilterModel::class]);
        $builder->andWhere("main.is_deleted = :is_deleted:",['is_deleted'=>HrResumeFilterModel::IS_DELETED_NO]);
        if(!empty($params['id'])){
            $builder->andWhere("main.id = :id:",['id'=>$params['id']]);
        }
        if(!empty($params['staff_info_id'])){
            $builder->andWhere("main.filter_staff_id = :filter_staff_id: or main.recommender_staff_id = :recommender_staff_id:",['filter_staff_id'=>$params['staff_info_id'],'recommender_staff_id'=>$params['staff_info_id']]);
        }
        return $builder->getQuery()->execute()->getFirst();
    }

    /**
     * 获取筛选官指定状态的筛选简历数据和
     * @param $staff_info_id
     * @param array $filter_state
     * @return array
     */
    public function getStaffResumeFilterNum($staff_info_id, array $filter_state=[]): array
    {

        $builder = $this->modelsManager->createBuilder();
        $builder->columns("count(1) as total,filter_state");
        $builder->from(['main' => HrResumeFilterModel::class]);
        $builder->andWhere("main.filter_staff_id = :filter_staff_id: and main.is_deleted = :is_deleted:",['filter_staff_id'=>$staff_info_id,'is_deleted'=>HrResumeFilterModel::IS_DELETED_NO]);
        if($filter_state){
            $builder->inWhere('main.filter_state', $filter_state);
        }
        $builder->groupby('main.filter_state');
        return $builder->getQuery()->execute()->toArray();
    }


    public static function getExtentOne($params, $columns = ['*'])
    {
        if(empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind = [];


        if(!empty($params['resume_id'])) {
            $conditions .= ' and resume_id = :resume_id:';
            $bind['resume_id'] = $params['resume_id'];
        }

        $data = HrResumeExtentModel::findFirst([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);

        return empty($data) ? [] : $data->toArray();
    }
}
