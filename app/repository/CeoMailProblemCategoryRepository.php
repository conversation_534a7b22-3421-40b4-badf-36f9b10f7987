<?php
/**
 * Author: Bruce
 * Date  : 2024-03-04 19:45
 * Description:
 */

namespace FlashExpress\bi\App\Repository;


use FlashExpress\bi\App\Models\backyard\CeoMailFollowModel;
use FlashExpress\bi\App\Models\backyard\CeoMailProblemCategoryModel;
use FlashExpress\bi\App\Models\backyard\CeoMailProblemCategoryRelationModel;
use FlashExpress\bi\App\Models\backyard\CeoMailStaffProblemOrderModel;

class CeoMailProblemCategoryRepository extends BaseRepository{

    public function __construct($lang = 'zh-CN',$timezone)
    {
        parent::__construct($lang);
        $this->timezone =  $timezone;
    }

    public static function getCategoryList($columns = ['*'], $conditions = '', $bind = [], $order = '')
    {
        return CeoMailProblemCategoryModel::find([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
            'order'      => $order,
        ])->toArray();
    }

    public static function getCategoryOne($columns = ['*'], $conditions = '', $bind = [])
    {
        $data = CeoMailProblemCategoryModel::findFirst([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);

        return empty($data) ? [] : $data->toArray();
    }

    /**
     * 获取工单
     * @param array $columns
     * @param string $conditions
     * @param array $bind
     * @return array
     */
    public static function getOrderOne($columns = ['*'], $conditions = '', $bind = [])
    {
        $data = CeoMailStaffProblemOrderModel::findFirst([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);

        return empty($data) ? [] : $data->toArray();
    }

    public static function getFollowOne($params, $columns = ['*'])
    {
        if(empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind = [];

        if(!empty($params['follow_staff_id'])) {
            $conditions .= ' and follow_staff_id = :follow_staff_id:';
            $bind['follow_staff_id'] = $params['follow_staff_id'];
        }

        if(!empty($params['id'])) {
            $conditions .= ' and id = :id:';
            $bind['id'] = $params['id'];
        }

        $data = CeoMailFollowModel::findFirst([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);

        return empty($data) ? [] : $data->toArray();
    }


    public static function getCategoryRelationList($params, $isOnlyId = false)
    {
        if(empty($params)) {
            return [];
        }

        $conditions = 'deleted = :deleted:';
        $bind['deleted'] = CeoMailProblemCategoryRelationModel::DELETED_NO;

        if(!empty($params['module_type'])) {
            $conditions .= ' and module_type = :module_type:';
            $bind['module_type'] = $params['module_type'];
        }

        $list = CeoMailProblemCategoryRelationModel::find([
            'columns'    => ['*'],
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();

        if($isOnlyId && !empty($list)) {
            return array_column($list, 'category_id');
        }

        return $list;
    }



}