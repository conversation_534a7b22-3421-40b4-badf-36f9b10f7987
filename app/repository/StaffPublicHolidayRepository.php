<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\backyard\StaffPublicHolidayModel;

class StaffPublicHolidayRepository extends BaseRepository
{

    public function __construct($lang = 'zh-CN', $timezone)
    {
        parent::__construct($lang);
        $this->timezone = $timezone;
    }

    /**
     * 获取员工个人的公共假期
     * @param int $staff_info_id
     * @param string $start_date
     * @return array
     */
    public function getStaffData( $staff_info_id, $start_date = ''): array
    {
        if(empty($staff_info_id)){
            return [];
        }

        $conditions = 'staff_info_id = :staff_info_id: AND is_deleted = 0 AND date_at >= :date_at:';
        if (empty($start_date)) {
            $start_date = date('Y-m-d', strtotime('-1 years'));
        }
        $bind = ['staff_info_id' => $staff_info_id, 'date_at' => $start_date];
        return  StaffPublicHolidayModel::find([
            'columns'    => 'date_at,date_at as day,staff_info_id',
            'conditions' => $conditions,
            'bind'       => $bind,

        ])->toArray();

    }

    /**
     * 获取多个员工个人的公共假期
     * @param array $staff_info_ids
     * @param string $start_date
     * @return array
     */
    public function getMultiStaffData(array $staff_info_ids, string $start_date = ''): array
    {
        $conditions = 'staff_info_id in ({staff_info_id:array}) AND is_deleted = 0 AND date_at >= :date_at:';
        if (empty($start_date)) {
            $start_date = date('Y-m-d', strtotime('-1 years'));
        }
        $bind = ['staff_info_id' => $staff_info_ids, 'date_at' => $start_date];
        $data = StaffPublicHolidayModel::find([
            'columns'    => 'date_at,staff_info_id',
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();

        $res = [];
        foreach ($data as $datum) {
            $res[$datum['staff_info_id']][] = $datum['date_at'];
        }
        return $res;
    }

}
