<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffTransferModel;

class HrStaffTransferRepository extends BaseRepository
{


    /**
     * 获取网点指定日期指定范围在职的员工数
     * @param $store_id
     * @param $job_title
     * @param $date_list
     * @return mixed
     */
    public static function getStoreStaffOnJobNum($store_id, $job_title, $date_list)
    {
        return HrStaffTransferModel::find([
            'conditions' => "stat_date in ({stat_date:array}) AND store_id = :store_id: AND job_title in ({job_title:array}) AND formal = :formal: AND state = :state: ",
            'columns'    => 'stat_date, count(1) AS total',
            'bind'       => [
                'job_title' => $job_title,
                'store_id'  => $store_id,
                'stat_date' => $date_list,
                'formal'    => HrStaffInfoModel::FORMAL_1,
                'state'     => HrStaffInfoModel::STATE_ON_JOB,
            ],
            'group'      => 'stat_date',
        ])->toArray();
    }

    /**
     * 获取网点指定日期指定范围在职 待离职，停职 的员工数
     * 编制 + 实习生
     * @param $store_id
     * @param $date_list
     * @return mixed
     */
    public static function getStoreStaffNum($store_id, $date_list)
    {
        return HrStaffTransferModel::find([
            'conditions' => "stat_date in ({stat_date:array}) AND store_id = :store_id: AND formal in ({formal:array}) AND state in ({state:array}) ",
            'columns'    => 'stat_date, count(1) AS total',
            'bind'       => [
                'store_id'  => $store_id,
                'stat_date' => $date_list,
                'formal'    => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN],
                'state'     => [HrStaffInfoModel::STATE_ON_JOB, HrStaffInfoModel::STATE_SUSPENSION],
            ],
            'group'      => 'stat_date',
        ])->toArray();
    }


}
