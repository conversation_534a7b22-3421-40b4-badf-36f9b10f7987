<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrInterviewOperationModel;
use FlashExpress\bi\App\Models\backyard\HrJobDepartmentRelationModel;
use FlashExpress\bi\App\Models\backyard\HRStaffingModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewInfoModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewSubscribeModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewFileModel;
use FlashExpress\bi\App\Models\backyard\HrHcModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Server\MessageQueueServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\SalaryServer;


class InterviewRepository extends BaseRepository {

    public $timezone;
    public $db;

    public function __construct($timezone) {
        parent::__construct();
        $this->timezone = $timezone;
        $this->db = $this->getDI()->get('db');
    }

    /**
     * 面试列表
     * @param array $params Description
     * @return array Description
     */
    public function getInterviewList($params) {



        $builder = $this->modelsManager->createBuilder();
        $builder->columns(
                "b.name,b.first_name_en,b.last_name_en,main.hc_id,main.interview_id,main.resume_provider_id,main.id as subscribe_id,
                DATE_FORMAT(CONVERT_TZ(o.interview_time, '+00:00', '{$this->timezone}'),'%Y-%m-%d %H:%i:%s') as interview_time,
                o.detail_address,
                f.interview_info_id,f.state,
                main.staff_id,
                g.job_title,
                f.interview_info_id,
                f.created_at as info_created_at,
                if(ISNULL(o.cancel_at), '', o.cancel_at) as opt_cancel_at,
                main.status,
                o.create_id as create_id,
                o.state as operation_state,
                main.interview_back,
                DATE_FORMAT(CONVERT_TZ(o.created_at,  '+00:00', '{$this->timezone}'),'%Y-%m-%d %H:%i:%s') as created_at ,o.id as opt_id"
        );
        $builder->from(['o' => HrInterviewOperationModel::class]);
        $builder->leftjoin(HrInterviewSubscribeModel::class, 'main.id=o.interview_sub_id', 'main');
        $builder->innerjoin(HrInterviewModel::class, 'main.interview_id=a.interview_id', 'a');
        $builder->leftjoin(HrResumeModel::class, 'a.resume_id=b.id', 'b');
        $builder->leftjoin(HrHcModel::class, 'main.hc_id=g.hc_id', 'g');
        $builder->leftjoin(HrInterviewInfoModel::class, 'o.id=f.opt_id', 'f');
        //搜索关键词
        if ($params['keyword']) {
            $builder->andWhere('main.hc_id = :hc_id: or b.name like :keyword:',
                ['hc_id' => $params['keyword'], 'keyword' => '%' . $params['keyword'] . '%']);
        }

        switch ($params['type']){
            case 0://面试状态为待反馈
                $builder->andWhere('o.state  = 0 ');
                break;
            case 1://面试状态为已反馈
                $builder->andWhere('o.state in (1,2)');
                //面试结果筛选 1:通过 2:不通过
                if(isset($params['is_pass'])){
                    if ($params['is_pass'] == 1) {
                        $builder->andWhere('o.state  = 1');
                    }elseif($params['is_pass'] == 2){
                        $builder->andWhere('o.state = 2');//不通过
                    }
                }
                break;
            case 2://面试状态为已取消
                $builder->andWhere('(main.status =2 and o.state = 3) or (main.status in (1,2) and main.interview_back = '.HrInterviewSubscribeModel::INTERVIEW_BACK_YES.')');
                break;
            default:
                $builder->andWhere('f.interview_info_id is null');
        }

        //面试官为当前用户
        $builder->andWhere('o.interviewer_id = :staff_id:', ['staff_id' => $params['staff_id']]);
        $builder->groupby('o.id');

        if ($params['type'] == 1) {
            $builder->orderby('f.created_at desc');
        } elseif ($params['type'] == 2) {
            $builder->orderby('opt_cancel_at DESC,main.id desc');
        } elseif ($params['type'] == 0) {
            $builder->orderby('o.interview_time ASC ,main.id desc');
        } else {
            $builder->orderby('main.updated_at desc,main.id desc');
        }

        $list = $builder->getQuery()->execute()->toArray();

        //分页
        $builder->limit($params['page_size'], ($params['page_num'] - 1) * $params['page_size']);

        $departmentData = $builder->getQuery()->execute()->toArray();

        $data['count'] = count($list);
        $data['data'] = $departmentData;

        return $data;
    }

    /**
     * 面试详情
     * @param array $params
     */
    public function getInterviewDetail($params) {
        $this->getDI()->get('logger')->write_log("getInterviewDetail参数：" . json_encode($params) , 'info');
        $return = [];
        $columns = " id as subscribe_id,status as sub_status,staff_id,hr_remark,hc_id,shop_id,interviewer_id,
        DATE_FORMAT(CONVERT_TZ(created_at, '+00:00', '" . $this->timezone . "' ),'%Y-%m-%d %H:%i:%s') as created_at,
        DATE_FORMAT(CONVERT_TZ(updated_at, '+00:00', '" . $this->timezone . "' ),'%Y-%m-%d %H:%i:%s') as updated_at ";
        $interviewSubscribe = HrInterviewSubscribeModel::findFirst([
                                                                       'columns' => $columns,
                                                                       'conditions' => 'interview_id = :interview_id: and id = :id:',
                                                                       'bind' => ['interview_id' => $params['interview_id'],'id'=>$params['subscribe_id']],
                                                                   ]);
        $return['working_day_rest_type'] = '';
        $hc_department_id = 0;
        $hc_job_title = 0;
        $hc_store_id = '';
        if($interviewSubscribe){
            $return['subscribe_id'] = $interviewSubscribe->subscribe_id;
            $return['staff_id'] = $interviewSubscribe->staff_id;
            $return['sub_status'] = $interviewSubscribe->sub_status;
            $return['hr_remark'] = $interviewSubscribe->hr_remark;
            $return['hc_id'] = $interviewSubscribe->hc_id;
            $return['created_at'] = $interviewSubscribe->created_at;
            $return['updated_at'] = $interviewSubscribe->updated_at;
            $return['shop_id'] = $interviewSubscribe->shop_id;

            $HrHcModel = HrHcModel::findFirst(
                [
                    'columns' => 'job_title,department_id,worknode_id,hire_type',
                    'conditions' => 'hc_id = :hc_id:',
                    'bind' => ['hc_id' =>$interviewSubscribe->hc_id],
                ]
            );
            $hc_department_id = $HrHcModel->department_id ?? 0;
            $hc_job_title = $HrHcModel->job_title ?? 0;
            $hc_store_id = $HrHcModel->worknode_id ?? '';

            $return['job_title'] = $HrHcModel->job_title;
            $return['hire_type'] = $HrHcModel->hire_type;
            //工作天数轮休规则
            $hr_job_department_relation = HrJobDepartmentRelationModel::findFirst([
                'columns' => 'department_id,job_id,working_day_rest_type',
                'conditions' => 'department_id = :department_id: and job_id = :job_id:',
                'bind' => [
                    'department_id' => $HrHcModel->department_id,
                    'job_id' => $HrHcModel->job_title
                ]
            ]);
            $return['working_day_rest_type'] = !empty($hr_job_department_relation) ? $hr_job_department_relation->working_day_rest_type : '';
        }
        
        $interview = HrInterviewModel::findFirst([
                                                       'columns' => 'resume_id',
                                                       'conditions' => 'interview_id = :interview_id:',
                                                       'bind' => ['interview_id' => $params['interview_id']],
                                                   ]);
        if(empty($interview)){
            $this->getDI()->get('logger')->write_log("getInterviewDetail获取信息失败-面试表（" . json_encode($params) . "", 'info');
            return [];
        }


        $HrResumeModel = HrResumeModel::findFirst([
                                                      'columns' => 'id as cvid ,credentials_num,sex,date_birth,name,phone,email,first_name_en,last_name_en,call_name ',
                                                      'conditions' => 'id = :id:',
                                                      'bind' => ['id' =>$interview->resume_id],
                                                  ]);
        if(empty($HrResumeModel)){
            $this->getDI()->get('logger')->write_log("getInterviewDetail获取信息失败-简历表，cvid:".$interview->resume_id, 'info');
            return [];
        }

        $return['cvid'] = $HrResumeModel->cvid;
        $return['sex'] = $HrResumeModel->sex;
        $return['date_birth'] = $HrResumeModel->date_birth;
        $return['name'] = empty($HrResumeModel->name)?$HrResumeModel->first_name_en .' '. $HrResumeModel->last_name_en : $HrResumeModel->name;
        $return['phone'] = $HrResumeModel->phone;
        $return['email'] = $HrResumeModel->email;
        $return['call_name'] = $HrResumeModel->call_name;
        $return['identity'] = $HrResumeModel->credentials_num;

        $condition  = "interview_id = :interview_id:";
        $binds = ['interview_id'=>$params['interview_id']];
        if(!empty($params['opt_id'])){
            $condition .= " and opt_id =:opt_id:";
            $binds['opt_id'] = $params['opt_id'];
        }

        $HrInterviewInfoModel = HrInterviewInfoModel::findFirst([
                                                      'columns' => '
                                                      evaluate as evaluation,
                                                      out_type,state as interview_state
                                                      ',
                                                      'conditions' => $condition,
                                                      'bind' =>  $binds,
                                                      'order'=>'interview_info_id desc'
                                                  ]);
        if($HrInterviewInfoModel){
            $return['evaluation'] = $HrInterviewInfoModel->evaluation;
            $return['out_type'] = $HrInterviewInfoModel->out_type;
            $return['interview_state'] = $HrInterviewInfoModel->interview_state;
        }

        //获取最近一次面试反馈直线上级id和工作天数&轮休规则
        $last_interview_info = HrInterviewInfoModel::findFirst([
            'columns' => 'manager_id,manager_name,working_day_rest_type',
            'conditions' => 'interview_id = :interview_id:',
            'bind' =>  [
                'interview_id' => $params['interview_id']
            ],
            'order'=>'interview_info_id desc'
        ]);

        if($last_interview_info) {
            $return['last_interview_working_day_rest_type'] = !empty($last_interview_info->working_day_rest_type) ? $last_interview_info->working_day_rest_type : '';
            $return['last_interview_manager_id'] = !empty($last_interview_info->manager_id) ? $last_interview_info->manager_id : '';
            $return['last_interview_manager_name'] = !empty($last_interview_info->manager_name) ? $last_interview_info->manager_name : '';
        } else {
            $return['last_interview_working_day_rest_type'] = '';
            $return['last_interview_manager_id'] = '';
            $return['last_interview_manager_name'] = '';
        }
        //找不到上一轮上级 1. NW部门下且是一线候选人面试反馈的直线上级默认规则：
        if(empty($return['last_interview_manager_id'])){
            $manager_staff_info = $this->getNwLineManager($hc_job_title,$hc_department_id,$hc_store_id);
            $return['last_interview_manager_id'] = $manager_staff_info['staff_info_id'];
            $return['last_interview_manager_name'] = $manager_staff_info['staff_name'];
        }



        //兼容旧数据
        $return['interview_link'] = '';
        $return['hr_remark'] = '';
        $return['state'] = '';
        $return['interview_time'] = '';
        $return['detail_address'] = '';
        $return['opt_id'] = '';

        if(isset($params['opt_id']) && !empty($params['opt_id'])){
            $condition_operation = "id = :id:";
            $binds_operation['id'] = $params['opt_id'];

            $HrInterviewOperationModel = HrInterviewOperationModel::findFirst(
                [
                    'columns' => "
                    DATE_FORMAT(CONVERT_TZ(interview_time, '+00:00', '" . $this->timezone . "' ),'%Y-%m-%d %H:%i:%s') as interview_time,
                    detail_address,
                    interview_url,
                    hr_remark,
                    state,
                    id,
                    shop_id,
                    create_id,
                    update_id,
                    interviewer_id
                    ",
                    'conditions' => $condition_operation,
                    'bind' => $binds_operation,
                    'order'=>'id desc'
                ]
            );

            if($HrInterviewOperationModel){
                $return['interview_link'] = $HrInterviewOperationModel->interview_url;
                $return['hr_remark'] = $HrInterviewOperationModel->hr_remark;
                $return['state'] = $HrInterviewOperationModel->state;
                $return['interview_time'] = $HrInterviewOperationModel->interview_time;
                $return['detail_address'] = $HrInterviewOperationModel->detail_address;
                $return['opt_id'] = $HrInterviewOperationModel->id;
                $return['create_id'] = $HrInterviewOperationModel->create_id; //创建用户
                $return['update_id'] = $HrInterviewOperationModel->update_id; //修改用户
                $return['operation_shop_id'] = $HrInterviewOperationModel->shop_id; //网点
                $return['interviewer_id'] = $HrInterviewOperationModel->interviewer_id;
            }
        }

        $re[0] = $return;
        return $re;
    }

    /**
     * 获取面试官操作记录
     * @param $params
     * @return array
     */
    public function getInterviewerOperation($params)
    {
        $columns = "DATE_FORMAT(CONVERT_TZ(interview_time, '+00:00', '" . $this->timezone . "' ),'%Y-%m-%d %H:%i:%s') as interview_time,detail_address,interview_url,hr_remark,state ";
        $data = HrInterviewOperationModel::findFirst([
                                                'columns' => $columns,
                                                'conditions' => 'interview_sub_id = :interview_sub_id: and interviewer_id = :staff_id:',
                                                'bind' => ['interview_sub_id' => $params['subscribe_id'],'staff_id'=>$params['staff_id']],
                                                'order'=>'id desc'
                                            ]);
        return $data ? $data->toArray():[];
    }


    /**
     * 取消面试
     * @param array $params Description
     * @param $data
     * @return bool Description
     */
    public function cancelInterview($params, $data = null) {

        if(empty($data)){
            $data = HrInterviewModel::findFirst([
                'conditions' => 'interview_id = :interview_id:',
                'bind' => ['interview_id' => $params['interview_id']],
            ]);
        }
        if ($data) {
            if($params['state'] ==2 ){
                $params['state'] = 31;  //hr_interview && hr_interview_subscribe 状态值不匹配
            }
            $data->state = $params['state']; //取消面试
            $data->cancel_type = $params['cancel_type'];
            $data->cancel_reason = $params['cancel_reason'];

            return $data->save() ? true : $data->getMessages();
        }

        return false;
    }

    /**
     * 取消预约
     * @Access  public
     * @param array $params Description
     * @return bool Description
     */
    public function interviewAppointmentStatus($params) {
        try {

            $data = HrInterviewSubscribeModel::findFirst([
                                                             'conditions' => 'id = :id:',
                                                             'bind' => ['id' => $params['subscribe_id']],
                                                         ]);

            if ($data) {
                $data->status = $params['state'];
                $data->save();
            }

            $conditions = 'interview_sub_id = :interview_sub_id: ';
            $binds = ['interview_sub_id' => $params['subscribe_id']];
            //兼容旧数据
            if(isset($params['opt_id']) && !empty($params['opt_id'])){
                $conditions.=  ' and id = :id:';
                $binds['id'] = $params['opt_id'];
            }

            $operationData = HrInterviewOperationModel::findFirst([
                  'conditions' => $conditions,
                  'bind' => $binds,
                  'order'=>'id desc'
              ]);

            if($operationData){
                $operationData->state = HrInterviewOperationModel::STATE_CANCEL;//取消
                $operationData->cancel_at = gmdate('Y-m-d H:i:s');
                if(!$operationData->save()){
                    $this->getDI()->get('logger')->write_log("interviewRepository_interviewAppointmentStatus:HrInterviewOperationModel更新失败（" . json_encode($params) . "）操作失败：", 'notice');
                }
            }

            return $data;
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("interviewRepository_interviewAppointmentStatus:（" . json_encode($params) . "）操作失败：" . $e->getMessage() . $e->getTraceAsString(), 'notice');
            throw $e;
        }
    }

    /**
     * [selectLevel 查询轮次]
     * @return [type] [array]
     * <AUTHOR> <[email address]>
     */
    public function selectLevel($interview_id = '')
    {
        // 非空验证
        if (empty($interview_id)) return false;

        // sql
        $sql = "
            SELECT 
                *
            FROM
                `hr_interview_info`
            WHERE
                `hr_interview_info`.`interview_id` = :interview_id
        ";
        $sql_param = [
            'interview_id' => $interview_id,
        ];
        // 执行sql
        $this->getDI()->get('db')->execute($sql, $sql_param);
        return $this->getDI()->get('db')->affectedRows();
    }

    /**
     * [recordFeedback 面试反馈]
     * @return [type] [array]
     * <AUTHOR> <[email address]>
     */
    public function recordFeedback($paramIn = []) {
        // 处理数据
        $opt = $paramIn['conclusion'];
        $subscribe_id = $paramIn['subscribe_id'];
        $staff_id = $paramIn['staff_id'];

        unset($paramIn['person_accessory']);
        unset($paramIn['feedback_accessory']);
        unset($paramIn['evaluate_accessory']);
//        unset($paramIn['reason']);

        unset($paramIn['conclusion']);
        //unset($paramIn['evaluation']);
        unset($paramIn['staff_id']);
        unset($paramIn['current_user_id']);
        unset($paramIn['subscribe_id']);
        unset($paramIn['cvid']);

        //$paramIn['out_type'] = !empty($paramIn['reason']) ? $paramIn['reason'] : 0 ;
        unset($paramIn['reason']);
        //面试不通过原因修改，不通过原因写入pass_type_record表
        $paramIn['out_type'] = enums::INTERVIEW_OUT_TYPE_DEFAULT;
        if(!empty($paramIn['out_type_list'])){
            $out_type_list = $paramIn['out_type_list'];
            unset($paramIn['out_type_list']);
        }

        $paramIn['evaluate'] = $paramIn['evaluation'];
        unset($paramIn['evaluation']);

        $paramIn['interviewer_id'] = $staff_id;//面试官id
        // 插入
        $this->db->begin();

        //修改面试操作记录状态
        $operationData = HrInterviewOperationModel::findFirst([
                                                                  'conditions' => 'interview_sub_id = :interview_sub_id: and interviewer_id = :staff_id:',
                                                                  'bind' => ['interview_sub_id' => $subscribe_id,'staff_id'=>$staff_id],
                                                                  'order'=>'id desc'
                                                              ]);

        if($operationData){
            $operationData->state =   $opt ==  HrInterviewOperationModel::STATE_PASS ? HrInterviewOperationModel::STATE_PASS : HrInterviewOperationModel::STATE_NOT_PASS;//通过/不通过
            $operationData->save();
        }



        $paramIn['opt_id'] =  $operationData->id??0;
        $paramIn['operation_id'] =  $staff_id;

        $data = $this->db->insertAsDict(
                'hr_interview_info', $paramIn
        );
        if (!$data) {
            $this->db->rollback();
            return false;
        }
        $resumeId = $this->db->lastInsertId();

        //面试不通过，原因写入pass_type_record表
        $batch_data = [];
        if($opt==0){
            foreach ($out_type_list as $key => $val) {
                $batch_data[] = [
                    'business_type' => enums::INTERVIEW_PASS_BUSINESS_TYPE,
                    'business_id' => $resumeId,
                    'resume_id' => $resumeId,
                    'opt_id' => $paramIn['opt_id'],
                    'out_type' => enums::INTERVIEW_OUT_TYPE_1,
                    'deleted' => 0,
                    'pass_type' => $val['pass_type'],
                    'pass_reason' => $val['out_type'],
                    'pass_remark' => !empty($val['pass_remark']) ? $val['pass_remark'] : '',
                ];
            }
        }
        if($opt==0 && !empty($batch_data)) {
            // 插入前先把之前的数据软删
            $this->getDI()->get('db') ->updateAsDict('hr_interview_pass_type_record', ["deleted"=>1], [
                'conditions' => "resume_id = $resumeId ",
            ]);
            if( !$this->batch_insert('hr_interview_pass_type_record', $batch_data) ){
                $this->db->rollback();
                return false;
            }
        }

        // 修改主表状态
        $update_params =[
            'interview_id' => $paramIn['interview_id'],
            'state' => $paramIn['state'],
            'manager_id' => $paramIn['manager_id'] ?? 0,
            'manager_name' => $paramIn['manager_name'] ?? '',
            'working_day_rest_type' => $paramIn['working_day_rest_type'] ?? 0
        ];
        if (!$this->updateInterview($update_params)) {
            $this->db->rollback();
            return false;
        };
        // 修改表状态
        if (!$this->updateSubscribe($paramIn['interview_id'])) {
            $this->db->rollback();
            return false;
        };
        

        $this->db->commit();
        return $resumeId;
    }

    /**
     * [updateInterview 修改状态]
     * @return [type] [array]
     * <AUTHOR> <[email address]>
     */
    //public function updateInterview($interview_id = '', $state = '') {
    public function updateInterview($params) {
        $interview_id = $params['interview_id'] ?? '';
        $state = $params['state'] ?? '';

        $manager_id = $params['manager_id'] ?? 0;
        $manager_name = $params['manager_name'] ?? '';
        $working_day_rest_type = $params['working_day_rest_type'] ?? 0;

        // 非空验证
        if (empty($interview_id) || !in_array($state, ['1', '2', '3'])) {
            return false;
        }

        // 1、终试通过 - 20、代发offre
        $state == '1' && $state = '20';

        // 2、进入下一轮 - 10、面试中
        $state == '2' && $state = '10';

        // 3、不通过 - 30、已拒绝
        $state == '3' && $state = '30';

        $update_params['state'] = $state;
        if($state != 3) {
            $update_params['manager_id'] = $manager_id;
            $update_params['manager_name'] = $manager_name;
            $update_params['working_day_rest_type'] = $working_day_rest_type;
        }

        // 拼接sql
        $data = $this->db->updateAsDict('hr_interview', $update_params, 'interview_id = ' . $interview_id);
        if ($data === false)
            return false;

        //$this->sendMsgToBuddy($interview_id,$state);

        return true;
    }

    /**
     * 修改简历
     * @param integer $interviewId 简历信息
     * @param integer $state       状态
     * @param integer $level       当前轮次
     */
    public function sendMsgToBuddy($interviewId, $state = 0, $level = 0) {

        //合作到期，停用次功能
        return;

        $sql = "select * from hr_interview where interview_id=:id";
        $item = $this->db->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC, ['id' => $interviewId]);

        if (empty($item)) {
            $this->getDI()->get("logger")->write_log("not found interview", "info");
            return;
        }

        if (empty($state)) {
            $state = $item['state'];
        }

        $resume = (new ResumeRepository())->getResumeInfo(['id' => $item['resume_id']]);
        if (empty($resume)) {
            $this->getDI()->get("logger")->write_log("not found resume", "info");
            return;
        }
        //如果不是buddy的简历，不用管
        if ($resume['source'] != enums::$resume_source['buddy']) {
            $this->getDI()->get("logger")->write_log("not buddy resume", "info");
            return;
        }

        if (empty($level)) {
            $level = $this->selectLevel($interviewId);
        }
        if (empty($level)) {
            $level = 1;
        }

        $sql = "select * from hr_interview_info where interview_id = :id and `level`=:level";
        $info = $this->db->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC, ['id' => $interviewId, 'level' => $level]);
        $mark = '';
        $attachment = [];
        if (!empty($info)) {
            $mark = $info['evaluate'];
            $prefix = $this->getDI()->getConfig()->application['img_prefix'];
            $sql = "select * from hr_interview_file where interview_info_id = :id and `type`=3 and `is_del`=2 ";
            $files = $this->db->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC, ['id' => $info['interview_info_id']]);
            if (!empty($files)) {
                foreach ($files as $file) {
                    $attachment[] = ['url' => $prefix . $file['file_path'], 'name' => $file['file_name']];
                }
            }
        }

        $this->state_array = [
            '1' => $this->getTranslation()->_('4801'),
            '5' => $this->getTranslation()->_('4802'),
            '10' => $this->getTranslation()->_('4803'),
            '20' => $this->getTranslation()->_('4804'),
            '25' => $this->getTranslation()->_('4805'),
            '30' => $this->getTranslation()->_('4806'),
            '31' => $this->getTranslation()->_('4807'),
            '32' => $this->getTranslation()->_('4808'),
            '40' => $this->getTranslation()->_('4809'),
            '2' => $this->getTranslation()->_('4810'),
            '3' => $this->getTranslation()->_('4811'),
        ];

        $state_arr = [1 => 4801, 5 => 4802, 10 => 4803, 20 => 4804, 25 => 4805, 30 => 4806, 31 => 4807, 32 => 4808, 40 => 4809, 2 => 4810, 3 => 4811];
        $this->lang = 'en';
        foreach ($state_arr as $k => $v) {
            $state_arr[$k] = $this->getTranslation()->_('' . $v);
        }

        $hc_id = $item['hc_id'];

        $sql = "select * from hr_interview_offer where interview_id = :id order by id desc";
        $offer = $this->db->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC, ['id' => $interviewId]);
        if (!empty($offer)) {
            $hc_id = $offer['hc_id'];
        }

        //简历id,轮次，状态，轮次评价内容，时间是英国时间，buddy会加7
        $data = ['cv_id' => $resume['id'], 'hc_id' => $hc_id, 'level' => $level, 'state' => $state, 'mark' => $mark, 'attachment' => $attachment, 'state_list' => $state_arr, 'buddy_id' => $resume['buddy_id'], 'updated_at' => $item['updated_at']];

        //$message = new MessageQueueServer();
        //$message->sendToMNS(MessageQueueServer::INTERVIEW_QUEUE, $data);
    }

    /**
     * 修改状态
     * @param type $interview_id
     * @param type $status 1:未取消 2：已取消 3：面试完成
     * @return boolean
     */
    public function updateSubscribe($interview_id = '', $status = '2') {
        // 非空验证
        if (empty($interview_id))
            return false;

        // 拼接sql
        $data = $this->db->updateAsDict(
                'hr_interview_subscribe',
                [
                    'status' => $status
                ],
                'interview_id = 5 ORDER BY level DESC LIMIT 1'
        );
        if ($data === false)
            return false;
        return true;
    }

    /**
     * 我的面试菜单显示
     * @param array $params
     */
    public function myInterview($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id');
        $builder->from(['main' => HrInterviewOperationModel::class]);
        $builder->andWhere('interviewer_id = :interviewer_id:', ['interviewer_id' => $params['staff_id']]);
        $builder->limit(1);
        $listCnt = $builder->getQuery()->getSingleResult();

        return $listCnt ?? false;
    }

    
    /**
     * 我的面试提示数量
     * @param  $params
     */
    public function myCountInterview($params): array
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => HrInterviewOperationModel::class]);
        $builder->leftjoin(HrInterviewSubscribeModel::class, 's.id=main.interview_sub_id', 's');
        $builder->innerjoin(HrInterviewModel::class, 's.interview_id=a.interview_id', 'a');
        $builder->andWhere('main.interviewer_id = :interviewer_id: and main.state=:state:', ['interviewer_id' => $params['staff_id'],'state'=>0]);
        $totalCount = $builder->columns('COUNT(1) AS total')->getQuery()->execute()->getFirst();

        $listCnt = $totalCount->total;


        $data['data']['num'] = 0;

        if(!is_cli()){
            $headerData       = $this->request->getHeaders();
            //演示APP 调用返回 0
            if((isset($headerData['X-Demonstration-App']) || $this->request->get('x-demonstration-app')) ){
                return $data;
            }

        }

        if($listCnt){
            $data['data']['num'] = (int)$listCnt;
        }
        return $data;
    }


    /**
     * @description:NW部门下且是一线候选人面试反馈的直线上级默认
     * @param int $job_title_id
     * @param int $department_id
     * @param string $store_id
     * @return string[] :
     * @author: L.J
     * @time: 2023/2/22 10:38
     */
    public function getNwLineManager($job_title_id = 0, $department_id = 0, $store_id = '')
    {
        $data = ['staff_info_id' => '', 'staff_name' => ''];
        if (empty($job_title_id) || empty($department_id) || empty($store_id) || $store_id == enums::HEAD_OFFICE_ID) {
            return $data;
        }
        //备注：每个国家的Network Operations在架构上名称略有不一致
        //泰国：Network Operations、Network Bulky Operations
        //菲律宾：Network Operations（NW-PH）
        //马来：Network Operations（Malayisa）
        //越南、老挝、印尼暂无此部门

        $department = SysDepartmentModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => [
                'id' => $department_id,
            ],
            'columns'    => ['id', 'ancestry_v3'],
        ]);
        if (empty($department)) {
            return $data;
        }
        $department = $department->toArray();
        //判断是否为 nw 部门下
        $settingEnvServer = new SettingEnvServer();
        $interview_nw_dep = explode(',', $settingEnvServer->getSetValFromCache('interview_nw_dep'));
        if (empty($interview_nw_dep)) {
            return $data;
        }
        $ancestry_v3 = explode('/', $department['ancestry_v3']);
        //非 nw 部门下
        if (empty(array_intersect($ancestry_v3, $interview_nw_dep))) {
            return $data;
        }
        //非一线
        if (!(new SalaryServer($this->lang, $this->timezone))->isFirstLineJob($department_id, $job_title_id)) {
            return $data;
        }
        //根据网点找
        $store_info = SysStoreModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => [
                'id' => $store_id,
            ],
            'columns'    => ['id', 'manager_id', 'manage_piece', 'manager_name'],
        ]);
        if (empty($store_info)) {
            return $data;
        }
        $store_info = $store_info->toArray();
        if (!empty($store_info['manager_id'])) {
            $data['staff_info_id'] = $store_info['manager_id'];
            $data['staff_name']    = $store_info['manager_name'];
            return $data;
        }
        //根据片区找
        $manage_piece = SysManagePieceModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $store_info['manage_piece']],
            'columns'    => ['id', 'manager_id', 'manager_name'],
        ]);
        if (empty($manage_piece)) {
            return $data;
        }
        $manage_piece = $manage_piece->toArray();
        if (!empty($manage_piece['manager_id'])) {
            $data['staff_info_id'] = $manage_piece['manager_id'];
            $data['staff_name']    = $manage_piece['manager_name'];
            return $data;
        }

        return $data;
    }


}
