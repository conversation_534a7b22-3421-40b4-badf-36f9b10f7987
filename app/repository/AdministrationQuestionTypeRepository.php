<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\backyard\AdministrationQuestionTypeModel;

class AdministrationQuestionTypeRepository extends BaseRepository
{
    private $db = null;

    public function __construct()
    {
        parent::__construct();
        $this->db = $this->getDI()->get("db");
    }


    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 根据参数获取数据
     * @param array $params 查询参数
     * @param $colum 查询的字段
     * @return false
     */
    public function getQuestionTypeByParams(array $params, $colum = [])
    {
        if (empty($params)) {
            return [];
        }
        $fields  = (!is_array($colum) || empty($colum)) ? "*" : implode(',', $colum);
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($fields);
        $builder->from(['m' => AdministrationQuestionTypeModel::class]);
        foreach ($params as $field => $val) {
            if (!is_array($val)) {
                $builder->andWhere($field." = :{$field}:", [$field => $val]);
            }
            if (is_array($val)) {
                $builder->andWhere($field." IN ({{$field}:array})", [$field => $val]);
            }
        }
        return $builder->getQuery()->execute()->toArray();
    }
}