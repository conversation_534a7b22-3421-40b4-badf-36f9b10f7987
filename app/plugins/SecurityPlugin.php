<?php


namespace FlashExpress\bi\App\Plugins;

use Phalcon\Events\Event;
use Phalcon\Mvc\Dispatcher;
use Phalcon\Mvc\User\Plugin;

/**
 * Class SecurityPlugin
 * @package App\Plugins
 * @property ResourceServer $resourceServer
 */
class SecurityPlugin extends Plugin
{

    /**
     * @param Event $event
     * @param Dispatcher $dispatcher
     * @return bool
     * @throws AuthenticationException
     * @throws AuthorizationException
     * @throws BusinessLogicException
     */
    public function beforeExecuteRoute(Event $event, Dispatcher $dispatcher)
    {
        $m = $dispatcher->getModuleName();
        $c = $dispatcher->getControllerName();
        $a = strtolower($dispatcher->getActionName());
        if (in_array($m, ['app', 'web']) || ($m == 'auth' && $c == 'user') || ($m == 'auth' && $c == 'qr' && $a == 'callback')) {
            try {
                //cid 安全验证
                
            } catch (\Exception $e) {
                print_r($e);                 
            }
        }
        return true;
    }
  
}