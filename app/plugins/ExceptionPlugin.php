<?php

namespace FlashExpress\bi\App\Plugins;

use Phalcon\Events\Event;
use Phalcon\Mvc\User\Plugin;
use Phalcon\Dispatcher;
use Phalcon\Mvc\Dispatcher\Exception as DispatcherException;
use Phalcon\Mvc\Dispatcher as MvcDispatcher;
use FlashExpress\bi\App\Core\PhalBaseResponse as Response;
use FlashExpress\bi\App\library\Exception\AuthenticationException;
use FlashExpress\bi\App\library\Exception\AuthorizationException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\Exception\BusinessException;

/**
 * NotFoundPlugin
 *
 * Handles not-found controller/actions
 */
class ExceptionPlugin extends Plugin
{
    /**
     * This action is executed before perform any action in the application
     *
     * @param Event $event
     * @param MvcDispatcher $dispatcher
     * @param \Exception $exception
     * @return boolean
     */
    public function beforeException(Event $event, MvcDispatcher $dispatcher, $exception)
    {
        $data       = null;
        $msg        = $exception->getMessage();
        $log_data   = [];
        $realCode = http_response_code();
        $code = $realCode == Response::OK ? Response::OK : $realCode;
        $level      = 'info';
        $resultCode = 0;
        if ($exception instanceof DispatcherException) {
            if (in_array($exception->getCode(),
                [Dispatcher::EXCEPTION_HANDLER_NOT_FOUND, Dispatcher::EXCEPTION_ACTION_NOT_FOUND])) {
                $country_code = ucfirst(strtolower(env('country_code')));
                if (stripos($dispatcher->getNamespaceName(), '\Modules\\' . $country_code) !== false) {
                    $name_space = str_replace('\Modules\\' . $country_code, '', $dispatcher->getNamespaceName());
                    $dispatcher->setNamespaceName($name_space);
                    $dispatcher->setModuleName(null);
                    $dispatcher->forward([
                        'namespace'  => $name_space,
                        'controller' => $dispatcher->getControllerName(),
                        'action'     => $dispatcher->getActionName(),
                    ]);
                    $this->response->setStatusCode(Response::OK);
                    return false;
                }
            }
            $code = Response::NOT_FOUND;
        } elseif ($exception instanceof AuthenticationException) {
            $code = Response::UNAUTHORIZED;
        } elseif ($exception instanceof AuthorizationException) {
            $code = Response::FORBIDDEN;
        } elseif ($exception instanceof ValidationException) {
            $resultCode = $exception->getCode() ?? $resultCode;
            $log_data   = [
                'exception_type' => 'Validation',
                'code'           => $resultCode,
                'message'        => $exception->getMessage(),
                'file'           => $exception->getFile(),
                'line'           => $exception->getLine(),
                'trace'          => $exception->getTraceAsString(),
                'params'         => [$this->request->getJsonRawBody(true), $this->request->get()],
            ];
        } elseif ($exception instanceof BusinessException) {
            $resultCode = $exception->getCode() ?? $resultCode;
            $log_data   = [
                'exception_type' => 'Business',
                'code'           => $resultCode,
                'message'        => $exception->getMessage(),
                'file'           => $exception->getFile(),
                'line'           => $exception->getLine(),
                'trace'          => $exception->getTraceAsString(),
                'params'         => [$this->request->getJsonRawBody(true), $this->request->get()],
            ];
        } else {
            $level    = 'error';
            $str = 'something went wrong ...';
            if (isCountry('PH')) {
                //$str = 'System is upgrading. Please try again later.';
            }
            $msg = RUNTIME == 'dev' ? $exception->getMessage() : $str;

            $data     = RUNTIME == 'dev' ? $exception->getTraceAsString() : null;
            $log_data = [
                'exception_type' => 'Other',
                'code'           => $exception->getCode(),
                'message'        => $exception->getMessage(),
                'file'           => $exception->getFile(),
                'line'           => $exception->getLine(),
                'trace'          => $exception->getTraceAsString(),
                'params'         => [$this->request->getJsonRawBody(true), $this->request->get()],
            ];
        }

        $log_data && $this->getDI()->get('logger')->write_log([$log_data], $level);

        $result = [
            'code'    => $resultCode,
            'message' => $msg,//兼容客户端和h5
            'msg'     => $msg,//兼容客户端和h5
            'data'    => $data,
            'tid'     => molten_get_traceid(),
        ];

        if (in_array(RUNTIME, ['dev', 'tra', 'training'])) {
            unset($log_data['params'], $log_data['message']);
            $result['debug'] = $log_data;
        }
        $this->response->setStatusCode($code);
        $this->response->setJsonContent($result);
        $this->response->send();
        exit();
    }


}
