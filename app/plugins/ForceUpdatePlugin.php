<?php

namespace FlashExpress\bi\App\Plugins;

use Phalcon\Events\Event;
use Phalcon\Mvc\Model;
use Phalcon\Di\Injectable;

/**
 * 强制更新插件
 * @description: 自动处理Phalcon模型在useDynamicUpdate模式下相同值不更新的问题
 * @author: AI
 * @date: 2025-01-15
 */
class ForceUpdatePlugin extends Injectable
{
    /**
     * 需要强制更新的字段配置
     * 可以按模型类名配置不同的字段列表
     */
    private $forceUpdateConfig = [
        // 全局配置：所有模型都会检查这些字段
        '*' => ['set_val', 'status', 'state', 'is_active', 'enabled'],
        
        // 特定模型配置
        'SettingEnvModel' => ['set_val'],
        'UserModel' => ['status', 'is_active'],
        // 可以继续添加其他模型的配置
    ];

    /**
     * 在模型保存前触发
     * @param Event $event
     * @param Model $model
     */
    public function beforeSave(Event $event, Model $model)
    {
        $this->handleForceUpdate($model);
    }

    /**
     * 在模型更新前触发
     * @param Event $event
     * @param Model $model
     */
    public function beforeUpdate(Event $event, Model $model)
    {
        $this->handleForceUpdate($model);
    }

    /**
     * 处理强制更新逻辑
     * @param Model $model
     */
    private function handleForceUpdate(Model $model)
    {
        $className = $this->getModelClassName($model);
        $forceUpdateFields = $this->getForceUpdateFields($className);

        foreach ($forceUpdateFields as $field) {
            if (isset($model->$field)) {
                // 强制标记字段为已修改
                $model->writeAttribute($field, $model->$field);
            }
        }
    }

    /**
     * 获取模型类名（不包含命名空间）
     * @param Model $model
     * @return string
     */
    private function getModelClassName(Model $model)
    {
        $fullClassName = get_class($model);
        return substr($fullClassName, strrpos($fullClassName, '\\') + 1);
    }

    /**
     * 获取指定模型需要强制更新的字段列表
     * @param string $className
     * @return array
     */
    private function getForceUpdateFields($className)
    {
        $fields = [];
        
        // 添加全局配置的字段
        if (isset($this->forceUpdateConfig['*'])) {
            $fields = array_merge($fields, $this->forceUpdateConfig['*']);
        }
        
        // 添加特定模型配置的字段
        if (isset($this->forceUpdateConfig[$className])) {
            $fields = array_merge($fields, $this->forceUpdateConfig[$className]);
        }
        
        // 去重并返回
        return array_unique($fields);
    }

    /**
     * 动态添加需要强制更新的字段
     * @param string $className 模型类名，使用'*'表示全局
     * @param array $fields 字段列表
     */
    public function addForceUpdateFields($className, array $fields)
    {
        if (!isset($this->forceUpdateConfig[$className])) {
            $this->forceUpdateConfig[$className] = [];
        }
        
        $this->forceUpdateConfig[$className] = array_merge(
            $this->forceUpdateConfig[$className],
            $fields
        );
        
        // 去重
        $this->forceUpdateConfig[$className] = array_unique($this->forceUpdateConfig[$className]);
    }

    /**
     * 移除指定模型的强制更新字段
     * @param string $className 模型类名
     * @param array $fields 要移除的字段列表
     */
    public function removeForceUpdateFields($className, array $fields)
    {
        if (isset($this->forceUpdateConfig[$className])) {
            $this->forceUpdateConfig[$className] = array_diff(
                $this->forceUpdateConfig[$className],
                $fields
            );
        }
    }

    /**
     * 获取当前配置
     * @return array
     */
    public function getConfig()
    {
        return $this->forceUpdateConfig;
    }
}
