<?php

namespace FlashExpress\bi\App\Traits;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\ErrCode;

trait RpcHelper
{
    /**
     * rpc请求OA端
     * @param array $params 请求参数组
     * @param string $method 请求方法
     * @return array
     */
    public function rpcOa(array $params, string $method, $language)
    {
        $api_client = new ApiClient("oa_rpc", '', $method, $language);
        $api_client->setParams($params);
        $res = $api_client->execute();
        if (!isset($res['result'])) {
            return [
                'code' => ErrCode::VALIDATE_ERROR,
                'msg'  => $this->getTranslation()->_('please try again'),
                'data' => [],
            ];
        }
        if (isset($res['result']['message'])) {
            $res['result']['msg'] = $res['result']['message'];
            unset($res['result']['message']);
        } else {
            $res['result']['msg'] = '';
        }
        return $res['result'];
    }
}