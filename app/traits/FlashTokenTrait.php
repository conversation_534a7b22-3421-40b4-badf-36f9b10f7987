<?php

namespace FlashExpress\bi\App\Traits;

use FlashExpress\bi\App\Enums\RedisEnums;

trait FlashTokenTrait
{
    public static  $sub_to_master_session_prefix = 'STM';//子账号转主账号token前缀
    private $sessionIdFirstSalt;
    private $sessionExpireSeconds;
    private $sessionPrefix = null;
    //前缀
    public function setSessionPrefix($session_prefix)
    {
        $this->sessionPrefix = $session_prefix;
        return $this;
    }

    private function initConfig()
    {
        $this->sessionIdFirstSalt   = env('session_salt', 'meiyouxiangfafa');
        $this->sessionExpireSeconds = env('session_expires_seconds', 86400);
    }

    private static function sha256HexDigest($source): string
    {
        $messageDigest = hash_init('sha256');
        hash_update($messageDigest, $source);
        return hash_final($messageDigest);
    }

    private function generateDigest($time, $userId): string
    {
        return self::sha256HexDigest(sprintf("%s%s%s", $time, $this->sessionIdFirstSalt, $userId));
    }

    /**
     * 生成token
     * @param $userId
     * @return string
     */
    public function generateSessionId($userId): string
    {
        $this->initConfig();
        $time   = time() + $this->sessionExpireSeconds;
        $digest = $this->generateDigest($time, $userId);
        $token  = sprintf("%s%s%s%s%s", $time, '_', $digest, '_', $userId);
//        $this->getDI()->get('redisLib')->set(sprintf(RedisEnums::BY_STAFF_SESSION_ID, $userId), $token,
//            $this->sessionExpireSeconds + 2);

        if (!empty($this->sessionPrefix)) {
            $token = $this->sessionPrefix . '_' . $token;
        }
        return $token;
    }

    /**
     * 验证token
     * @param $token
     * @return false|mixed|string
     */
    public function validateToken($token)
    {
        $this->initConfig();

        if (strrpos($token, '_') === false) {
            return false;
        }
        if (!empty($this->sessionPrefix)) {
            [$session_prefix, $time, $digest, $staff_id] = explode('_', $token);
        } else {
            [$time, $digest, $staff_id] = explode('_', $token);
        }

        if ($this->generateDigest($time, $staff_id) !== $digest) {
            return false;
        }
//        $redis    = $this->getDI()->get('redisLib');
//        $staff_session = $redis->get(sprintf(RedisEnums::BY_STAFF_SESSION_ID,
//            $staff_id));
//        if (!$staff_session) {
//            return false;
//        }
        return $staff_id;
    }
}