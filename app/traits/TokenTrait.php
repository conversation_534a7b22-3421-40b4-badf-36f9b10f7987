<?php

namespace FlashExpress\bi\App\Traits;

use <PERSON><PERSON><PERSON><PERSON>\JWT\Builder;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Parser;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Hmac\Sha512;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Key;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Token;
use <PERSON><PERSON><PERSON><PERSON>\JWT\ValidationData;
use <PERSON>\Uuid\Uuid;

trait TokenTrait
{
    /**
     * Returns the JWT token object
     *
     * @param string $token
     *
     * @return Token
     */
    protected function parseToken($token)
    {
        return (new Parser())->parse($token);
    }

    /**
     * Returns the default audience for the tokens
     *
     * @return string
     */
    protected function getTokenAudience()
    {
        /** @var string $audience */
        $audience = env('TOKEN_AUDIENCE', 'https://phalconphp.com');

        return $audience;
    }

    /**
     * Returns the time the token is issued at
     *
     * @return int
     */
    protected function getTokenTimeIssuedAt()
    {
        return time();
    }

    /**
     * Returns the time drift i.e. token will be valid not before
     *
     * @return int
     */
    protected function getTokenTimeNotBefore()
    {
        return (time() + env('TOKEN_NOT_BEFORE', 0));
    }

    /**
     * Returns the expiry time for the token
     *
     * @return int
     */
    protected function getTokenTimeExpiration()
    {
        return (time() + env('TOKEN_EXPIRATION', 86400));
    }

    /**
     * @return Key
     */
    protected function getKey()
    {
        return new Key(env('JWT_KEY', 'changeme！'));
    }

    /**
     * @return mixed
     */
    protected function getIssuer()
    {
        return env('TOKEN_ISSUER', 'https://phalconphp.com');
    }

    /**
     * @return string
     * @throws \Exception
     */
    protected function getTokenId()
    {
        return Uuid::uuid4()->toString();
    }

    /**
     * @param $user
     * @return string
     * @throws \Exception
     */
    protected function generateToken($user)
    {
        $signer  = new Sha512();
        $builder = new Builder();
        $token   = $builder
            ->issuedBy($this->getIssuer())
            ->permittedFor($this->getTokenAudience())
            ->identifiedBy($this->getTokenId(), true)
            ->issuedAt($this->getTokenTimeIssuedAt())
            ->canOnlyBeUsedAfter($this->getTokenTimeNotBefore())
            ->expiresAt($this->getTokenTimeExpiration())
            ->withClaim('uid',$user->id)
            ->getToken($signer, $this->getKey());

        return $token->__toString();
    }

    /**
     * @param Token $token
     * @return mixed
     */
    protected function validateToken($token)
    {
        $signer  = new Sha512();
        $data = new ValidationData(); // It will use the current time to validate (iat, nbf and exp)
        $data->setIssuer($this->getIssuer());
        $data->setAudience($this->getTokenAudience());

        return $token->verify($signer, $this->getKey()) && $token->validate($data);
    }
}