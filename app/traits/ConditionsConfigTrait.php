<?php

namespace FlashExpress\bi\App\Traits;

use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\Enums\ConditionsRulesEnums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\ConditionsRulesModel;

trait ConditionsConfigTrait
{
    //规则key
    protected $rule_key = '';
    //参与条件参数
    protected $parameters = [];

    /**
     * 根据规则key获取条件配置的结果
     * @return mixed
     */
    public function getConfig()
    {
        if ($this->isRuleKeyValid()) {
            return $this->get();
        }
        return null;
    }

    /**
     * 获取必要的参数名（测试期间使用）
     * @return void
     */
    public function getRequiredParameters()
    {

    }

    /**
     * rule_key是否有效
     * @return bool
     */
    private function isRuleKeyValid(): bool
    {
        try {
            if (empty($this->getRuleKey())) {
                throw new ValidationException('miss args `rule_key`');
            }
            $ruleInfo = ConditionsRulesModel::findFirstByRuleKey($this->getRuleKey());
            if (empty($ruleInfo) || $ruleInfo->deleted == CommonEnums::IS_DELETED_YES) {
                throw new ValidationException(sprintf('rule key %s not exists', $this->getRuleKey()));
            }
            if ($ruleInfo->state == ConditionsRulesEnums::RULE_STATE_DISABLE) {
                throw new ValidationException(sprintf('rule key %s not exists', $this->getRuleKey()));
            }
        } catch (ValidationException $ve) {
            $this->logger->write_log('[ConditionsRuleTrait isValid] err:' . $ve->getMessage(), 'notice');
            return false;
        }
        return true;
    }

    /**
     * @return mixed
     */
    private function getRuleKey()
    {
        return $this->rule_key;
    }

    /**
     * @param mixed $rule_key
     */
    public function setRuleKey($rule_key)
    {
        $this->rule_key = $rule_key;
        return $this;
    }

    private function getParameters(): array
    {
        return $this->parameters;
    }

    public function setParameters(array $parameters)
    {
        $this->parameters = $parameters;
        return $this;
    }
}