<?php

namespace FlashExpress\bi\App\Traits;

trait FactoryTrait
{
    /**
     * @param $class_name
     * @param string $locate svc 用的时候要传进来
     * @return mixed
     */
    public function class_factory($class_name, $locate = '', $timezone='')
    {
        $lang =  !empty($locate) ? $locate: $this->config->application->lang;
        $timeZone = !empty($timezone) ? $timezone : $this->config->application->timeZone;

        $country_code = $this->config->application->country_code;
        $country_code = ucfirst(strtolower($country_code));
        $class = "\FlashExpress\bi\App\Modules\\{$country_code}\Server\\{$class_name}";
        if(class_exists($class))
            return new $class($lang,$timeZone);

        $class = "\FlashExpress\bi\App\Server\\{$class_name}";
        return new $class($lang,$timeZone);
    }
}