<?php
/**
 * Author: Bruce
 * Date  : 2025-04-15 20:38
 * Description: 审批节点查找工具类
 */

namespace FlashExpress\bi\App\Traits;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeRelateModel;

trait FindApprovalNodeTrait
{
    protected $node_list;
    protected $node_relate_list;
    protected $workflow_data_cache = [];

    /**
     * @return mixed
     */
    private function getNodeList()
    {
        return $this->node_list;
    }

    /**
     * @param mixed $node_list
     */
    private function setNodeList($node_list): void
    {
        $this->node_list = $node_list;
    }

    /**
     * @return mixed
     */
    private function getNodeRelateList()
    {
        return $this->node_relate_list;
    }

    /**
     * @param mixed $node_relate_list
     */
    private function setNodeRelateList($node_relate_list): void
    {
        $this->node_relate_list = $node_relate_list;
    }

    /**
     * 查询当前审批人，是否为审批流第一节点审批人
     * @param array $params
     * @return bool
     */
    public function findFirstApprovalNode(array $params): bool
    {
        // 参数验证
        if (!$this->validateParams($params)) {
            return false;
        }

        $auditType = $params['auditType'];
        $auditId = $params['auditId'];
        $staffId = $params['staff_id'];

        // 获取审批申请信息和工作流数据
        $workflowData = $this->getWorkflowData($auditType, $auditId);
        if (empty($workflowData)) {
            return false;
        }

        // 获取第一个节点
        $firstNode = $this->getNodeByType($workflowData['flow_id'], enums::NODE_SUBMITTER);
        if (empty($firstNode)) {
            return false;
        }

        // 找到第一个审批节点
        $firstApprovalNodeId = $this->findNextApprovalNodeId($firstNode->getId());
        if (empty($firstApprovalNodeId)) {
            return false;
        }

        // 检查用户是否为该节点的待审批人
        return $this->isUserApprover($firstApprovalNodeId, $staffId, enums::APPROVAL_STATUS_PENDING);
    }

    /**
     * 查询当前审批人，是否为审批流最后节点审批人
     * @param array $params
     * @return bool
     */
    public function findLastApprovalNode(array $params): bool
    {
        // 参数验证
        if (!$this->validateParams($params)) {
            return false;
        }

        $auditType = $params['auditType'];
        $auditId = $params['auditId'];
        $staffId = $params['staff_id'];

        // 获取审批申请信息和工作流数据
        $workflowData = $this->getWorkflowData($auditType, $auditId);
        if (empty($workflowData)) {
            return false;
        }

        // 获取最终节点
        $lastNode = $this->getNodeByType($workflowData['flow_id'], enums::NODE_FINAL);
        if (empty($lastNode)) {
            return false;
        }

        // 找到最后一个审批节点
        $lastApprovalNodeId = $this->findPreviousApprovalNodeId($lastNode->getId());
        if (empty($lastApprovalNodeId)) {
            return false;
        }

        // 检查用户是否为该节点的已审批人
        return $this->isUserApprover($lastApprovalNodeId, $staffId, enums::APPROVAL_STATUS_APPROVAL);
    }

    /**
     * 参数验证
     * @param array $params
     * @return bool
     */
    private function validateParams(array $params): bool
    {
        $requiredFields = ['auditType', 'auditId', 'staff_id'];
        
        foreach ($requiredFields as $field) {
            if (empty($params[$field])) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 获取工作流数据（带缓存）
     * @param int $auditType
     * @param int $auditId
     * @return array|null
     */
    private function getWorkflowData(int $auditType, int $auditId): ?array
    {
        $cacheKey = "{$auditType}_{$auditId}";
        
        if (isset($this->workflow_data_cache[$cacheKey])) {
            $workflowData = $this->workflow_data_cache[$cacheKey];
            $this->setNodeList($workflowData['node_list']);
            $this->setNodeRelateList($workflowData['node_relate_list']);
            return $workflowData;
        }

        // 获取审批申请信息
        $auditApplyInfo = AuditApplyModel::findFirst([
            "conditions" => "biz_type = :biz_type: and biz_value = :biz_value:",
            "bind" => [
                "biz_type" => $auditType,
                "biz_value" => $auditId,
            ],
        ]);

        if (empty($auditApplyInfo)) {
            return null;
        }

        $flowId = $auditApplyInfo->getFlowId();

        // 获取节点关联关系
        $workflowNodeRelate = WorkflowNodeRelateModel::find([
            "conditions" => "flow_id = :flow_id: and deleted = 0",
            "bind" => ["flow_id" => $flowId],
            "columns" => "from_node_id,to_node_id",
        ])->toArray();

        // 获取节点信息
        $workflowNode = WorkflowNodeModel::find([
            "conditions" => "flow_id = :flow_id: and deleted = 0",
            "bind" => ["flow_id" => $flowId],
            "columns" => "id,type",
        ])->toArray();

        $nodeRelateList = array_column($workflowNodeRelate, 'to_node_id', 'from_node_id');
        $nodeList = array_column($workflowNode, 'type', 'id');

        $this->setNodeRelateList($nodeRelateList);
        $this->setNodeList($nodeList);

        // 缓存数据
        $workflowData = [
            'flow_id' => $flowId,
            'node_list' => $nodeList,
            'node_relate_list' => $nodeRelateList,
        ];
        $this->workflow_data_cache[$cacheKey] = $workflowData;

        return $workflowData;
    }

    /**
     * 根据类型获取节点
     * @param string $flowId
     * @param int $nodeType
     * @return mixed
     */
    private function getNodeByType(string $flowId, int $nodeType)
    {
        return WorkflowNodeModel::findFirst([
            "conditions" => "flow_id = :flow_id: and type = :type: and deleted = 0",
            "bind" => [
                "flow_id" => $flowId,
                "type" => $nodeType,
            ],
        ]);
    }

    /**
     * 检查用户是否为指定节点的审批人
     * @param int $nodeId
     * @param int $userId
     * @param int $approvalState
     * @return bool
     */
    private function isUserApprover(int $nodeId, int $userId, int $approvalState): bool
    {
        $approvalInfo = AuditApprovalModel::findFirst([
            "conditions" => "flow_node_id = :node_id: and approval_id = :approval_id: and state = :state: and deleted = 0",
            "bind" => [
                "node_id" => $nodeId,
                "approval_id" => $userId,
                "state" => $approvalState,
            ],
        ]);

        return !empty($approvalInfo);
    }

    /**
     * 向前查找下一个审批节点
     * @param int $currentNodeId
     * @return int|false
     */
    private function findNextApprovalNodeId(int $currentNodeId)
    {
        return $this->findApprovalNodeInDirection($currentNodeId, 'next');
    }

    /**
     * 向后查找上一个审批节点
     * @param int $currentNodeId
     * @return int|false
     */
    private function findPreviousApprovalNodeId(int $currentNodeId)
    {
        return $this->findApprovalNodeInDirection($currentNodeId, 'previous');
    }

    /**
     * 在指定方向查找审批节点
     * @param int $currentNodeId
     * @param string $direction 'next' 或 'previous'
     * @return int|false
     */
    private function findApprovalNodeInDirection(int $currentNodeId, string $direction)
    {
        $nodeList = $this->getNodeList();
        $nodeRelateList = $this->getNodeRelateList();
        
        if (empty($nodeList) || empty($nodeRelateList)) {
            return false;
        }

        // 构建反向关联关系（用于向前查找）
        if ($direction === 'previous') {
            $nodeRelateList = array_flip($nodeRelateList);
        }

        $visitedNodes = [];
        $currentNode = $currentNodeId;
        $maxIterations = count($nodeList); // 防止无限循环

        while ($maxIterations > 0 && isset($nodeRelateList[$currentNode])) {
            $nextNode = $nodeRelateList[$currentNode];
            
            // 防止循环引用
            if (in_array($nextNode, $visitedNodes)) {
                break;
            }
            $visitedNodes[] = $nextNode;

            // 检查节点是否存在且有效
            if (!isset($nodeList[$nextNode])) {
                break;
            }

            // 检查是否为审批节点
            if (in_array($nodeList[$nextNode], [enums::NODE_APPROVER, enums::NODE_COUNTERSIGN])) {
                return $nextNode;
            }

            $currentNode = $nextNode;
            $maxIterations--;
        }

        return false;
    }

    /**
     * 清理缓存
     */
    public function clearCache(): void
    {
        $this->workflow_data_cache = [];
        $this->node_list = null;
        $this->node_relate_list = null;
    }

    // 保留原有方法用于向后兼容
    /**
     * @deprecated 使用 isUserApprover 替代
     */
    private function getApprovalStateByNodeId($node_id, $user, int $default_approval_state = enums::APPROVAL_STATUS_PENDING)
    {
        return AuditApprovalModel::findFirst([
            "conditions" => "flow_node_id = :node_id: and approval_id = :approval_id: and state = :state: and deleted = 0",
            "bind" => [
                "node_id" => $node_id,
                "approval_id" => $user,
                "state" => $default_approval_state,
            ],
        ]);
    }

    /**
     * @deprecated 使用 findNextApprovalNodeId 替代
     */
    private function findPrefixNodeId($current_node_id)
    {
        return $this->findNextApprovalNodeId($current_node_id);
    }
}