<?php

namespace FlashExpress\bi\App\Traits;

use FlashExpress\bi\App\library\enums;

trait AuditStateTrait
{

    /**
     * 审批状态转业务状态
     * @param $state
     * @param $isRevoke
     * @return int|mixed
     */
    public function transformState($state,$isRevoke)
    {
        if ($isRevoke) {
            if ($state == enums::APPROVAL_STATUS_PENDING) {
                $state = enums::APPROVAL_STATUS_PENDING;
            } elseif ($state == enums::APPROVAL_STATUS_APPROVAL) {
                $state = enums::APPROVAL_STATUS_CANCEL;
            } elseif ($state == enums::APPROVAL_STATUS_CANCEL) {
                $state = enums::APPROVAL_STATUS_CANCEL;
            } else {
                $state = enums::APPROVAL_STATUS_APPROVAL;
            }
        }
        return $state;
    }
}