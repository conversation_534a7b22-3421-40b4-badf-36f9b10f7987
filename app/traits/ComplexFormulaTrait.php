<?php

namespace FlashExpress\bi\App\Traits;


//处理复杂公式
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;

trait ComplexFormulaTrait
{
    /**
     * 解析复杂的条件
     * @param $formula
     * @return string
     */
    public function parseComplexCondition($formula): string
    {
        //初始化返回值
        $newFormula = '';

        //先判断该条件是否需要解析
        if (empty($formula) || !strpos($formula, '#')) {
            return $newFormula;
        }

        //获取需要解析的结构
        preg_match_all('/((?<=#)([^a-z])*?(?=#))+/', $formula, $match);
        if (empty($match)) {
            return $newFormula;
        }

        if (isset($match[1]) && $match[1]) {
            $newFormula = $formula;
            foreach ($match[1] as $v) {
                [$newFormulaType, $newFormulaValue] = explode('|', $v);

                switch ($newFormulaType) {
                    case 'DEPARTMENT': //原始结构 #DEPARTMENT|4#  ==> 表达的意思: 属于部门 Network Managerment(4)
                        $model   = new SysDepartmentModel();
                        $deptIds = $model->getSpecifiedDeptAndSubDept($newFormulaValue);
                        if (empty($deptIds)) {
                            //如果部门被删除，默认为0
                            $deptIds = [0];
                            $this->logger->write_log("[workflow][DEPARTMENT][审批流中部门不存在] ===> " . $newFormulaValue);
                        }
                        $newFormula = str_replace(sprintf("#%s#", $v), implode(',', $deptIds), $newFormula);
                        break;
                    default:
                        break;
                }
            }
        }
        return $newFormula;
    }
}