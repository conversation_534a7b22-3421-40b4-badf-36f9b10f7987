#!/bin/bash
#by <EMAIL>

function echo_log(){
   echo -e "\033[0;31m $1 \033[0m"
}
function echo_blue {
   echo -e "\033[47;34m $1 \033[0m"
}

function start() {
    echo_blue "1 start"
    if [ "$1" = "1" ];then
        docker-compose up -d
        echo_blue "start ok http://127.0.0.1/ "
    fi
}

function stop() {
    echo_blue "2 stop"
    if [ "$1" = "1" ];then
        docker-compose down
        echo_blue "start"
    fi
}

function build() {
    echo_blue "3 build"
    if [ "$1" = "1" ];then
        docker build -t flashexpress/php:72-bk -f php/Dockerfile-bk .
        echo_blue "start"
    fi
}

echo_log "you will start bk"
start
stop
build

read -p "input: " number

case $number in
    1)
       start 1
    ;;
    2)
       stop 1
    ;;
    3)
       build 1
    ;;
    *)
       echo_log "input error"
    ;;
esac
