# 1 授权第三方账号
```
{
"client_id": "SBjjoxzXoYc",
"client_secret": "zcwQfhrwrmUGaepzc",
"grant_type": "client_credentials"
}
```

# 2 composer install
```
composer require league/oauth2-server 7.2
```

# 3 调用授权接口获取认证token
```

{{host}}/api/auth/token

{
    "token_type": "Bearer",
    "expires_in": 2678399,
    "access_token": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
}
{{host}}/api/test/test


```
