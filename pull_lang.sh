#!/bin/bash
#代码备份

#第一步备份代码

function echo_log(){
   echo -e "\033[0;31m $1 \033[0m"
}
function echo_blue {
   echo -e "\033[47;34m $1 \033[0m"
}

#备份代码库
function git_branch {
  br=`git branch | grep "*"`
  local_branch=${br/* /}
  #建立备份分支名字
  branch_name=bak`date +%Y%m%d%H%M%S`
  #echo_log $branch_name
  #建立备份分支名
  git branch $branch_name
  echo_blue "${local_branch} 备份成功为 ${branch_name}"
}

#初始化项目
function init {
  composer install
  mkdir app/runtime/logs
  mkdir app/runtime/log
  mkdir app/runtime/tasklog
  chmod 777 -R app/runtime
}

function cpssh {
   docker cp ~/.ssh/id_rsa mybk:/root/.ssh/
   docker cp ~/.ssh/id_rsa.pub mybk:/root/.ssh/
}

#运行生成语言包文件
function build_translation {
    #php bin/cli translation gen
    php app/cli.php translation main
}

function pull_nacos() {
   
  # 尝试从.env文件中读取country_code
  if [ -f ".env" ]; then
      country_code=$(grep "^country_code=" .env 2>/dev/null | cut -d'=' -f2 | tr -d '"' | tr -d "'")
      if [ -n "$country_code" ]; then
          echo_log "当前环境变量 COUNTRY_CODE为: $country_code"
      fi
  fi
  
  # 输入国家code
  read -p "请输入变更后的国家code(如:th)，默认th: " country_code
  
  echo_log "使用的国家代码: $country_code"
  echo_log "正在拉取 data-flashhr-$country_code-backyard-api-01"
  php app/cli.php tool pull_nacos $country_code
  echo ""
  echo_log "拉取data-flashhr-$country_code-backyard-api-01配置完成."
}

echo_log "请选择 1 备份代码 2 升级代码  3 更新翻译  4 cp id_rsa in docker 5 初始化项目 6 拉取nacos配置"
read -p "请输入: " number

case $number in
    1)
       echo_log "你要做的操作是备份代码"
       git_branch
    ;;
    2)
       echo_log "更新代码中"
       git pull origin
    ;;
    3)
       echo_blue "语言包开始更新"
       build_translation
       echo_blue "语言包更新完毕"
    ;;
    4)
       echo_log "cp id_rsa in docker"
       cpssh
    ;;
    5)
       echo_log "初始化项目"
       init
    ;;
    6)
       echo_log "拉取nacos配置"
       pull_nacos
    ;;
    *)
       echo_log "输入错误"
    ;;
esac