
------赵成成 员工车辆类型 8838
ALTER TABLE `vehicle_info` ADD COLUMN `formal_data` text NOT NULL COMMENT '上次审核通过数据';

---------------------------9031--------------------
-------修改字段信息和增加字段
ALTER TABLE `hr_job_department_relation`
MODIFY COLUMN `jd_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '岗位描述' AFTER `jd_id`,
ADD COLUMN `jd_desc_supply` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'JD描述(补充)' AFTER `jd_desc`,
MODIFY COLUMN `job_requirements_jybj` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '职位要求-教育背景' AFTER `plan_hc_nums`,
MODIFY COLUMN `job_requirements_zyjl` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '职位要求-专业经历' AFTER `job_requirements_jybj`;

------创建变更记录表
CREATE TABLE `hr_job_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `hr_job_d_r_id` int(11) unsigned NOT NULL COMMENT '关联 ID',
  `type` tinyint(2) DEFAULT '0' COMMENT '类型: 1.新增职位 2.修改职位 3.新建关联 4. 编辑关联 5.解除关联',
  `log_data` varchar(200) NOT NULL DEFAULT '' COMMENT '历史数据',
  `save_data` varchar(200) NOT NULL DEFAULT '' COMMENT '新数据',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `created_uid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '操作人',
  `staff_info_name` varchar(50) DEFAULT NULL COMMENT '操作人姓名',
  `hr_job_id` int(11) NOT NULL COMMENT '变更职位 ID',
  PRIMARY KEY (`id`),
  KEY `idx_uid` (`created_uid`) COMMENT '操作人id',
  KEY `idx_hrjobid` (`hr_job_id`) USING BTREE COMMENT '职位 id'
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COMMENT='职位管理-变更记录';

------增加 JD 列表导出权限
INSERT INTO `hr_permission_auth`( `pid`, `class`, `function`, `auth_code`, `describe`, `is_switch`, `updated_at`) VALUES (3, 'jd', 'jdListExport', 'jdListExport', 'JD管理-JD列表导出', 0, '2021-06-04 10:30:57');


---------------------------9031--------------------





