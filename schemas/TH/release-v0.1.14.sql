-- by9164-------------------------------------------
CREATE TABLE `sys_store_goods` (
                                   `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
                                   `store_cate_id` varchar(150) NOT NULL COMMENT '网点id',
                                   `sys_store_cate` varchar(50) NOT NULL COMMENT '网点名称',
                                   `goods_id` varchar(100) DEFAULT NULL COMMENT '商品id',
                                   `create_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_at` datetime DEFAULT NULL,
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COMMENT='网点商品查看关联表';


INSERT INTO `setting_env` (`code`, `set_val`, `remark`, `created_at`, `updated_at`) VALUES ('interior_buy_limit_goods_id', '1,2,3,4,5,6,7,8,11', '工服限制购买goods_id', '2021-06-17 03:03:50', '2021-06-17 03:06:03');
INSERT INTO `setting_env` (`code`, `set_val`, `remark`, `created_at`, `updated_at`) VALUES ('interior_buy_limit_num', '5', '工服每月购买限制件数', '2021-06-17 02:34:24', '2021-06-17 02:34:47');


INSERT INTO `interior_goods`(`id`, `goods_name_zh`, `goods_name_th`, `goods_name_en`, `goods_cate`, `img_path`, `info`, `status`, `deleted_at`, `created_at`, `updated_at`) VALUES (11, 'Polo衫（黑）', 'สีดำ Polo Flash Express', 'Black Polo Flash Express', 11, 'https://fex-ph-asset-dev.oss-ap-southeast-1.aliyuncs.com/workOrder/oca-1623910260-da4d85f34adb4d1085ab371659cbc1d8.png', NULL, 1, NULL, '2021-06-21 06:04:05', '2021-06-21 06:18:16');

UPDATE `interior_goods` SET `goods_name_zh` = 'Polo衫（黄）', `goods_name_th` = 'สีเหลือง Polo Flash Express', `goods_name_en` = 'เสื้อ Polo Flash Express' WHERE `id` = 1;

UPDATE `interior_goods` SET  `img_path` = 'https://fex-ph-asset-dev.oss-ap-southeast-1.aliyuncs.com/workOrder/oca-1623901814-02848e4bd359482eb56a07602082312a.jpg' WHERE `id` = 9;

UPDATE `interior_goods_sku` SET `img_path` = 'https://fex-ph-asset-dev.oss-ap-southeast-1.aliyuncs.com/workOrder/oca-1623901814-02848e4bd359482eb56a07602082312a.jpg' WHERE `id` = 59;

UPDATE `interior_goods_sku` SET `price` = 125.00 WHERE `goods_sku_code` = 'FEX00116';
UPDATE `interior_goods_sku` SET `price` = 125.00 WHERE `goods_sku_code` = 'FEX00117';
UPDATE `interior_goods_sku` SET `price` = 125.00 WHERE `goods_sku_code` = 'FEX00267';


INSERT INTO `interior_goods_sku`( `goods_id`, `goods_sku_code`, `goods_name_en`, `goods_name_th`, `goods_name_zh`, `img_path`, `attr_1`, `attr_2`, `unit_en`, `unit_th`, `unit_zh`, `unit_num`, `sale_num`, `surplus_num`, `total_num`, `price`, `status`, `deleted_at`, `created_at`, `updated_at`) VALUES ( 11, 'FEX00981', 'เสื้อ POLO สีดำ size S', 'เสื้อ POLO สีดำ size S', 'POLO 衫 黑色 size S', 'https://fex-ph-asset-dev.oss-ap-southeast-1.aliyuncs.com/workOrder/oca-1623910260-da4d85f34adb4d1085ab371659cbc1d8.png', 'S', '', '', '', '件', 1, 0, 100, 0, 165.00, 1, NULL, '2020-08-13 08:41:32', '2020-12-04 07:01:53');
INSERT INTO `interior_goods_sku`( `goods_id`, `goods_sku_code`, `goods_name_en`, `goods_name_th`, `goods_name_zh`, `img_path`, `attr_1`, `attr_2`, `unit_en`, `unit_th`, `unit_zh`, `unit_num`, `sale_num`, `surplus_num`, `total_num`, `price`, `status`, `deleted_at`, `created_at`, `updated_at`) VALUES ( 11, 'FEX00982', 'เสื้อ POLO สีดำ size M', 'เสื้อ POLO สีดำ size M', 'POLO 衫 黑色 size M', 'https://fex-ph-asset-dev.oss-ap-southeast-1.aliyuncs.com/workOrder/oca-1623910260-da4d85f34adb4d1085ab371659cbc1d8.png', 'M', '', '', '', '件', 1, 0, 100, 0, 165.00, 1, NULL, '2020-08-13 08:41:32', '2020-12-04 07:01:53');
INSERT INTO `interior_goods_sku`( `goods_id`, `goods_sku_code`, `goods_name_en`, `goods_name_th`, `goods_name_zh`, `img_path`, `attr_1`, `attr_2`, `unit_en`, `unit_th`, `unit_zh`, `unit_num`, `sale_num`, `surplus_num`, `total_num`, `price`, `status`, `deleted_at`, `created_at`, `updated_at`) VALUES ( 11, 'FEX00983', 'เสื้อ POLO สีดำ size L', 'เสื้อ POLO สีดำ size L', 'POLO 衫 黑色 size L', 'https://fex-ph-asset-dev.oss-ap-southeast-1.aliyuncs.com/workOrder/oca-1623910260-da4d85f34adb4d1085ab371659cbc1d8.png', 'L', '', '', '', '件', 1, 0, 100, 0, 165.00, 1, NULL, '2020-08-13 08:41:32', '2020-12-04 07:01:53');
INSERT INTO `interior_goods_sku`( `goods_id`, `goods_sku_code`, `goods_name_en`, `goods_name_th`, `goods_name_zh`, `img_path`, `attr_1`, `attr_2`, `unit_en`, `unit_th`, `unit_zh`, `unit_num`, `sale_num`, `surplus_num`, `total_num`, `price`, `status`, `deleted_at`, `created_at`, `updated_at`) VALUES ( 11, 'FEX00984', 'เสื้อ POLO สีดำ size XL', 'เสื้อ POLO สีดำ size XL', 'POLO 衫 黑色 size XL', 'https://fex-ph-asset-dev.oss-ap-southeast-1.aliyuncs.com/workOrder/oca-1623910260-da4d85f34adb4d1085ab371659cbc1d8.png', 'XL', '', '', '', '件', 1, 0, 100, 0, 165.00, 1, NULL, '2020-08-13 08:41:32', '2020-12-04 07:01:53');
INSERT INTO `interior_goods_sku`( `goods_id`, `goods_sku_code`, `goods_name_en`, `goods_name_th`, `goods_name_zh`, `img_path`, `attr_1`, `attr_2`, `unit_en`, `unit_th`, `unit_zh`, `unit_num`, `sale_num`, `surplus_num`, `total_num`, `price`, `status`, `deleted_at`, `created_at`, `updated_at`) VALUES ( 11, 'FEX00985', 'เสื้อ POLO สีดำ size 2XL', 'เสื้อ POLO สีดำ size 2XL', 'POLO 衫 黑色 size 2XL', 'https://fex-ph-asset-dev.oss-ap-southeast-1.aliyuncs.com/workOrder/oca-1623910260-da4d85f34adb4d1085ab371659cbc1d8.png', '2XL', '', '', '', '件', 1, 0, 100, 0, 165.00, 1, NULL, '2020-08-13 08:41:32', '2020-12-04 07:01:53');
INSERT INTO `interior_goods_sku`( `goods_id`, `goods_sku_code`, `goods_name_en`, `goods_name_th`, `goods_name_zh`, `img_path`, `attr_1`, `attr_2`, `unit_en`, `unit_th`, `unit_zh`, `unit_num`, `sale_num`, `surplus_num`, `total_num`, `price`, `status`, `deleted_at`, `created_at`, `updated_at`) VALUES ( 11, 'FEX00986', 'เสื้อ POLO สีดำ size 3XL', 'เสื้อ POLO สีดำ size 3XL', 'POLO 衫 黑色 size 3XL', 'https://fex-ph-asset-dev.oss-ap-southeast-1.aliyuncs.com/workOrder/oca-1623910260-da4d85f34adb4d1085ab371659cbc1d8.png', '3XL', '', '', '', '件', 1, 0, 100, 0, 165.00, 1, NULL, '2020-08-13 08:41:32', '2020-12-04 07:01:53');


INSERT INTO `sys_store_goods`(`id`, `store_cate_id`, `sys_store_cate`, `goods_id`, `create_at`, `update_at`) VALUES (1, '-1', 'HO', '1,2,3,4,9,10,11', '2021-06-16 12:59:50', '2021-06-16 20:44:38');
INSERT INTO `sys_store_goods`(`id`, `store_cate_id`, `sys_store_cate`, `goods_id`, `create_at`, `update_at`) VALUES (2, '1', 'DC', '1,3,4,9,10,11', '2021-06-16 13:04:32', '2021-06-16 21:04:29');
INSERT INTO `sys_store_goods`(`id`, `store_cate_id`, `sys_store_cate`, `goods_id`, `create_at`, `update_at`) VALUES (3, '2', 'SP', '1,3,4,9,10,11', '2021-06-16 13:04:36', '2021-06-16 21:04:32');
INSERT INTO `sys_store_goods`(`id`, `store_cate_id`, `sys_store_cate`, `goods_id`, `create_at`, `update_at`) VALUES (4, '10', 'BDC', '1,3,4,9,10,11', '2021-06-16 13:04:39', '2021-06-16 21:04:36');
INSERT INTO `sys_store_goods`(`id`, `store_cate_id`, `sys_store_cate`, `goods_id`, `create_at`, `update_at`) VALUES (5, '4,5,7', 'SHOP', '1,3,4,9,10,11', '2021-06-16 13:04:43', '2021-06-16 21:04:39');
INSERT INTO `sys_store_goods`(`id`, `store_cate_id`, `sys_store_cate`, `goods_id`, `create_at`, `update_at`) VALUES (6, '9', 'OS', '2,3,4,9,10,11', '2021-06-16 13:04:47', '2021-06-16 21:04:43');
INSERT INTO `sys_store_goods`(`id`, `store_cate_id`, `sys_store_cate`, `goods_id`, `create_at`, `update_at`) VALUES (7, '8', 'HUB', '2,3,4,9,10,11', '2021-06-16 13:04:50', '2021-06-16 21:04:47');
INSERT INTO `sys_store_goods`(`id`, `store_cate_id`, `sys_store_cate`, `goods_id`, `create_at`, `update_at`) VALUES (8, '12', 'B_HUB', '2,3,4,9,10,11', '2021-06-16 13:04:54', '2021-06-16 21:04:50');
INSERT INTO `sys_store_goods`(`id`, `store_cate_id`, `sys_store_cate`, `goods_id`, `create_at`, `update_at`) VALUES (9, '18', 'FLASH_HOME', '5,6,7,8,9,10', '2021-06-17 06:46:55', '2021-06-17 14:45:42');


