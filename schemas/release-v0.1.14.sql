-- backyard 多国家同步执行

CREATE TABLE `staff_leave_read` (
  `staff_info_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '工号',
  `leave_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '假期类型 staff_audit表 leave_type字段',
  `is_read` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否选择不再弹窗 0 弹窗  1 已选择不再弹窗',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`staff_info_id`,`leave_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='请假类型说明 是否已读且选择不再弹窗';

-- backyard  安全培训二期  杨根新
ALTER TABLE `training_task`
ADD COLUMN `training_files` text NULL COMMENT '培训文件，json格式' AFTER `training_place`;

ALTER TABLE `training_pool`
ADD COLUMN `training_files` text NULL COMMENT '培训文件，json格式' AFTER `job_title_level`;

ALTER TABLE `training_people`
ADD COLUMN `is_join` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否参与：1待定，2参与，3拒绝' AFTER `status`;

ALTER TABLE `training_task`
ADD COLUMN `time_long_hour` smallint(5) UNSIGNED NOT NULL DEFAULT 0 COMMENT '预计时长，小时' AFTER `end_time`,
ADD COLUMN `time_long_minute` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '预计时长，分钟' AFTER `time_long_hour`;


ALTER TABLE `training_task`
ADD COLUMN `version` tinyint(255) UNSIGNED NOT NULL DEFAULT 1 COMMENT '发送消息通知版本' AFTER `time_long_minute`;


ALTER TABLE `training_operate_log`
ADD COLUMN `version` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '消息版本' AFTER `updated_time`;

-- coupon库 Liuchunhua

alter table message_content add related_id varchar(50) not null default '' comment '对外关联id结合message_courier.category使用', add index idx_related_id (related_id);