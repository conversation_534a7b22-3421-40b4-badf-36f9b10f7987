-- 菲律宾 bi 库
insert into `setting_env`(`code`,`set_val`,`content`)
values('outsourcing_edit_a','34017,66941,20017181,20017210,20017254,118672','外协工单配置权限A组');
insert into `setting_env`(`code`,`set_val`,`content`)
values('outsourcing_edit_b','34017,66941,20017181,20017210,20017254,118672','外协工单配置权限B组');
insert into `setting_env`(`code`,`set_val`,`content`)
values('outsourcing_edit_c','34017,66941,20017181,20017210,20017254,118672','外协工单配置权限C组');



-- 菲律宾by库：
INSERT INTO `setting_env` (`code`,`set_val`,`created_at`,`updated_at`,`remark`) VALUES ('dept_flash_philippines_network','125','2021-07-27 09:27:05','2021-07-27 09:27:05','Flash Philippines Network');
INSERT INTO `setting_env` (`code`,`set_val`,`created_at`,`updated_at`,`remark`) VALUES ('dept_fulfillment_id','26','2021-07-09 08:44:36','2021-07-27 09:29:43','Fulfillment 部门ID');
INSERT INTO `setting_env` (`code`,`set_val`,`created_at`,`updated_at`,`remark`) VALUES ('dept_hub_management_id','126','2021-07-09 08:44:36','2021-07-09 08:47:21','Hub Management 部门ID');
INSERT INTO `setting_env` (`code`,`set_val`,`created_at`,`updated_at`,`remark`) VALUES ('dept_network_operations_id','18','2021-07-09 08:44:36','2021-07-09 08:47:21','Network Operations 部门ID');

insert into `workflow`(`id`,`code`,`name`,`type`,`relate_type`,`description`,`flow_request`)values(20,null,'外协申请',1,14,null,null);

insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(185,20,'1','申请节点',0,1,null,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(186,20,'1','结束节点',99,1,null,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(187,20,'1','审批节点',1,3,1,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(188,20,'1','审批节点',1,2,null,'30286',1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(189,20,'2','申请节点',0,1,null,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(190,20,'2','结束节点',99,1,null,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(191,20,'2','审批节点',1,3,1,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(192,20,'2','审批节点',1,19,null,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(193,20,'3','申请节点',0,1,null,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(194,20,'3','结束节点',99,1,null,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(195,20,'3','审批节点',1,16,null,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(196,20,'3','审批节点',1,19,null,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(197,20,'3','审批节点',1,2,null,'30286',1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(198,20,'3','审批节点',1,2,null,'30286',1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(199,20,'4','申请节点',0,1,null,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(200,20,'4','结束节点',99,1,null,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(201,20,'4','审批节点',1,16,null,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(202,20,'4','审批节点',1,2,null,'30286',1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(203,20,'5','申请节点',0,1,null,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(204,20,'5','结束节点',99,1,null,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(205,20,'5','审批节点',1,2,null,'30286',1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(206,20,'6','申请节点',0,1,null,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(207,20,'6','结束节点',99,1,null,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(208,20,'6','审批节点',1,16,null,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(209,20,'6','审批节点',1,4,15,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(211,20,'7','申请节点',0,1,null,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(212,20,'7','结束节点',99,1,null,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(213,20,'7','审批节点',1,3,1,null,1,null,0);
insert into `workflow_node_base`(`id`,`flow_id`,`code`,`name`,`type`,`auditor_type`,`auditor_level`,`auditor_id`,`approval_policy`,`specify_approver`,`deleted`)
values(214,20,'7','审批节点',1,15,null,null,1,null,0);


insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'1',185,187,null,null,null,10,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'1',187,188,null,null,null,10,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'1',188,186,null,null,null,10,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'1',185,188,'$p1','isHasStoreManagerRole',null,20,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'2',189,191,null,null,null,10,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'2',191,192,null,null,null,10,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'2',192,190,null,null,null,10,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'2',189,192,'$p1','isHasStoreManagerRole',null,20,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'3',193,195,null,null,null,10,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'3',195,196,null,null,null,10,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'3',196,197,null,null,null,10,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'3',197,198,null,null,null,10,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'3',198,194,null,null,null,10,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'3',193,196,'$p1','isStoreManager',null,20,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'4',199,201,null,null,null,10,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'4',201,202,null,null,null,10,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'4',202,200,null,null,null,10,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'4',199,202,'$p1','isStoreManager',null,20,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'5',203,205,null,null,null,10,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'5',205,204,null,null,null,10,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'6',206,208,null,null,null,10,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'6',208,209,null,null,null,10,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'6',209,207,null,null,null,10,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'6',206,209,'$p1','isStoreManager',null,20,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'7',211,213,null,null,null,10,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'7',213,214,null,null,null,10,0);
insert into `workflow_node_relate_base`(`flow_id`,`code`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`deleted`)
values(20,'7',214,212,null,null,null,10,0);