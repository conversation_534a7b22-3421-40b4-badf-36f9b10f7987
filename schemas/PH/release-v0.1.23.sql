-- 到岗确认是否生成电子合同开关
INSERT INTO `setting_env`(`code`, `set_val`, `remark`) VALUES ('create_contract_switch', '{"create_contract":1,"get_contract_function":"getContractDataPH"}', 'win-hr 到岗确认是否生成员工电子合同');
-- 菲律宾库添加权限
insert into `hr_permission_auth` ( `id`, `pid`, `class`, `function`, `auth_code`, `describe`, `is_switch`, `updated_at`) values ( '153','1', 'contract', null, null, '电子合同', '0', '2021-07-21 13:44:21');
insert into `hr_permission_auth` ( `id`, `pid`, `class`, `function`, `auth_code`, `describe`, `is_switch`, `updated_at`) values ('154', '153', 'contract', 'getList', 'getList', '电子合同-员工列表', '0', '2021-07-21 13:50:32');

delete from hr_staff_contract;
delete from hr_staff_contract_log;