## BY库 全部国家执行
ALTER TABLE `audit_apply` CHANGE `flow_id` `flow_id` CHAR(36)  CHARACTER SET utf8mb4  COLLATE utf8mb4_general_ci  NULL  DEFAULT NULL  COMMENT '流程ID';
ALTER TABLE `audit_log` CHANGE `flow_id` `flow_id` CHAR(36)  CHARACTER SET utf8mb4  COLLATE utf8mb4_general_ci  NULL  DEFAULT NULL  COMMENT '工作流ID';
ALTER TABLE `workflow_node_relate` CHANGE `flow_id` `flow_id` CHAR(36)  CHARACTER SET utf8mb4  COLLATE utf8mb4_general_ci  NOT NULL  DEFAULT ''  COMMENT '审批流id';
ALTER TABLE `workflow_node` CHANGE `flow_id` `flow_id` CHAR(36)  CHARACTER SET utf8mb4  COLLATE utf8mb4_general_ci  NULL  DEFAULT NULL  COMMENT '工作流ID';

# 中国籍员工入离职提醒 hutao
CREATE TABLE `staff_email_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_info_id` int(11) NOT NULL COMMENT '员工号',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '邮件接收人',
  `event` tinyint(1) NOT NULL COMMENT '触发邮箱的事件 1:入职 2:离职',
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_staff_info_id`(`staff_info_id`) USING BTREE,
  INDEX `idx_email`(`email`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '中国籍员工在外国入离职邮件通知记录' ROW_FORMAT = Compact;