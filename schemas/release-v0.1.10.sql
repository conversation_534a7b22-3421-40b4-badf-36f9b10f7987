-- backayrd 数据库

-- 跨国探亲假 年假请假审批流变更
insert into workflow_node_relate_base (flow_id, from_node_id, to_node_id, valuate_formula, valuate_code, remark, sort)
values
(9, 60, 95, '$p1 == 0 && ($p2 == 1 || $p2 == 19)', 'isNetworkDepartment,getK', '非network部门，年假，跨国探亲假走二级审批', 65),
(9, 95, 96, '$p1 == 0 && ($p2 == 1 || $p2 == 19) && $p3 >= 3', 'isNetworkDepartment,getK,getK1', '非network部门，年假，跨国探亲假请假天数>=3走三级级审批', 65),
(9, 95, 62, '$p1 == 0 && ($p2 == 1 || $p2 == 19)', 'isNetworkDepartment,getK', '非network部门，年假，跨国探亲假三级后结束', 65)
;

-- hc预算申请
insert into workflow_node_base (id, flow_id, code, name, type, auditor_type, auditor_id, approval_policy)
    value (234, 7, 1, '审批节点', 1, 2, 28228, 1);
insert into workflow_node_relate_base (flow_id, code, from_node_id, to_node_id, valuate_formula, valuate_code, remark, sort)
values
(7, 1, 50, 234, '$p1 == 1', 'getK', 'Flash HR、CPO下泰国部门HRBP审批节点工号', 20),
(7, 1, 49, 234, '$p1 == 1 && $p2 && $p3 == 1' , 'getSubmitterDepartmentLevel,isSubmitterDepartmentManager,getK', 'Flash HR、CPO下泰国部门HRBP审批节点工号', 20),
(7, 1, 234, 52, null, null, null, 10);

insert into workflow_node_base (id, flow_id, code, name, type, auditor_type, auditor_id, approval_policy)
    value (235, 7, 2, '审批节点', 1, 2, 28228, 1);
insert into workflow_node_relate_base (flow_id, code, from_node_id, to_node_id, valuate_formula, valuate_code, remark, sort)
values (7, 2, 176, 235, '$p1 == 1', 'getK', 'Flash HR、CPO下泰国部门HRBP审批节点工号', 20),
       (7, 2, 235, 179, null, null, null, 20);


--王冬冰
CREATE TABLE `staff_device_info` (
                                     `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                     `staff_info_id` int(10) unsigned NOT NULL COMMENT '员工信息ID',
                                     `device_model` varchar(200) DEFAULT NULL COMMENT '设备型号',
                                     `equipment_type` varchar(60) DEFAULT NULL COMMENT '服务端 seed 1-kit 2-bs 3-backyard',
                                     `current_ip` varchar(20) DEFAULT NULL COMMENT '本次登录ip',
                                     `network_type` varchar(64) DEFAULT NULL COMMENT '运营商类型或者wifi',
                                     `lat` decimal(11,8) DEFAULT NULL COMMENT '位置的纬度',
                                     `lng` decimal(11,8) DEFAULT NULL COMMENT '位置经度',
                                     `current_time` datetime DEFAULT NULL COMMENT '本次登录时间',
                                     `version` varchar(20) DEFAULT NULL COMMENT '客户端版本',
                                     `os` varchar(30) DEFAULT NULL COMMENT '本次登录os',
                                     `client_id` varchar(128) DEFAULT NULL COMMENT '标识app，若同一个发布账号下所以app都被删除，重新安装此值会改变。',
                                     `device_id` varchar(200) DEFAULT NULL COMMENT '广告标识符，可作为设备唯一标识，但有可能获取不到',
                                     `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
                                     `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                     PRIMARY KEY (`id`),
                                     UNIQUE KEY `idx_staff_device` (`staff_info_id`,`equipment_type`,`client_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工登录账号表';

INSERT INTO `setting_env` (`code`, `set_val`, `remark`)
VALUES
('open_permission_store', 'TH01470102,TH03060403,TH04020107,TH01280201,TH04060110,TH04030108,TH01500207,TH04020308,TH01420106,TH04060205,TH01420103,TH01300301,TH01180102,TH01180204,TH01080109,TH01400201,TH01370302,TH01370402,TH01160205,TH01050407,TH01210102,TH01160106,TH01410202,TH01280204,TH02030107,TH02011302,TH02011001,TH02030206,TH02011107,TH02030303,TH02030401,TH01280109,TH02060106,TH02011306,TH02011205,TH02011006,TH02030505,TH01280303,TH02010501,TH02030201,TH01310201,TH02010204,TH01010202,TH01340103,TH01340202,TH01170201,TH01230105,TH01250101,TH01090201,TH0132010','临时开放指定网点和职位的 加班申请权限');


-- backyard 泰国和菲律宾同步增加
alter table staff_audit add column template_comment varchar(512) not null default '' comment '对应不同请假类型 需要保存存在差异的定制字段 json map 保存为key 字段翻译key  value 定制value' after reject_reason;