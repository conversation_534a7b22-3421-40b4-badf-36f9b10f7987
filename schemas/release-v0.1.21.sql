CREATE TABLE `vehicle_info_expire_remind_log` (
                                                  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
                                                  `vehicle_info_id` int(10) unsigned NOT NULL DEFAULT '0',
                                                  `staff_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '车辆信息所属人工号',
                                                  `staff_name` varchar(255) NOT NULL DEFAULT '' COMMENT '车辆信息所属人姓名',
                                                  `staff_department_name` varchar(255) NOT NULL DEFAULT '' COMMENT '车辆信息所属人部门名称',
                                                  `staff_position_name` varchar(255) NOT NULL DEFAULT '' COMMENT '车辆信息所属人职位名称',
                                                  `staff_manager_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '车辆信息所属人上级工号',
                                                  `expire_period` mediumint(5) unsigned NOT NULL DEFAULT '0' COMMENT '距离业务到期天数，示例: 90,60,30',
                                                  `remind_business_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '提醒业务类型: 1-保险结束；2-车辆税失效；3-驾照过期',
                                                  `business_end_date` date NOT NULL COMMENT '业务到期日期',
                                                  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
                                                  `send_time` datetime DEFAULT NULL COMMENT '消息提醒发送时间',
                                                  `send_status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '消息提醒发送状态:0-待发送；1-已发送；3-无需发送，已跳过',
                                                  PRIMARY KEY (`id`),
                                                  KEY `idx_send_status` (`send_status`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='车辆信息 - 到期提醒日志';

alter table hr_interview_offer add column `other_taxable_allowance` int(11) DEFAULT '0' COMMENT '其他纳税补贴';

-- 资产信息导入备注新字段
ALTER TABLE `assets_info` ADD COLUMN `remark` varchar(200) DEFAULT '' COMMENT '导入备注';
-- 资产转移日志添加更新时间
ALTER TABLE `assets_info_log` ADD COLUMN `updated_at` datetime NOT NULL DEFAULT '0001-01-01 00:00:00' ON UPDATE CURRENT_TIMESTAMP;