-- ###桂亚涛 离职资产消息
CREATE TABLE `msg_assets`
(
    `id`            INT ( 11 ) NOT NULL AUTO_INCREMENT,
    `msg_id`        VARCHAR(32)       DEFAULT NULL COMMENT '消息id',
    `staff_id`      VARCHAR(32)       DEFAULT NULL COMMENT '离职员工id',
    `goods_id`      VARCHAR(32)       DEFAULT NULL COMMENT '转交商品id',
    `transfer_id`   VARCHAR(32)       DEFAULT NULL COMMENT '转交人id',
    `asset_info_id` VARCHAR(32)       DEFAULT NULL COMMENT 'asset_info 表id',
    `type`          TINYINT ( 4 ) DEFAULT NULL COMMENT '1离职消息,2单个转交资产消息,3批量转交资产消息',
    `created_at`    datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`    datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后一次修改时间',
    PRIMARY KEY (`id`)
) ENGINE = INNODB AUTO_INCREMENT = 0 DEFAULT CHARSET = utf8mb4 ROW_FORMAT = COMPACT;

-- ## 李杰 【BY|外协申请】优化    bi 库
#增加索引
bi 库 人员固化表  线上数据  1306,3383
ALTER TABLE `hr_staff_transfer`
    ADD INDEX `idx_storeid_jobtitle_statdate_state_formal` (`store_id`, `job_title`, `stat_date`, `state`, `formal`);

##增加索引
bi 库  打卡记录  线上数据  1439,1830
ALTER TABLE `attendance_data_v2`
    ADD INDEX `idx_statdate_sysstoreid_attendancetime` (`stat_date`, `sys_store_id`, `attendance_time`);
-- backyard 数据库
create table staff_criminal_record (
                                       id int unsigned auto_increment primary key comment '主键ID',
                                       staff_info_id int unsigned not null default 0 comment '工号ID',
                                       record_status tinyint unsigned not null default 0 comment '犯罪记录状态 0 未检查  1 已检查',
                                       is_has_criminal tinyint unsigned not null default 0 comment '是否有犯罪记录 1 有 2 无',
                                       inspection_certificate varchar(1000) not null default '' comment '检查证明',
                                       company_inspection_certificate varchar(1000) not null default '' comment '公司检查证明',
                                       review_status tinyint unsigned not null default 0 comment '信息审核状态 0 待提交、 1 待审核、 2 已通过、3 已驳回',
                                       reject_reason varchar(500) not null default '' comment '驳回原因',
                                       operator_id int unsigned not null default 0 comment '操作人ID',
                                       created_time datetime not null DEFAULT CURRENT_TIMESTAMP comment '创建时间',
                                       updated_time datetime not null default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
                                       unique index idx_staff_id (staff_info_id)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT '员工犯罪记录表';


-- 犯罪记录类型
create table staff_criminal_types (
                                      id int unsigned auto_increment primary key comment '主键ID',
                                      staff_info_id int unsigned not null default 0 comment '工号ID',
                                      record_type tinyint unsigned not null default 0 comment '记录类型 1 毒品 2 拥有毒品/销售毒品 3 财产犯罪 4 侵犯生命和身体的犯罪 5 非法持有战争武器与枪支罪 6 轻罪 7 其他',
                                      record_case tinyint unsigned not null default 0 comment '记录案例 枚举值',
                                      record_text varchar(500) not null default '' comment '输入文本',
                                      record_status tinyint unsigned not null default 0 comment '1 已结束 2 未结束',
                                      operator_id  int unsigned not null default 0 comment '操作人ID',
                                      status tinyint unsigned not null default 0 comment ' 0 删除 1 有效',
                                      created_time datetime not null DEFAULT CURRENT_TIMESTAMP comment '创建时间',
                                      updated_time datetime not null default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
                                      index idx_staff_id (staff_info_id)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT '员工犯罪记录类型';

-- 二期
alter table staff_criminal_types add column process_result tinyint unsigned not null default 0 comment '1 个性行为警告书 2 辞退（不赔偿）3 劝服自愿离职 4 其他' after operator_id;
alter table staff_criminal_types add column resign_reason tinyint unsigned not null default 0 comment '1 向公司提供虚假信息' after process_result;
alter table staff_criminal_types add column resign_date date not null default '1970-01-01' comment '辞退/离职日期' after resign_reason;
alter table staff_criminal_types add column process_remark varchar(500) not null default '' comment '备注' after resign_date;
alter table staff_criminal_types add column process_operator_id int unsigned not null default 0 comment '处理人' after process_remark;
alter table staff_criminal_types add column process_datetime datetime not null default '1970-01-01' comment '处理时间' after process_operator_id;

-- 上级签字
alter table staff_criminal_types add column superior_id int unsigned not null default 0 comment '上级ID' after process_datetime;
alter table staff_criminal_types add column superior_name varchar(30) not null default '' comment '上级名字' after superior_id;
alter table staff_criminal_types add column superior_kit_id varchar(32) not null default '' comment '上级消息ID' after superior_name;
alter table staff_criminal_types add column superior_img varchar(150) not null default '' not null comment '上级签字' after superior_kit_id;
alter table staff_criminal_types add column superior_sign_status tinyint not null default 0 comment '上级签字状态 1 签字 2拒绝签字' after superior_img;
-- 本人签字
alter table staff_criminal_types add column kit_id varchar(32) not null default '' comment '本人签字' after superior_img;
alter table staff_criminal_types add column img_url varchar(150) not null default '' not null comment '本人签字' after kit_id;
alter table staff_criminal_types add column sign_status tinyint not null default 0 comment '本人签字状态 1 签字 2拒绝签字' after img_url;
-- hrbp 消息
alter table staff_criminal_types add column hrbp_kit_ids varchar(500) not null default '' comment 'hrbp 消息IDs' after sign_status;


