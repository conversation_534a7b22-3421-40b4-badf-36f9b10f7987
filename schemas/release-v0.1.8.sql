-- by数据库 各个国家都执行
alter table audit_permission
modify  `company_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '公司id 废弃';

alter table audit_permission
modify `sys_department_id` varchar(10) DEFAULT NULL COMMENT '部门Id 废弃';

alter table audit_permission
modify `node_department_id` varchar(10) DEFAULT NULL COMMENT '子部门Id 废弃';


alter table audit_permission
add column ancestry_1 varchar(10) DEFAULT NULL COMMENT '组织id 一级 对应 sys_department 的 ancestry_v3 字段的第一层级 跟产品沟通 按组织类型type 划分 不同意' after node_department_id;

alter table audit_permission
add column ancestry_2 varchar(10) DEFAULT NULL COMMENT '组织id 一级 对应 sys_department 的 ancestry_v3 字段的第一层级 跟产品沟通 按组织类型type 划分 不同意' after ancestry_1;

alter table audit_permission
add column ancestry_3 varchar(10) DEFAULT NULL COMMENT '组织id 一级 对应 sys_department 的 ancestry_v3 字段的第一层级 跟产品沟通 按组织类型type 划分 不同意' after ancestry_2;

alter table audit_permission
add column ancestry_4 varchar(10) DEFAULT NULL COMMENT '组织id 一级 对应 sys_department 的 ancestry_v3 字段的第一层级 跟产品沟通 按组织类型type 划分 不同意' after ancestry_3;

alter table audit_permission
add column ancestry_5 varchar(10) DEFAULT NULL COMMENT '组织id 一级 对应 sys_department 的 ancestry_v3 字段的第一层级 跟产品沟通 按组织类型type 划分 不同意' after ancestry_4;

alter table audit_permission
add column version varchar(10) DEFAULT NULL COMMENT '版本号' after module_type;


-- 只有by 泰国 数据库

INSERT INTO `audit_permission` (company_id, `sys_department_id`, `node_department_id`, `ancestry_1`, `ancestry_2`, `ancestry_3`, `ancestry_4`, `ancestry_5`, `job_title_id`, `job_title_grade`, `job_title_level`, `permission_value`, `module_type`, `version`)
VALUES
   (0, NULL, NULL, '999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[1,4,2]', 1, 'ot_2'),

   (0, NULL, NULL, '999', '333', NULL, NULL, NULL, NULL, NULL, NULL, '[1,4,2]', 1, 'ot_2'),

        (0, NULL, NULL, '999', '333', '220', '17', NULL, NULL, NULL, NULL, '[1,4]', 1, 'ot_2'),
        (0, NULL, NULL, '999', '333', '220', NULL, NULL, NULL, NULL, NULL, '[1,4,2]', 1, 'ot_2'),

        (0, NULL, NULL, '999', '333', '8', NULL, NULL, NULL, NULL, NULL, '[1,4]', 1, 'ot_2'),
        (0, NULL, NULL, '999', '333', '55', NULL, NULL, NULL, NULL, NULL, '[1,4]', 1, 'ot_2'),

   (0, NULL, NULL, '999', '444', NULL, NULL, NULL, NULL, NULL, NULL, '[1,4,2]', 1, 'ot_2'),


   (0, NULL, NULL, '999', '222', NULL, NULL, NULL, NULL, NULL, NULL, '[1,4,2]', 1, 'ot_2'),

        (0, NULL, NULL, '999', '222', '1', NULL, NULL, NULL, NULL, NULL, '[1,4,2]', 1, 'ot_2'),

        (0, NULL, NULL, '999', '222', '1', '4',NULL, NULL, NULL, NULL, '[1,4,2]', 1, 'ot_2'),
        (0, NULL, NULL, '999', '222', '1', '4',NULL, '16', NULL, NULL, '[4]', 1, 'ot_2'),
        (0, NULL, NULL, '999', '222', '1', '4',NULL, '451', NULL, NULL, '[4]', 1, 'ot_2'),
        (0, NULL, NULL, '999', '222', '1', '4',NULL, '110', NULL, NULL, '[4]', 1, 'ot_2'),
        (0, NULL, NULL, '999', '222', '1', '4',NULL, '13', NULL, NULL, '[4]', 1, 'ot_2'),
        (0, NULL, NULL, '999', '222', '1', '4',NULL, '269', NULL, NULL, '[4]', 1, 'ot_2'),



        (0, NULL, NULL, '999', '222', '1', '13', NULL, NULL, NULL, NULL, '[1,4]', 1, 'ot_2'),
        (0, NULL, NULL, '999', '222', '1', '13',NULL, '101', NULL, NULL, '[4]', 1, 'ot_2'),
        (0, NULL, NULL, '999', '222', '1', '13',NULL, '11', NULL, NULL, '[4]', 1, 'ot_2'),
        (0, NULL, NULL, '999', '222', '1', '13',NULL, '782', NULL, NULL, '[]', 1, 'ot_2'),
        (0, NULL, NULL, '999', '222', '1', '13',NULL, '713', NULL, NULL, '[]', 1, 'ot_2'),
        (0, NULL, NULL, '999', '222', '1', '13',NULL, '291', NULL, NULL, '[]', 1, 'ot_2'),

        (0, NULL, NULL, '999', '222', '1', '25', NULL, NULL, NULL, NULL, '[1,4,2]', 1, 'ot_2'),
        (0, NULL, NULL, '999', '222', '1', '25', NULL, '50', NULL, NULL, '[]', 1, 'ot_2'),
        (0, NULL, NULL, '999', '222', '1', '25', NULL, '272', NULL, NULL, '[4]', 1, 'ot_2'),


        (0, NULL, NULL, '999', '222', '1', '26', NULL, NULL, NULL, NULL, '[]', 1, 'ot_2'),

        (0, NULL, NULL, '999', '222', '1', '26', '62', NULL, NULL, NULL, '[1,4]', 1, 'ot_2'),
        (0, NULL, NULL, '999', '222', '1', '26', '61', NULL, NULL, NULL, '[]', 1, 'ot_2'),
        (0, NULL, NULL, '999', '222', '1', '26', '61', '526', NULL, NULL, '[4]', 1, 'ot_2'),
        (0, NULL, NULL, '999', '222', '1', '26', '61', '527', NULL, NULL, '[4]', 1, 'ot_2'),

        (0, NULL, NULL, '999', '222', '1', '26', NULL, '474', NULL, NULL, '[4]', 1, 'ot_2'),
        (0, NULL, NULL, '999', '222', '1', '26', NULL, '521', NULL, NULL, '[4]', 1, 'ot_2'),


        (0, NULL, NULL, '999', '222', '1', '12', NULL, NULL, NULL, NULL, '[1,4]', 1, 'ot_2'),
        (0, NULL, NULL, '999', '222', '1', '20', NULL, NULL, NULL, NULL, '[1,4]', 1, 'ot_2'),
        (0, NULL, NULL, '999', '222', '1', '3', NULL, NULL, NULL, NULL, '[1,4]', 1, 'ot_2'),
        (0, NULL, NULL, '999', '222', '1', '22', NULL, NULL, NULL, NULL, '[1,4]', 1, 'ot_2'),
        (0, NULL, NULL, '999', '222', '1', '18', NULL, NULL, NULL, NULL, '[1,4]', 1, 'ot_2'),


    (0, NULL, NULL, '999', '222', '30001', NULL, NULL, NULL, NULL, NULL, '[1,4,2]', 1, 'ot_2'),
    (0, NULL, NULL, '999', '222', '40001', NULL, NULL, NULL, NULL, NULL, '[1,4]', 1, 'ot_2'),
    (0, NULL, NULL, '999', '222', '40001', NULL, NULL, NULL, '16', NULL, '[4]', 1, 'ot_2'),
    (0, NULL, NULL, '999', '222', '50001', NULL, NULL, NULL, NULL, NULL, '[1,4,2]', 1, 'ot_2'),
    (0, NULL, NULL, '999', '222', '60001', NULL, NULL, NULL, NULL, NULL, '[1,4,2]', 1, 'ot_2'),
    (0, NULL, NULL, '999', '222', '70001', NULL, NULL, NULL, NULL, NULL, '[1,4]', 1, 'ot_2'),
    (0, NULL, NULL, '999', '222', '80001', NULL, NULL, NULL, NULL, NULL, '[1,4,2]', 1, 'ot_2');

#JD（岗位）表
ALTER TABLE `hr_jd` ADD COLUMN `job_group_id` int(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '职组ID' , ADD COLUMN `job_group_child_id` int(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '子职组ID' , ADD COLUMN `created_uid` int(10) DEFAULT NULL COMMENT '创建人' ,  ADD COLUMN `updated_uid` int(10) DEFAULT NULL COMMENT '更新人' , ADD COLUMN `created_uname` varchar(100) NOT NULL DEFAULT '' COMMENT '创建人名称' , ADD COLUMN `updated_uname` varchar(100) NOT NULL DEFAULT '' COMMENT '更新人名称' ,ADD INDEX `idx_job_group_id` USING BTREE (`job_group_id`) comment '子职组ID';

#更新历史jd数据提交人信息到新的创建人和更新人字段中
update hr_jd set created_uid=submitter_id,updated_uid=submitter_id,created_uname=submitter_name,updated_uname=submitter_name WHERE submitter_id <>0 and submitter_name is not null;

#添加职级字段
ALTER TABLE `hr_interview_offer` ADD COLUMN `job_title_grade` tinyint(2) UNSIGNED DEFAULT NULL COMMENT '职级';

#职位部门绑定关系表 修改
CREATE TABLE `hr_job_department_relation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `department_id` int(11) NOT NULL DEFAULT '0' COMMENT '部门id',
  `job_id` int(11) NOT NULL COMMENT '职位id',
  `job_level` varchar(100) NOT NULL DEFAULT '' COMMENT '职级 ：F0,F12-F24,value为数字部分',
  `public_job_name` varchar(100) NOT NULL DEFAULT '' COMMENT '公开职位名称',
  `group_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '职组ID',
  `group_child_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '子职组ID',
  `work_place_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '工作地点类型 1：网点，2：职能总部，3：业务总部，4：分拨、5：门店',
  `report_job_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '汇报职位ID',
  `jd_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'JD(岗位） id',
  `jd_desc` varchar(255) NOT NULL DEFAULT '' COMMENT '岗位描述',
  `plan_hc_nums` smallint(5) unsigned NOT NULL DEFAULT '0' COMMENT '计划hc 数量',
  `job_requirements_jybj` varchar(2000) NOT NULL DEFAULT '' COMMENT '职位要求-教育背景',
  `job_requirements_zyjl` varchar(2000) NOT NULL DEFAULT '' COMMENT '职位要求-专业经历',
  `job_requirements_other` varchar(1000) NOT NULL DEFAULT '' COMMENT '职位要求_其他',
  `job_competency_zy` varchar(2000) NOT NULL DEFAULT '' COMMENT '胜任力要求-专业胜任力，json内容（胜任力ID，level值）',
  `job_competency_ld` varchar(2000) NOT NULL DEFAULT '' COMMENT '胜任力要求-领导胜任力，json内容（胜任力ID，level值）',
  `job_competency_hx` varchar(2000) NOT NULL DEFAULT '' COMMENT '胜任力要求-核心胜任力,，json内容（胜任力ID）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `created_uid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '添加人',
  `created_uname` varchar(100) NOT NULL DEFAULT '' COMMENT '创建人名称',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updated_uid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '最后操作人',
  `updated_uname` varchar(100) NOT NULL DEFAULT '' COMMENT '数据更新人名称',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_depart_job` (`department_id`,`job_id`) USING BTREE,
  KEY `idx_groupid` (`group_id`) USING BTREE,
  KEY `idx_joblevel` (`job_level`) USING BTREE,
  KEY `idx_jobid` (`job_id`) USING BTREE,
  KEY `idx_jdid` (`jd_id`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='职位关联表';

#胜任力表
CREATE TABLE `hr_job_competency` (
  `id` int(4) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '胜任力名称',
  `type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '胜任力类型 1：领导胜任力，2：专业胜任力，3：核心胜任力',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0正常1删除',
  `description` varchar(500) NOT NULL DEFAULT '' COMMENT '胜任力描述',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COMMENT='职位管理-胜任力表';

#职组表
CREATE TABLE `hr_job_group` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `pid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '父ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '职组名称',
  `functional_competency_ids` varchar(500) NOT NULL DEFAULT '' COMMENT '专业胜任力ids，多个逗号隔开（一级职组才有该字段）',
  `add_userid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '添加人ID',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0正常1删除',
  `update_userid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最后更新人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0时区',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '0时区',
  `created_uname` varchar(100) NOT NULL DEFAULT '' COMMENT '创建人',
  `updated_uname` varchar(100) NOT NULL DEFAULT '' COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `pid` (`pid`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='职位管理-职组表';
