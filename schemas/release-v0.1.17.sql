--赵成成
CREATE TABLE `sys_office_address` (
  `id` varchar(10) NOT NULL COMMENT '网点编码',
  `name` varchar(100) DEFAULT NULL COMMENT '名字',
  `country_code` varchar(100) DEFAULT NULL COMMENT '省',
  `province_code` varchar(100) DEFAULT NULL COMMENT '国家',
  `city_code` varchar(100) DEFAULT NULL COMMENT '城市',
  `district_code` varchar(100) DEFAULT NULL COMMENT '区号',
  `postal_code` varchar(100) DEFAULT NULL COMMENT '邮编',
  `detail_address` varchar(100) DEFAULT NULL COMMENT '地址',
  `manage_region_id` varchar(100) DEFAULT NULL COMMENT '大区',
  `manage_piece_id` varchar(100) DEFAULT NULL COMMENT '片区',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='总部网点地址';


--backyard库执行（泰国/菲律宾/老挝）
ALTER TABLE hr_interview_offer
  ADD `deminimis_benefits` int(11) DEFAULT '0' COMMENT '最低福利-无税金补贴',
  ADD `performance_allowance` int(11) DEFAULT '0' COMMENT '绩效补贴',
  ADD `other_non_taxable_allowance` int(11) DEFAULT '0' COMMENT '其他无税金补贴';

---------------------------------------------------
-- 多国家 backyard 同步执行
alter table assets_order
add column use_people_id int(11) NOT NULL DEFAULT '0' COMMENT '使用人工号 个人资产用' after staff_info_id;

alter table assets_order
add column use_store_id varchar(20) NOT NULL DEFAULT '' COMMENT '使用地网点编号 公共资产用' after use_people_id;


-- 增加 用户输入的 油费 金额
alter table reimbursement_fuel
add column `no_tax_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '用户输入的不含税金额' after car_owner;

alter table reimbursement_fuel
add column `tax_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '用户输入的汗水金额' after no_tax_amount;


alter table reimbursement_fuel
add column  `invoices_ids` varchar(1000) DEFAULT NULL COMMENT '油费发票发票编号' after car_owner;


alter table reimbursement_fuel
add column  `traffic_invoices_ids` varchar(1000) DEFAULT NULL COMMENT '过路费发票发票编号' after invoices_ids;

alter table reimbursement_fuel
add column `traffic_no_tax_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '用户输入过路费的不含税金额' after tax_amount;

alter table reimbursement_fuel
add column `traffic_tax_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '用户输入过路费的不含税金额' after traffic_no_tax_amount;


alter table reimbursement_fuel
add column `object_code` varchar(90) NOT NULL DEFAULT '' COMMENT '科目code 交通费或者差旅费' after staff_info_id;

alter table  reimbursement_fuel
drop column  f_no;-- 线上没用 直接删调没问题

CREATE TABLE `reimbursement_fuel_connect` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
   `staff_info_id` int(10) unsigned DEFAULT NULL COMMENT '员工工号',
  `r_no` varchar(20) NOT NULL DEFAULT '' COMMENT '关联oa报销单订单编号',
  `f_no` varchar(30) NOT NULL DEFAULT '' COMMENT '用车记录编号',
   `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0 未删除 1 已删除',
  PRIMARY KEY (`id`),
  KEY `idx_r_no` (`r_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='by申请报销关联用车记录表';