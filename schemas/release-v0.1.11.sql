-- backyard 数据库结构变更 菲律宾同步执行
alter table staff_work_face_verify_record modify `verify_channel` tinyint(3) unsigned DEFAULT NULL COMMENT '验证渠道 阿里 0  腾讯 1 百度 2 ai 3 原图底片 4（验证结果存成功 1）5 静默活体';
alter table staff_work_face_verify_record add column   `device_type` varchar(30) DEFAULT NULL COMMENT '设备类型' after image_bucket;

-- backyard  不需要需要同步 多国家
INSERT INTO `setting_env` (`code`, `set_val`, `remark`)
VALUES
        ('IOS_3', '1.4.6', 'ios by 最新全量版本号 对应backyard:app:ios:version');

INSERT INTO `setting_env` (`code`, `set_val`, `remark`)
VALUES
        ('ANDROID_3', '1.7.1', '安卓 by 最新全量版本号 对应 backyard:app:android:version');
        

INSERT INTO `setting_env` (`code`, `set_val`, `remark`)
VALUES
   ('version_switch', '0', '灰度总开关version:gray_scale:switch_enabled'),
   ('LIST_ANDROID_3', '29589,28228,25504,21688,21672,20468,19989,19533,19055,17626,17625,17573,16954,19899,25162,19130,20503,21365,23357,24415,24701,24706,24707,24710,24711,24712,25156,21358,16966,20213,29825,30185,17715,35187,29855,31894,32244,17074,19900,27762,17063,17174,19933,28897,25228,17160,19011,17672,17550,34554,33469,24626,31451,17062,28162,17339,20359,17388,26475,30569,33311,33059,33920,62388,62301', '灰度名单 安卓version:gray_scale:staff_ids'),
   ('GREY_ANDROID_3', '1.7.1', '灰度版本号 gray_scale:version'),
   ('GREY_URL_ANDROID_3', 'http://ali-static-open.flashexpress.com/download/backyard-android/flashexpress-backyard-thRelease-v1.7.1.apk', '灰度下载链接 gray_scale:version:download_url'),
   ('ALL_URL_ANDROID_3', 'http://ali-static-open.flashexpress.com/download/backyard-android/flashexpress-backyard-thRelease-v1.7.1.apk', '全量下载链接 version:download_url');


---- 还有一个人 永远不升级

INSERT INTO `setting_env` (`code`, `set_val`, `remark`)
VALUES
   ('UNUP_IOS_3', '19112', '永远不提示升级的那个人 ios的ios:version:check:white_list');

INSERT INTO `setting_env` (`code`, `set_val`, `remark`)
VALUES
   ('release_note', 'พัฒนาประสิทธิภาพในการใช้งาน 
เพิ่มความรวดเร็วในการโหลด', '每次更新版本提示信息');


INSERT INTO `setting_env` (`code`, `set_val`, `remark`)
VALUES
        ('live_list', '402749,72798,66854,402764,398431,73568,72045,368044,71926,72922,358345,402722,397889,399405,399377,73544,402309,409412,409232,38024,399849,73637,73350,70964,141537,72603,73518,364608,73508,72558,72439,71919,73586,73428,26340,73724,73356,70914,403447,409030,65770,67979,409132,72285,71822,398678,71276,401921,71719,73547,398701,378567,73389,66421,59772,48876,73592,401711,402087,73304,182126,402728,70123,42027,409423,399961,73294,47533,399360,72827,68832,399310,72625,73374,72410,73459,399691,397713,73566,73689,25389,72571,73503,73319,60309,67029,70844,399920,73445,397780,60002,72615,72770,403047,409367,402306,398336,399323,70913,43568,408367,70485,394584,24964,409248,72683,397993,70703,399404,398726,399317,399731,60362,70544,399905,58683,72540,60232,28014,73087,399290,73643,35698,398333,399349,73113,55111,170609,23363,72367,402646,27671,68342,72826,70694,72846,403257,71524,73619,73035,35671,52127,57192,57013,68254,342371,171481,382959,335018,394060,361647,70523,40307,41927,58596,41887,59479,46666,53391,45901,58009,58006,40477,29687,58979,51288,41077,25912,28810,26605,23968,29868,25438,27532,27220,20645,25235,24742,24858,23848,23480,21972,19929,26216,27104,24302,24328,26809,24173,20751,35858,29155,28445,25434,24950,23839,21310,28178,35924,36190,58293,70532,73301,58256,41780,44394,39093,65869,65859,65860,73299,25162,67535,62853,28737', '静默活体检测是否测试员工');

INSERT INTO `setting_env` (`code`, `set_val`, `remark`)
VALUES
        ('alive_score', '0.7', '打卡 静默活体分数值');
-- 线上环境
INSERT INTO `setting_env` (`code`, `set_val`, `remark`)
VALUES
        ('ai_live_host', 'http://************:5101/v1/detect-live-face', 'ai 静默活体接口地址');
        
INSERT INTO `setting_env` (`code`, `set_val`, `remark`)
VALUES
        ('ai_face_host', 'http://************:5101/v1/compare-face', 'ai 人脸识别接口地址'); 
-- by 数据库 目前 线上数据
INSERT INTO `setting_env` (`code`, `set_val`, `remark`)
VALUES
   ('att_range_default',               '200', '打卡范围(单位:米)attendance_range'),
   ('field_punch_staff',               '55927', '员工工号:允许同类型网点打卡白名单field_punch_staff'),
   ('logistic_coordinate',             '100.3460576513603,13.787455136415179', 'FLASH LOGISTIC部门坐标flash_logistic:coordinates'),
   ('fulfillment_coordinate',          '100.573863,13.773394-100.568647,13.756638-100.647191,13.650532-100.745694,13.972500-100.647506,13.650458', 'fulfillment部门坐标fulfillment:coordinates'),
   ('store_temporary_coordinate',      '98.434823,19.349259,TH55010101-97.940867,18.139872,TH55010101-99.231384,19.938908,TH47190701-98.597494,16.720393,TH59010201-98.364885,18.499073,TH47020201-102.547278,11.634889,TH23010701-100.648417,13.651889,TH04060307-99.292152,9.118142,TH68170602-100.648434,13.651285,TH02060110-100.8024747,13.6080037,TH02060110-100.5406895,13.6047713,TH02030213-99.583083,7.114583,TH73040101-100.68241100,13.5964820,TH02030209-100.681601,13.593246,TH02030213-100.930806,13.567528,TH02030213-100.542859,13.605004,TH02030213-100.681601,13.593246,TH02060110-98.50368,8.335392,TH66070101-98.993111,7.919139,TH65080102-100.680917,13.59525,TH01470301-100.650113,14.200943,TH05110400-98.609231,19.113602,TH55030100-99.4010442,7.3106791,TH73020101-99.3071398,7.3754359,TH73020101-98.5747968,7.9331467,TH67030202-99.829493,10.100847,TH68050100-98.773356,7.739213,TH65010800-98.773387,7.759703,TH65010800-98.5747968,7.9331467,TH67030400-98.587869,8.097203,TH67030400-99.829278,10.100786,TH70010400-101.458032,12.574362,TH21010501-102.543812,11.653788,TH23010202', '网点临时考勤坐标store_temporary:coordinates'),
   ('connect_store',                   '', '网点编号:指定网点可以在其他网点打卡store_temporary:store_id');
   
   
-- 登陆 接口字段迁移 相关配置
INSERT INTO `setting_env` (`code`, `set_val`, `remark`)
VALUES
   ('field_punch_store_staff',               '37023,31014,32342,35187,28185,20579,26188,66884,69709,69767,16966', '员工工号:外勤打卡白名单field_punch_store_staff'),
   ('punch_department_switch',               '0', '总部外勤打卡开关field_punch_switch'),
   ('punch_department_id',             '40,93,159,160,161,162,64,17,81,170,171,46,38,10,156,157,158,73,22,35,36,37', '允许外勤打卡的总部部门id列表field_punch_department_ids'),
   ('punch_department_job',          '11,79,137,240,269,352,354,492,474,493,474,521', '允许外勤打卡的总部职位id列表field_punch_job_title_ids'),
   ('punch_department_staffs',      '33288,51283,34569,17024,17758,28916,21358,41784,33287,43574,57106,17062,36072,39462,28602,28574,17190,19982,35201,53700,58040,58467,19402,34610,33915,36213,35178,17627,54920,17574,34015,32628,19953,31342,40450,21318,19616,19055,58983,34014,32339,32214,39094,17603,19900,31253,34016,34017,40014,30989,27148,33282,33663,60642,28595,27691,29456,58291,45011,56008,51287,56812,57966,57951,34606,27918,55574,57969,56008,51287,56812,55856,39443,34787,36399,54262,55909,57952,56963,34820,57959,53635,57065,31857,31576,23477,17196,38636,55860,57066,57950,57966,57951,56663,57946,55866,56881,60809,55914,57629,57949,34819,55864,29194,25511,58066,57001,34803,54252,56808,33967,34185,55761,57973,57963,56828,57068,34610,25973,57941,33972,56805,57064,57961,33915,54919,31577,56010,57942,56955,56814,56809,57956,28558,56011,60721,56012,49588,56819,17026,35186,55855,56827,35201,56816,53749,17029,57968,51281,57957,33288,33920,45914,56813,36383,40489,56806,51275,54939,56195,57953,31047,19793,57960,36208,29846,57964,55867,56807,19936,35096,55911,45606,52862,55910,17022,56825,57948,55893,56307,33059,54934,17024,55861,57967,56817,35389,54253,56821,24323,59078,57000,57944,39261,28702,55894,53651,33918,37578,55865,56803,55871,33834,57947,37582,25921,52451,56820,55854,33916,56818,55857,34990,57945,55912,31840,55863,33832,34997,35047,56962,57620,56009,57628,51292,56822,57962,56788,56826,33835,32043,55862,28555,55753,56967,29825,55872,29362,32803,34248,34786,34806,38675,46704,51849,55218,55393,56579,56964,58068,58760,51290,27918,31287,31590,30185,54969,27691,29456,58291,42613,52100,38320,32150,54913,45011,22266,28141,17074,19199,17063,27009,61205,17008,17573,31856,56780,19990,40237,49077,21848,20278,51282,41984,20254,32342,30286,19987,28600,31849,35187,38643,58699,58698,32122,52731,17152,57335,17245,32125,33598,60779,60780,61436,23083,41075,54138,60429,17152,17178,17348,19432,19795,19796,20254,21706,21958,23116,24403,24902,24904,24905,24906,26808,28170,28728,28989,29480,29601,32739,33306,33837,35805,45612,48480,54249,54255,54677,54678,54893,55849,56003,56193,57624,57625,57626,58292,58294,60277,60404,60754,19900,19923,24022,24689,27695,32911,57720,58136,58466,58469,58755,58756,59297,55886,39460,55750,59046,59362,17008,17627,19982,23083,35610,38643,40237,41075,41984,43574,51282,53700,54138,57106,58467,58698,58699,60429,60641,57627,60722,58862,58636,58773,21866,42321,62958,64224,58040,63903,60274,58005,56326,56883,25209,64594,64595,65392,65865,65863,66387,66388,66386,66389,66414,62965,58983,30989,69709,69767', '总部外勤打卡白名单header_office:field_punch_staff');

INSERT INTO `setting_env` (`code`, `set_val`, `remark`)
VALUES
   ('miles_limit',  'TH47020201,TH59060104,TH13050401,TH71110201', '网点编号:里程汇报指定网点开放最大里程数 staff_mileage_code:store_ids');

-- 头盔、尾包 myj
INSERT INTO `interior_goods` (`id`, `goods_name_zh`, `goods_name_th`, `goods_name_en`, `goods_cate`, `img_path`, `info`, `status`, `deleted_at`, `created_at`, `updated_at`) VALUES (9, '安全帽/头盔', 'หมวกกันน็อค', '安全帽/头盔', 9, 'https://fle-training-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/oca-1623086070-6d421a8da3ca40448d428bac52352d3d.png', NULL, 1, NULL, '2021-06-07 16:40:54', '2021-06-07 17:15:14');
INSERT INTO `interior_goods` (`id`, `goods_name_zh`, `goods_name_th`, `goods_name_en`, `goods_cate`, `img_path`, `info`, `status`, `deleted_at`, `created_at`, `updated_at`) VALUES (10, '摩托车尾包', 'กระเป๋าติดท้ายรถมอเตอร์ไซค์', '摩托车尾包', 10, 'https://fle-training-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/oca-1623085989-8e4846505bd343d3beea9ff3b2d2dd80.png', NULL, 1, NULL, '2021-06-07 16:40:19', '2021-06-07 17:14:38');
INSERT INTO `interior_goods_sku` (`id`, `goods_id`, `goods_sku_code`, `goods_name_en`, `goods_name_th`, `goods_name_zh`, `img_path`, `attr_1`, `attr_2`, `unit_en`, `unit_th`, `unit_zh`, `unit_num`, `sale_num`, `surplus_num`, `total_num`, `price`, `status`, `deleted_at`, `created_at`, `updated_at`) VALUES (59, 9, 'FEX00012', 'หมวกกันน็อค', 'หมวกกันน็อค', '安全帽/头盔', 'https://fle-training-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/oca-1623086070-6d421a8da3ca40448d428bac52352d3d.png', 's', '', '', '', '件', 1, 3, 97, 0, 200.00, 1, NULL, '2021-06-07 16:44:25', '2021-06-08 13:00:37');
INSERT INTO `interior_goods_sku` (`id`, `goods_id`, `goods_sku_code`, `goods_name_en`, `goods_name_th`, `goods_name_zh`, `img_path`, `attr_1`, `attr_2`, `unit_en`, `unit_th`, `unit_zh`, `unit_num`, `sale_num`, `surplus_num`, `total_num`, `price`, `status`, `deleted_at`, `created_at`, `updated_at`) VALUES (60, 10, 'FEX00016', 'กระเป๋าติดท้ายรถมอเตอร์ไซค์', 'กระเป๋าติดท้ายรถมอเตอร์ไซค์', '摩托车尾包', 'https://fle-training-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/oca-1623085989-8e4846505bd343d3beea9ff3b2d2dd80.png', 's', '', '', '', '件', 1, 8, 0, 0, 1090.00, 1, NULL, '2021-06-07 16:45:53', '2021-06-08 15:02:39'); 
UPDATE `assets_goods` set `deleted` = 1 where `bar_code` = "FEX00012";
UPDATE `assets_goods` set `deleted` = 1 where `bar_code` = "FEX00016";
   

-- 泰国  backyard #@刘春华 
CREATE TABLE `staff_mobile_company_notice_Log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `staff_info_id` int(10) unsigned NOT NULL COMMENT '员工ID',
  `job_title` varchar(64) DEFAULT NULL COMMENT '职位',
  `send_type` tinyint(1)  NOT NULL default '0' COMMENT '发送类型1:直发 2:抄送',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `staff_info_id` (`staff_info_id`) USING BTREE,
  KEY `job_title` (`job_title`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='员工公司号码为空通知记录表';
