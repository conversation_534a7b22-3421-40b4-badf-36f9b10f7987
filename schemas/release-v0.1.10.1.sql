---------backyard 库

CREATE TABLE `hc_approval_task` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `is_deal` tinyint(1) DEFAULT '0',
  `staff_id` int(11) NOT NULL COMMENT '审批人',
  `create_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
 `total_num` int(11) DEFAULT '0',
  `success_num` int(11) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_key` (`create_at`,`staff_id`,`is_deal`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `hc_approval_detail` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `hc_task_id` int(11) DEFAULT NULL COMMENT '审批id',
  `export_time` datetime DEFAULT NULL COMMENT '导出时间',
  `hc_id` int(11) DEFAULT NULL,
  `branch_id` varchar(100) DEFAULT NULL,
  `job_name` varchar(255) DEFAULT NULL,
  `reason` varchar(500) DEFAULT NULL,
  `demand_num` int(4) DEFAULT NULL,
  `priority` int(10) DEFAULT NULL,
  `approval_result` varchar(250) DEFAULT NULL COMMENT '审批结果',
  `cause_rejection` varchar(250) DEFAULT NULL COMMENT '驳回原因',
  `state` tinyint(1) DEFAULT '1' COMMENT '1未处理2审批通过3驳回',
  `error_message` varchar(255) DEFAULT NULL COMMENT '错误原因',
  `create_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`hc_task_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;



CREATE TABLE `hc_priority_record` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL COMMENT '操作人',
  `hc_id` int(11) DEFAULT NULL,
  `is_success` tinyint(1) DEFAULT '0' COMMENT '1成功2失败',
  `error_message` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `priority_id` int(11) DEFAULT NULL,
  `create_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'CURRENT_TIMESTAMP',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


INSERT INTO `hr_permission_auth`(`id`, `pid`, `class`, `function`, `auth_code`, `describe`, `is_switch`, `updated_at`) VALUES (155, 5, 'hc', 'exportHc', 'exportHc', 'HC管理-HC审批导出', 0, '2021-06-01 08:26:46');
INSERT INTO `hr_permission_auth`(`id`, `pid`, `class`, `function`, `auth_code`, `describe`, `is_switch`, `updated_at`) VALUES (156, 5, 'hc', 'uploadHc', 'uploadHc', 'HC管理-HC批量导入', 0, '2021-06-01 08:26:57');
INSERT INTO `hr_permission_auth`(`id`, `pid`, `class`, `function`, `auth_code`, `describe`, `is_switch`, `updated_at`) VALUES (157, 5, 'hc', 'isApproveDone', 'isApproveDone', 'HC管理-HC是否完成', 0, '2021-06-01 08:27:51');
INSERT INTO `hr_permission_auth`(`id`, `pid`, `class`, `function`, `auth_code`, `describe`, `is_switch`, `updated_at`) VALUES (158, 5, 'hc', 'exportFailHc', 'exportFailHc', 'HC管理-HC失败导出', 0, '2021-06-01 08:28:39');
INSERT INTO `hr_permission_auth`(`id`, `pid`, `class`, `function`, `auth_code`, `describe`, `is_switch`, `updated_at`) VALUES (159, 5, 'hc', 'exportTemplate', 'exportTemplate', 'HC管理-HC优先级模版', 0, '2021-06-01 08:30:16');
INSERT INTO `hr_permission_auth`(`id`, `pid`, `class`, `function`, `auth_code`, `describe`, `is_switch`, `updated_at`) VALUES (160, 5, 'hc', 'uploadHcPriority', 'uploadHcPriority', 'HC管理-HC导入优先级', 0, '2021-06-01 08:30:49');
