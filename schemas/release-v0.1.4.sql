-- wdb
UPDATE `workflow_node_relate_base` SET `valuate_formula` = '$p1 == 15 || ($p1 == 1 && $p2 < 3) || $p3 == 1',`valuate_code` = 'getK,getK1,finish_flow',remark='只有休息日年假小于3天的走二级审批',sort = 50
WHERE from_node_id = 60 and to_node_id = 62;
UPDATE `workflow_node_relate_base` SET `valuate_formula` = null,`valuate_code` = null , sort = 10
WHERE from_node_id = 60 and to_node_id = 95;
UPDATE `workflow_node_relate_base` SET `valuate_formula` = '($p1 == 1 && $p2 >=6) || (!in_array($p1,[1,19]) && $p2 >= 3)',`valuate_code` = 'getK,getK1'
WHERE from_node_id = 95 and to_node_id = 96;
UPDATE `workflow_node_relate_base` SET `valuate_formula` = '$p1 == 19 || ($p1 == 1 && $p2 >=3 && $p2 < 6) || (!in_array($p1,[1,19]) && $p2 < 3) || $p3 == 1',`valuate_code` = 'getK,getK1,finish_flow' ,sort = 50
WHERE from_node_id = 95 and to_node_id = 62;
UPDATE `workflow_node_relate_base` SET `valuate_formula` = '$p1 == 13 || $p1 == 93'
WHERE from_node_id = 5 and to_node_id = 168;
UPDATE `workflow_node_relate_base` SET `valuate_formula` = '$p1 == 1 || $p2 = 93' , valuate_code = 'shopFlowType, getSubmitterDepartment' ,sort = 40
WHERE from_node_id = 168 and to_node_id = 7;



INSERT INTO `workflow_node_relate_base` (`flow_id`, `code`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`)
VALUES
(9, NULL, 59, 62, '$p1 == 1 ', 'finish_flow', '如果审批流的审批节点中碰到一级部门负责人或者CEO或者COO，则审批结束后即可完成审批束', 60);

delete from workflow_node_base where id in (61,6,166,57,65);

delete from workflow_node_relate_base where from_node_id in (61,6,166,57,65) or to_node_id in (61,6,166,57,65 );


-- 执行sql cls
insert into workflow_node_relate_base (flow_id, code, from_node_id, to_node_id, valuate_formula, valuate_code, remark, sort) values
(9, null, 60, 62, '$p1 == 1', 'is_end', '审批流是上级审批且满足结束条件', 70),
(9, null, 95, 62, '$p1 == 1', 'is_end', '审批流是上级审批且满足结束条件', 70),
(9, null, 96, 62, '$p1 == 1', 'is_end', '审批流是上级审批且满足结束条件', 70);

insert into workflow_node_relate_base  (flow_id, code, from_node_id, to_node_id, valuate_formula, valuate_code, remark, sort) values
(9, null, 59, 62, '$p1 == 1', 'is_end', '审批流是上级审批且满足结束条件', 70);