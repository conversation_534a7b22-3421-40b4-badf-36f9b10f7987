#workflow
INSERT INTO workflow (id, code, name, type, relate_type, description, flow_request, created_at, updated_at)
VALUES (21, null, 'KIT罚款申诉', 1, 34, null, null, now(), now());


INSERT INTO workflow_node_base (flow_id, code, name, type, auditor_type, auditor_level, auditor_id,
                                approval_policy, specify_approver, deleted, created_at, updated_at)
VALUES (21, DEFAULT, '申请节点', 0, 1, null, null, DEFAULT, null, DEFAULT, DEFAULT, DEFAULT);

INSERT INTO workflow_node_base (flow_id, code, name, type, auditor_type, auditor_level, auditor_id,
                                approval_policy, specify_approver, deleted, created_at, updated_at)
VALUES (21, DEFAULT, '审批节点', 1, 103, null, null, DEFAULT, null, DEFAULT, DEFAULT, DEFAULT);

INSERT INTO workflow_node_base (flow_id, code, name, type, auditor_type, auditor_level, auditor_id,
                                approval_policy, specify_approver, deleted, created_at, updated_at)
VALUES (21, DEFAULT, '结束节点', 99, 1, null, null, DEFAULT, null, DEFAULT, DEFAULT, DEFAULT);

#workflow_node_relate_base
INSERT INTO workflow_node_relate_base (flow_id, code, from_node_id, to_node_id, valuate_formula, valuate_code,
                                       remark, sort, deleted, updated_at, created_at)
VALUES (21, null, 231, 232, null, null, null, DEFAULT, DEFAULT, DEFAULT, DEFAULT);

INSERT INTO workflow_node_relate_base (flow_id, code, from_node_id, to_node_id, valuate_formula, valuate_code,
                                       remark, sort, deleted, updated_at, created_at)
VALUES (21, null, 232, 233, null, null, null, DEFAULT, DEFAULT, DEFAULT, DEFAULT);

CREATE TABLE `penalty_appeal`
(
    `id`                  int(11)        NOT NULL AUTO_INCREMENT,
    `staff_id`            int(11)        NOT NULL DEFAULT '0' COMMENT '申请人staff_id',
    `serial_no`           varchar(30)    NOT NULL DEFAULT '' COMMENT '编号',
    `store_id`            varchar(10)    NOT NULL DEFAULT '' COMMENT '申请人所在网点',
    `penalty_reason`      varchar(30)    NOT NULL DEFAULT '' COMMENT '处罚原因',
    `happen_date`         date                    DEFAULT NULL COMMENT '发生日期',
    `related_info`        varchar(30)    NOT NULL DEFAULT '' COMMENT '关联信息',
    `penalty_money`       decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '申诉金额',
    `apply_remark`        varchar(500)   NOT NULL DEFAULT '' COMMENT '申请备注',
    `image`               text,
    `status`              tinyint(4)     NOT NULL DEFAULT '0' COMMENT '审批状态 1 待审核 2 审核通过 3 驳回 4 撤销',
    `reject_reason`       varchar(500)            DEFAULT '' COMMENT '驳回原因',
    `process_result`      varchar(30)    NOT NULL DEFAULT '' COMMENT 'QAQC处理结果',
    `process_remark`      varchar(500)   NOT NULL DEFAULT '' COMMENT 'QAQC处理备注',
    `penalty_after_money` decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '申诉后的金额',
    `approval_notice`     varchar(30)    NOT NULL DEFAULT '' COMMENT '审批须知',
    `created_at`          timestamp      NULL     DEFAULT CURRENT_TIMESTAMP,
    `updated_at`          timestamp      NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `penalty_id`          int(11)        NOT NULL DEFAULT '0' COMMENT 'kit 系统处罚id，与bi系统通信用',
    PRIMARY KEY (`id`),
    KEY `idx_updated_at` (`updated_at`),
    KEY `idx_status` (`status`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='kit处罚申诉表'


