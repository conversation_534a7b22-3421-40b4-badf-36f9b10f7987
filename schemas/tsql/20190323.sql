CREATE TABLE `backyard_img` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '工单表自增ID',
  `origin_id` varchar(32) NOT NULL COMMENT '上传图片关联外键',
  `oss_bucket_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '上传图片类型(1:工单详情,2:工单回复)',
  `bucket_name` varchar(63) DEFAULT NULL COMMENT 'oss bucketName',
  `object_key` varchar(100) DEFAULT NULL COMMENT 'oss 对象 key 值',
  `deleted` tinyint(3) NOT NULL DEFAULT '0' COMMENT '逻辑删除 1:已逻辑删除 0：未逻辑删除',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `rotate_angle` int(10) DEFAULT '0' COMMENT '图片旋转角度：0、90、180、270',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='oss图片';

CREATE TABLE `mail_reply_from_ceo` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `mail_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '关联mail_to_ceo表主键',
  `staff_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '回复员工id',
  `staff_name` varchar(50) NOT NULL DEFAULT '' COMMENT '回复员工',
  `content` text COMMENT 'ceo站内信回复内容',
  `create_time` datetime DEFAULT NULL COMMENT '回复时间(已转成泰国时区)',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='员工站内信回复表';

CREATE TABLE `mail_to_ceo` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `staff_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
  `staff_name` varchar(50) NOT NULL DEFAULT '' COMMENT '姓名',
  `content` text COMMENT 'ceo站内信内容',
  `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
  `type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '问题类型:1=建议；2=工资问题；3=提成问题；4=投诉&举报；5=其他',
  `is_reply` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否回复 0 - 未回复 1- 已回复',
  `create_time` datetime DEFAULT NULL COMMENT '用户提交时间(已转成泰国时区)',
  `is_read` tinyint(4) DEFAULT '0' COMMENT '0=未读；1=已读',
  `img_url` varchar(512) DEFAULT NULL COMMENT '图片地址：以逗号隔开',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='员工给ceo发送站内信表附件表 关联work_order_img oss_bucket_type=3';

CREATE TABLE `staff_audit` (
  `audit_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '审批ID',
  `staff_info_id` int(10) unsigned DEFAULT NULL COMMENT '员工工号',
  `manager_id` int(10) unsigned DEFAULT NULL COMMENT '主管ID',
  `audit_type` tinyint(1) unsigned DEFAULT '0' COMMENT '审核类型 1补卡 2请假',
  `attendance_type` int(1) DEFAULT '1' COMMENT '考勤类型 1上班打卡 2下班打卡',
  `leave_type` int(10) DEFAULT '1' COMMENT '请假类型',
  `attendance_date` date DEFAULT NULL COMMENT '考勤日期',
  `reissue_card_date` datetime DEFAULT NULL COMMENT '补卡时间',
  `leave_start_time` datetime DEFAULT NULL COMMENT '请假开始时间',
  `leave_start_type` tinyint(1) DEFAULT '1' COMMENT '请假开始时间类型 1上午 2下午',
  `leave_end_time` datetime DEFAULT NULL COMMENT '请假结束时间',
  `leave_end_type` tinyint(1) DEFAULT '1' COMMENT '请假结束时间类型 1上午 2下午',
  `leave_day` decimal(10,1) DEFAULT NULL COMMENT '请假天数',
  `status` tinyint(1) DEFAULT '1' COMMENT '补卡状态 1 申请中 2 审核通过 3 补卡驳回',
  `audit_reason` text COMMENT '申请原因',
  `reject_reason` text COMMENT '驳回原因',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`audit_id`),
  KEY `staff_info_id` (`staff_info_id`),
  KEY `audit_type` (`audit_type`),
  KEY `status` (`status`),
  KEY `attendance` (`attendance_date`),
  KEY `lindex_evel` (`leave_start_time`,`leave_end_time`,`leave_start_type`,`leave_end_type`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='员工审批表';


CREATE TABLE `staff_audit_images` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '审批图片ID',
  `audit_id` bigint(10) unsigned DEFAULT NULL COMMENT '审核ID',
  `image_path` varchar(255) DEFAULT NULL COMMENT '图片路径',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='审批图片表';