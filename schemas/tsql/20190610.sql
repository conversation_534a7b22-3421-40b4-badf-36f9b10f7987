
CREATE TABLE `staff_audit_union` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `id_union` varchar(16) NOT NULL COMMENT '数据对应id',
  `staff_id_union` int(10) DEFAULT NULL COMMENT '用户id',
  `type_union` tinyint(3) DEFAULT NULL COMMENT '类型集 1-补卡 2-请假 3-LH 4-OT 9-物料',
  `status_union` tinyint(3) DEFAULT NULL COMMENT '状态集 1-待审批 2-已同意 3-驳回 4-撤销 5-审批超时',
  `store_id` varchar(10) NOT NULL COMMENT '网点id',
  `data` text COMMENT '数据集',
  `table` varchar(20) DEFAULT NULL COMMENT '表名',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=115123 DEFAULT CHARSET=utf8mb4;


ALTER TABLE `backyard`.`staff_audit_union`
ADD INDEX `staff_id_index`(`staff_id_union`) USING BTREE,
ADD INDEX `status_index`(`status_union`) USING BTREE,
ADD INDEX `created_at`(`created_at`) USING BTREE,
ADD INDEX `id_union_index`(`id_union`) USING BTREE;
