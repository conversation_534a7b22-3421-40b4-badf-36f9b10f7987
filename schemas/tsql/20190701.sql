<<<<<<< HEAD
ALTER TABLE `backyard`.`staff_work_attendance_attachment`
ADD INDEX `staff_id_index`(`staff_info_id`) USING BTREE COMMENT '员工id索引';

=======
<<<<<<< HEAD
>>>>>>> feature/luyao/fleet_audit
CREATE TABLE `fleet_audit` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `car_type` int(3) NOT NULL COMMENT '车辆类型',
  `capacity` int(10) NOT NULL COMMENT '装载量',
  `expected_date` datetime(3) NOT NULL COMMENT '期望到达日期',
  `plan_date` datetime(3) DEFAULT NULL COMMENT '单向-计划到达时间',
  `plan_back_date` datetime(3) DEFAULT NULL COMMENT '双向-计划到达时间',
  `start_store` varchar(10) NOT NULL COMMENT '出发网点',
  `end_store` varchar(10) NOT NULL COMMENT '目的网点',
  `region` varchar(255) DEFAULT NULL COMMENT '区域',
  `reason` varchar(500) NOT NULL COMMENT '申请理由',
  `reject_reason` varchar(500) DEFAULT NULL COMMENT '拒绝理由',
  `line_id` varchar(32) DEFAULT NULL COMMENT '单边线路id',
  `line_back_id` varchar(32) DEFAULT NULL COMMENT '双边线路id',
  `single_line` tinyint(3) DEFAULT NULL COMMENT '是否为单一线路 1-是  2-不是',
  `system_quote` int(11) unsigned DEFAULT '0' COMMENT '系统报价',
  `abnormal_cost` int(11) DEFAULT '0' COMMENT '异常费用 ',
  `final_cost` int(11) unsigned DEFAULT '0' COMMENT '最终费用',
  `submitter_id` int(10) unsigned NOT NULL COMMENT '申请人id',
  `status` tinyint(3) NOT NULL COMMENT '审批状态 1-待审批 2-同意  3-撤销 4-驳回 5-超时',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `final_approver` int(10) DEFAULT '0' COMMENT '最终审批人id',
  `final_approval_time` datetime DEFAULT NULL COMMENT '最终审批时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


CREATE TABLE `staff_audit_approval` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `type` int(3) DEFAULT NULL COMMENT '审批类型',
  `level` int(10) unsigned NOT NULL COMMENT '级别',
  `audit_id` int(10) DEFAULT NULL COMMENT '审批id',
  `status` int(10) DEFAULT NULL,
  `submitter_id` varchar(11) DEFAULT NULL,
  `updatetime` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `createtime` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `staff_ids` varchar(500) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `audit_id` (`audit_id`) USING BTREE,
  KEY `updatetime` (`updatetime`) USING BTREE,
  KEY `createtime` (`createtime`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='backyard我的审批表';
<<<<<<< HEAD
=======
=======
ALTER TABLE `backyard`.`staff_work_attendance_attachment`
ADD INDEX `staff_id_index`(`staff_info_id`) USING BTREE COMMENT '员工id索引';
>>>>>>> master
>>>>>>> feature/luyao/fleet_audit
