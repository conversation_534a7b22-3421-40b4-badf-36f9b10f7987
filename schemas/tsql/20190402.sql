

--  backyard 数据库添加下面表
-- ----------------------------
-- Table structure for message
-- ----------------------------
DROP TABLE IF EXISTS `message`;
CREATE TABLE `message` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '巴枪消息表ID',
  `title` varchar(500) DEFAULT '' COMMENT '标题',
  `content` mediumtext COMMENT '内容',
  `add_userid` int(11) NOT NULL DEFAULT '0' COMMENT '添加用户ID',
  `last_edit_userid` int(11) NOT NULL DEFAULT '0' COMMENT '最后一次编辑用户ID',
  `publish_status` tinyint(4) NOT NULL DEFAULT '6' COMMENT '消息状态（6:发布；9:挂起）',
  `top_status` tinyint(4) NOT NULL DEFAULT '3' COMMENT '置顶状态（1.永久置顶；2:按时间区间置顶；3:未置顶）',
  `top_starttime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '置顶开始时间',
  `top_endtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '置顶结束时间',
  `to_group` varchar(100) NOT NULL DEFAULT '' COMMENT '消息接受人群（1:在编快递员；2:非在编快递员；3:在编仓管员；4:非在编仓管员；5:全部在编；6:全部非在编),多个全体之间用|分割',
  `isdel` tinyint(4) NOT NULL DEFAULT '9' COMMENT '是否删除（6:删除，9:未删除）',
  `remote_message_id` varchar(32) DEFAULT NULL COMMENT '关联推送端消息ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后一次修改时间',
  `is_push` tinyint(4) DEFAULT '0' COMMENT '1=已发送，0=未发送',
  PRIMARY KEY (`id`),
  KEY `remote_message_id` (`remote_message_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COMMENT='巴枪推送消息';



-- ----------------------------
-- Table structure for message_courier
-- ----------------------------
DROP TABLE IF EXISTS `message_courier`;
CREATE TABLE `message_courier` (
  `id` varchar(32) NOT NULL DEFAULT '' COMMENT '主键',
  `staff_info_id` int(10) unsigned DEFAULT NULL COMMENT '员工ID',
  `title` varchar(800) DEFAULT NULL COMMENT '消息标题',
  `category` tinyint(1) NOT NULL DEFAULT '0' COMMENT '消息类型 0: 普通消息 1: 平台收件派单，2:同事收件转单，3:同事派件转单，4:平台取消订单，5:快递员奖罚通知',
  `top_state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否置顶 0: 不置顶 1: 置顶',
  `read_state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读 0: 未读 1: 已读',
  `push_state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '推送状态 0: 未发送 1: 已发送',
  `push_time` datetime(3) DEFAULT NULL COMMENT '发送时间',
  `source_type` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源 0:mns,1:自定义,2:老数据迁移',
  `created_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '添加时间',
  `updated_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '修改时间',
  `mns_message_id` varchar(100) DEFAULT NULL COMMENT '阿里云消息队列ID确保唯一性使用',
  `message_content_id` varchar(32) NOT NULL COMMENT '消息表内容表id，与message 表 remote_message_id  关联',
  PRIMARY KEY (`id`),
  UNIQUE KEY `mns_message_id` (`mns_message_id`),
  KEY `idx_staff_info_id` (`staff_info_id`),
  KEY `msg_content_id` (`message_content_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户消息表';
