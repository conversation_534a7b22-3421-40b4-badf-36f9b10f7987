
-- backyard
CREATE TABLE `hr_staff_work_days` (
  `staff_info_id` int(10) unsigned not NULL default 0 COMMENT '员工工号',
  `month` varchar(10) NOT NULL default '' COMMENT '对应月份 2019-04',
  `date_at` date DEFAULT NULL COMMENT '申请日期',
  `operator` int(10) unsigned not NULL default 0 COMMENT '配置人工号',
  `remark` varchar(80) not null default '' comment '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`staff_info_id`,`month`,`date_at`),
  KEY `idx_date_at` (`date_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工班次轮休表';