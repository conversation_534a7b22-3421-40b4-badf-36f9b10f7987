
CREATE TABLE `staff_audit_leave_split` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `audit_id` bigint(20) unsigned NOT NULL COMMENT '关联staff_audit 请假id',
  `staff_info_id` int(10) unsigned DEFAULT NULL COMMENT '员工工号',
  `date_at` date DEFAULT NULL COMMENT '请假日期',
  `type` tinyint not null default 0 comment '请假日期类型 0-一天 1-上午 2-下午',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_audit_id` (`audit_id`),
  KEY `idx_staff_info_id` (`staff_info_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工请假拆分成天';