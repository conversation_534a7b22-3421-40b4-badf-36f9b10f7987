
CREATE TABLE `staff_mobile` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) unsigned DEFAULT '0' COMMENT '工号',
  `staff_mobile` bigint(11) unsigned DEFAULT '0' COMMENT '手机号',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0:未删除；1:已删除；',
  `create_id` int(11) unsigned DEFAULT '0' COMMENT '创建人',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `create_id` (`create_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='员工手机表'

