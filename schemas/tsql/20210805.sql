DROP TABLE IF EXISTS `work_pass`;
CREATE TABLE `work_pass`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(10) NOT NULL COMMENT '员工ID',
  `expiration_date` date NOT NULL COMMENT '过期时间',
  `created_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0),
  `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1:已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_staff_Id`(`staff_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact;
