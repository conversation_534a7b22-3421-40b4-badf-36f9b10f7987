-- backyard库 start
-- 9195--电子警告书-举报操作记录-增加字段 -李杰
ALTER TABLE `report_audit_log`
    ADD COLUMN `remark` varchar(500) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '原因' AFTER `created_at`;
-- backyard库 end


-- bi 库 start
-- 9195-电子警告书-增加权限 -李杰
INSERT INTO `menus`(`id`, `parentid`, `type`, `name`, `route_action`, `status`, `created_at`, `updated_at`, `name_en`, `name_th`, `mark`, `sort`, `name_key`, `developer`, `icon`) VALUES (459, 296, 1, '电子警告书', 'warning_warning_add_warning', 1, NULL, '2020-08-24 09:47:16', '', '', NULL, 35.0, 'warning_menu', '李杰', NULL);
INSERT INTO `menus`(`id`, `parentid`, `type`, `name`, `route_action`, `status`, `created_at`, `updated_at`, `name_en`, `name_th`, `mark`, `sort`, `name_key`, `developer`, `icon`) VALUES (460, 296, 1, '发送记录', 'warning_warning_record_list', 1, NULL, '2020-08-24 09:47:16', '', '', NULL, 35.1, 'warning_record_list', '李杰', NULL);
INSERT INTO `menus`(`id`, `parentid`, `type`, `name`, `route_action`, `status`, `created_at`, `updated_at`, `name_en`, `name_th`, `mark`, `sort`, `name_key`, `developer`, `icon`) VALUES (461, 296, 1, '举报列表', 'report_list', 1, NULL, '2020-08-24 09:47:16', '', '', NULL, 35.2, 't_report_list', '李杰', NULL);
INSERT INTO `menus`(`id`, `parentid`, `type`, `name`, `route_action`, `status`, `created_at`, `updated_at`, `name_en`, `name_th`, `mark`, `sort`, `name_key`, `developer`, `icon`) VALUES (462, 461, 1, '操作', 'report_cancel_warning_action', 1, NULL, '2020-08-24 09:47:16', '', '', NULL, 35.3, 'action', '李杰', NULL);
INSERT INTO `menus`(`id`, `parentid`, `type`, `name`, `route_action`, `status`, `created_at`, `updated_at`, `name_en`, `name_th`, `mark`, `sort`, `name_key`, `developer`, `icon`) VALUES (463, 296, 1, '犯罪记录', 'crime_list', 1, NULL, '2020-08-24 09:47:16', '', '', NULL, 35.3, 'crime_list', '李杰', NULL);
INSERT INTO `menus`(`id`, `parentid`, `type`, `name`, `route_action`, `status`, `created_at`, `updated_at`, `name_en`, `name_th`, `mark`, `sort`, `name_key`, `developer`, `icon`) VALUES (464, 463, 1, '操作', 'crime_operating_action', 1, NULL, '2020-08-24 09:47:16', '', '', NULL, 35.3, 'action', '李杰', NULL);

-- 增加翻译
INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'en', 'warning_record_list', '发送记录', '2021-06-21 03:35:56', null, 1);
INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'th', 'warning_record_list', '发送记录', '2021-06-21 03:35:56', null, 1);
INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'zh-CN', 'warning_record_list', '发送记录', '2021-06-21 03:35:56', null, 1);

INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'en', 'crime_list', '犯罪记录', '2021-06-21 03:35:56', null, 1);
INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'th', 'crime_list', '犯罪记录', '2021-06-21 03:35:56', null, 1);
INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'zh-CN', 'crime_list', '犯罪记录', '2021-06-21 03:35:56', null, 1);

#-- 【FBI】【BY】消息模块优化-2021.05.19 -刘春华
alter table questionnaire_lib add first_department_id varchar(10) not null default '0' comment '创建者一级部门', add index idx_first_department_id (first_department_id);
INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'en', 'send_message_no_permissions', '您没有发送消息的权限，请联系HRD部门协助发送', '2021-06-30 03:35:56', null, 1);
INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'th', 'send_message_no_permissions', '您没有发送消息的权限，请联系HRD部门协助发送', '2021-06-30 03:35:56', null, 1);
INSERT INTO translations ( lang, t_key, t_value, created_at, updated_at, type) VALUES ( 'zh-CN', 'send_message_no_permissions', '您没有发送消息的权限，请联系HRD部门协助发送', '2021-06-30 03:35:56', null, 1);
-- bi 库 end


-- 工服订单+字段
ALTER TABLE `interior_orders`
    ADD COLUMN `node_sn` varchar(255) NULL COMMENT '网点id或者pc_code(下单的时候给scm)' AFTER `remark`;