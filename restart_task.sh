#!/bin/bash
#by <EMAIL>

function echo_log(){
   echo -e "\033[0;31m $1 \033[0m"
}
function echo_blue {
   echo -e "\033[47;34m $1 \033[0m"
}

#重启addv2 主路由消费 
#php cli.php routeaddv2
function send_push() {
    echo_blue "1 restart send_push"
    if [ "$1" = "1" ];then
        ps -ef |grep send_push |awk '{print $2}' |xargs kill
        echo_blue "Done，Please start"
    fi
}

function close_email_rocket() {
    echo_blue "2 restart close_email_rocket"
    if [ "$1" = "1" ];then
        ps -ef |grep close_email_rocket |awk '{print $2}' |xargs kill
        echo_blue "Done，Please start"
    fi
}

function cancel_pdpa() {
    echo_blue "3 restart cancel_pdpa"
    if [ "$1" = "1" ];then
        ps -ef |grep cancel_pdpa |awk '{print $2}' |xargs kill
        echo_blue "Done，Please start"
    fi
}

function create_approval() {
    echo_blue "4 restart create_approval"
    if [ "$1" = "1" ];then
        ps -ef |grep create_approval |awk '{print $2}' |xargs kill
        echo_blue "Done，Please start"
    fi
}

function jobtransfer_do_transfer() {
    echo_blue "5 restart jobtransfer_do_transfer"
    if [ "$1" = "1" ];then
        ps -ef |grep jobtransfer_do_transfer |awk '{print $2}' |xargs kill
        echo_blue "Done，Please start"
    fi
}

echo_log "你即将操作是重启队列的操作 请在次确认"
send_push
close_email_rocket
cancel_pdpa
create_approval
jobtransfer_do_transfer


read -p "please input number: " number

case $number in
    1)
       send_push 1
    ;;
    2)
       close_email_rocket 1
    ;;
    3)
       cancel_pdpa 1
    ;;
    4)
       create_approval 1
    ;;
    5)
       jobtransfer_do_transfer 1
    ;;
    *)
       echo_log "input error"
    ;;
esac
