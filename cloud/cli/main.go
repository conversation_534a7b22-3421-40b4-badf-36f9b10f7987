package main
 
import (
    "encoding/json"
    "net/http"
    "strings"
	"io/ioutil"
	"crypto/tls"
    "log"
    "github.com/astaxie/beego"
    "github.com/astaxie/beego/httplib"
    "github.com/hprose/hprose-golang/rpc"
)


type EnvelopeCard_Statement_API struct {
    XMLName xml.Name `xml:"Envelope"`
    Body    struct {
        Card_Statement_API struct {
            StrJson string `xml:"strJson"`
        } `xml:"Card_Statement_API"`
    } `xml:"Body"`
}

type EnvelopeCheck_Balance_API struct {
    XMLName xml.Name `xml:"Envelope"`
    Body    struct {
        Check_Balance_API struct {
            StrJson string `xml:"strJson"`
        } `xml:"Check_Balance_API"`
    } `xml:"Body"`
}

type EnvelopeAES256GCM_Decrypt_API struct {
    XMLName xml.Name `xml:"Envelope"`
    Body    struct {
        AES256GCM_Decrypt_API struct {
            StrJson string `xml:"strJson"`
        } `xml:"AES256GCM_Decrypt_API"`
    } `xml:"Body"`
}
type EnvelopeGenerate_JWT_Token struct {
    XMLName xml.Name `xml:"Envelope"`
    Body    struct {
        Generate_JWT_Token struct {
            StrJson string `xml:"strJson"`
        } `xml:"Generate_JWT_Token"`
    } `xml:"Body"`
}

func beegoXml(WSDL string){
    req := httplib.Post(WSDL)
    body := `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
        <soapenv:Header/>
        <soapenv:Body>
        <tem:Generate_JWT_Token>
            <!--Optional:-->
            <tem:user_id>dd22e1f1-3993-436f-8bab-da04f5459551</tem:user_id>
            <!--Optional:-->
            <tem:role>Admin</tem:role>
            <!--Optional:-->
            <tem:api_name>Check_Balance_API</tem:api_name>
        </tem:Generate_JWT_Token>
        </soapenv:Body>
    </soapenv:Envelope>`
    req.SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
    req.Header("Content-Type","text/xml; charset=UTF-8")
    req.Body(strings.NewReader(body))
    str, err := req.String()
    if err != nil {
        log.Println(err)
    }
    log.Println(str)
}
 
func Generate_JWT_Token() (string){
    WSDL := beego.AppConfig.String("PT.WSDL")
    body := `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
        <soapenv:Header/>
        <soapenv:Body>
        <tem:Generate_JWT_Token>
            <!--Optional:-->
            <tem:user_id>dd22e1f1-3993-436f-8bab-da04f5459551</tem:user_id>
            <!--Optional:-->
            <tem:role>Admin</tem:role>
            <!--Optional:-->
            <tem:api_name>Check_Balance_API</tem:api_name>
        </tem:Generate_JWT_Token>
        </soapenv:Body>
    </soapenv:Envelope>`
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}
    res, err := client.Post(WSDL, "text/xml; charset=UTF-8", strings.NewReader(body))
    result := make(map[string]interface{})
    if nil != err {
        log.Println("http post err:", err)
        result["code"] = -1
        result["data"] = ""
        result["msg"] = err 
    }
    defer res.Body.Close()
    
	log.Println(res.StatusCode)
    // return status
    if 200 != res.StatusCode {
        log.Println("WebService soap1.1 request fail, status: %s\n", res.StatusCode)
        result["code"] = -1
        result["data"] = ""
        result["msg"] = err 
    }
 
    rawbody, err := ioutil.ReadAll(res.Body)
    log.Println("webService soap1.1 response: ", string(rawbody))
    
    result["code"] = 0
    result["data"] = string(rawbody)
    result["msg"] = "ok"

    if nil != err {
        log.Println("ioutil ReadAll err:", err)
        result["code"] = -1
        result["data"] = ""
        result["msg"] = err 
    }

	resA, _ := json.Marshal(result)
	return string(resA)
}

func AES256GCM_Decrypt_API(decryptText string) (string){
    hexKey := beego.AppConfig.String("PT.ENC_HEX_KEY")
    hexIV := beego.AppConfig.String("PT.ENC_HEX_IV")
    body := `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
        <soapenv:Header/>
        <soapenv:Body>
        <tem:AES256GCM_Decrypt_API>
            <!--Optional:-->
            <tem:decryptText>`+decryptText+`</tem:decryptText>
            <!--Optional:-->
            <tem:hexKey>`+hexKey+`</tem:hexKey>
            <!--Optional:-->
            <tem:hexIV>`+hexIV+`</tem:hexIV>
        </tem:AES256GCM_Decrypt_API>
        </soapenv:Body>
    </soapenv:Envelope>`
	return WSDL_POST("",body)
}
func AES256GCM_Encrypt_API(plainTextJson string) (string){
    hexKey := beego.AppConfig.String("PT.ENC_HEX_KEY")
    hexIV := beego.AppConfig.String("PT.ENC_HEX_IV")
    body := `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
        <soapenv:Header/>
        <soapenv:Body>
        <tem:AES256GCM_Encrypt_API>
            <!--Optional:-->
            <tem:plainTextJson>`+plainTextJson+`</tem:plainTextJson>
            <!--Optional:-->
            <tem:hexKey>`+hexKey+`</tem:hexKey>
            <!--Optional:-->
            <tem:hexIV>`+hexIV+`</tem:hexIV>
        </tem:AES256GCM_Encrypt_API>
        </soapenv:Body>
    </soapenv:Envelope>`
	return WSDL_POST("",body)
}

func WSDL_POST(head,body string)(string){
    WSDL := beego.AppConfig.String("PT.WSDL")
    tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}
    resp, err := http.NewRequest("POST",WSDL,strings.NewReader(body))
    resp.Header.Set("Content-Type", "text/xml; charset=UTF-8")
    if head != "" {
        resp.Header.Set("Authorization", "Bearer "+head)
    }
   
    res, err := client.Do(resp)

    result := make(map[string]interface{})
    if nil != err {
        log.Println("http post err:", err)
        result["code"] = -1
        result["data"] = ""
        result["msg"] = err 
    }
    defer res.Body.Close()
    
	log.Println(res.StatusCode)
    // return status
    if 200 != res.StatusCode {
        log.Println("WebService soap1.1 request fail, status: %s\n", res.StatusCode)
        result["code"] = -1
        result["data"] = ""
        result["msg"] = err 
    }
 
    rawbody, err := ioutil.ReadAll(res.Body)
    log.Println("webService soap1.1 response: ", string(rawbody))
    
    result["code"] = 0
    result["data"] = string(rawbody)
    result["msg"] = "ok"

    if nil != err {
        log.Println("ioutil ReadAll err:", err)
        result["code"] = -1
        result["data"] = ""
        result["msg"] = err 
    }

	resA, _ := json.Marshal(result)
	return string(resA)
}

func Check_Balance_API(head,strJson string) (string){
    body := `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
        <soapenv:Header/>
        <soapenv:Body>
        <tem:Check_Balance_API>
            <!--Optional:-->
            <tem:strJson>`+strJson+`</tem:strJson>
        </tem:Check_Balance_API>
        </soapenv:Body>
    </soapenv:Envelope>`
	return WSDL_POST(head,body)
}

func Card_Statement_API(head,strJson string) (string){
    body := `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
        <soapenv:Header/>
        <soapenv:Body>
        <tem:Card_Statement_API>
            <!--Optional:-->
            <tem:strJson>`+strJson+`</tem:strJson>
        </tem:Card_Statement_API>
        </soapenv:Body>
    </soapenv:Envelope>`
	return WSDL_POST(head,body)
}


func main() {
    
    service := rpc.NewHTTPService()
    service.AddFunction("AES256GCM_Decrypt_API", AES256GCM_Decrypt_API)
    service.AddFunction("AES256GCM_Encrypt_API", AES256GCM_Encrypt_API)
    service.AddFunction("Generate_JWT_Token", Generate_JWT_Token)
    service.AddFunction("Check_Balance_API", Check_Balance_API)
    service.AddFunction("Card_Statement_API", Card_Statement_API)
	
	beego.Handler("/rpc", service)
	beego.Run()
}
