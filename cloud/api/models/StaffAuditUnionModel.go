package models

import (
	"backyard/cloud/api/common/raw"
	"backyard/cloud/api/library"
	"github.com/astaxie/beego/logs"
	"strconv"
	"strings"
)

/*
 * 继承基础model
 */
type StaffAuditUnionModel struct {
	BaseModel
}

/**
 * 获取待审核数量
 * @Access  public
 * @Param   params
 * @Return  int64
 */
func (c *StaffAuditUnionModel) WaitAuditNum(params raw.StaffAuditUnion) int64 {
	//返回自	定义结构体
	templateList := struct {
		Cou int64
	}{}
	//用户校验
	staffData := library.CheckoutStaff(params.StaffIdUnion, 0)
	//员工不存在返回
	if len(staffData) <= 0 {
		panic("1001")
	}
	//[3]存在直属下级
	higherStaffId := library.GetSubordinateStaffId(params.StaffIdUnion)
	var where []string
	if len(higherStaffId) > 0 {
		var higherIds []string
		for _, value := range higherStaffId {
			higherIds = append(higherIds, value["staff_info_id"])
		}
		where = append(where, "(`staff_id_union` in ("+strings.Join(higherIds, ",")+") and `status_union` = '101')")
	}

	//HC审批
	where = append(where, "(approval_id = "+strconv.Itoa(int(params.StaffIdUnion))+" and `status_union` = '207')")
	condition := strings.Join(where, " or ")

	countSql := `SELECT
					count(*) AS cou
				 FROM 
					staff_audit_union
 				 WHERE ` + condition
	err := c.GetDI("db").Query(countSql).FetchOne(&templateList)
	if err != nil {
		logs.Error(err.Error())
	}
	return templateList.Cou
}
