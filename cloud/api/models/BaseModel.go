package models

import (
	"backyard/cloud/api/common/log"
	"backyard/cloud/api/common/raw"
	"backyard/cloud/api/db"
	"backyard/cloud/api/utils"
	"flag"
	"github.com/astaxie/beego"
	"github.com/astaxie/beego/orm"
	_ "github.com/go-sql-driver/mysql"
	"os"
)

var Time_zone = "+07:00"

type BaseModel struct {
	TABLE_NAME string
	DB_NAME    string
	ORM_CON    orm.Ormer
	OS         orm.QuerySeter
	COND       *orm.Condition
	//MongoSchema db.MongoInterface
	MysqlSchema db.MyInterface
}

func (c *BaseModel) _init() {
	c.ORM_CON = orm.NewOrm()
	if c.DB_NAME != "" {
		dbErr := c.ORM_CON.Using(c.DB_NAME)
		if dbErr != nil {
			panic(dbErr)
		}
	}
	c.OS = c.ORM_CON.QueryTable(c.TABLE_NAME)
	c.COND = orm.NewCondition()
}

func (c *BaseModel) GetDI(dbName string) db.MyInterface {
	c.MysqlSchema = db.MysqlConnection("", dbName, "")
	return c.MysqlSchema
}

func (c *BaseModel) _InitMyqlConnect(dbName, tableName string) db.MyInterface {
	c.MysqlSchema = db.MysqlConnection("", dbName, tableName)
	return c.MysqlSchema
}

//func (c *BaseModel) _InitMongoDBConnect(conf, collection string) db.MongoInterface {
//	c.MongoSchema = db.MongoConnection(
//		utils.DbConfig.String(conf+"::mongoConnDevice"),
//		utils.DbConfig.String(conf+"::mongoDbNameDevice"),
//		collection,
//	)
//	return c.MongoSchema
//}

func (c *BaseModel) nilToString(byt interface{}) interface{} {
	if byt == nil {
		return ""
	}
	return byt
}
func init() {
	//接收终端conf参数.env的路径
	var confFile = flag.String("conf", "../../.env", "config file of mysql(host|user|pwd...)")
	flag.Parse()
	//读取配置
	utils.LoadConf(*confFile)
	//注册backyard
	err := orm.RegisterDataBase("default", "mysql", utils.GetDBConn("database_"), 500, 200)
	if err != nil {
		log.Logger.Error(err.Error())
		os.Exit(1)
	}
	//注册fle
	err_fle := orm.RegisterDataBase("db_fle", "mysql", utils.GetDBConn("database_fle_"), 500, 200)
	if err_fle != nil {
		log.Logger.Error(err_fle.Error())
		os.Exit(1)
	}
	//注册bi
	err_bi := orm.RegisterDataBase("db_rbi", "mysql", utils.GetDBConn("database_rbi_"), 500, 200)
	if err_bi != nil {
		log.Logger.Error(err_bi.Error())
		os.Exit(1)
	}

	//注册coupon
	err_coupon := orm.RegisterDataBase("db_coupon", "mysql", utils.GetDBConn("database_coupon_"), 500, 200)
	if err_coupon != nil {
		log.Logger.Error(err_coupon.Error())
		os.Exit(1)
	}
	//注册coupon从库
	err_coupon_r := orm.RegisterDataBase("db_coupon_r", "mysql", utils.GetDBConn("database_coupon_r_"), 500, 200)
	if err_coupon_r != nil {
		log.Logger.Error(err_coupon_r.Error())
		os.Exit(1)
	}
	//是否开启orm debug模式
	if beego.AppConfig.String("server::ormdebug") == "true" {
		orm.Debug = true
	}
	//orm.DefaultTimeLoc = time.UTC
	//注册数据库表(非必须)
	orm.RegisterModel(
		&raw.BusinessTripImg{},
	)
	db.OrmCondition = orm.NewOrm()
}
