package models

import (
	"backyard/cloud/api/common/raw"
	"github.com/astaxie/beego/logs"
)

/*
 * 继承基础model
 */
type BusinessTripImgModel struct {
	BaseModel
}

/*
 * 获取列表
 */
func (c *BusinessTripImgModel) GetList(params raw.BusinessTripImg) (int64, []raw.BusinessTripImg) {
	//sql := `SELECT
	//			id,name,organization_id
	//		FROM
	//			staff_info
	//		WHERE
	//			organization_id = ? and state = 1`
	//_, err := c.GetDI("db_fle").Query(sql, params.OrganizationId).FetchAll(&staff_info)
	//if err != nil {
	//	logs.Error(err.Error())
	//}
	templateList := []raw.BusinessTripImg{} //表的结构体
	sql := `SELECT
				*
			FROM 
				business_trip_img
			WHERE
				deleted=0 limit ?,?`
	sum, atter := c.GetDI("db").Query(sql, params.Offset, params.Limit).FetchAll(&templateList)
	if atter != nil {
		logs.Error(atter.Error())
	}
	if len(templateList) <= 0 {
		return 0, templateList
	}

	for key, value := range templateList {
		templateList[key].CreatedAtStr = value.CreatedAt.Format("2006-01-02 15:04:05")
		templateList[key].UpdatedAtStr = value.UpdatedAt.Format("2006-01-02 15:04:05")
	}

	//TODO一些逻辑
	return sum, templateList
}

//func (c *BootAnimationModel) Remove(params raw.Bootanimation) bool {
//	c.InitMysqlConnect("", new(raw.Bootanimation).TableName())
//	if c.MysqlSchema.Remove(map[string]interface{}{"id": params.Id}) <= 0 {
//		return false
//	}
//	return true
//}

//func (c *BootAnimationModel) Modify(params raw.Bootanimation) bool {
//	c.InitMysqlConnect("", new(raw.Bootanimation).TableName())
//	if params.Id <= 0 {
//		c.MysqlSchema.Insert(&params)
//		if params.Id <= 0 {
//			return false
//		}
//		return true
//	}
//	options := orm.Params{
//		"title":      params.Title,
//		"fileUrl":    params.FileUrl,
//		"fileMd5":    params.FileMd5,
//		"type":       params.Type,
//		"status":     params.Status,
//		"editor":     params.Editor,
//		"updateTime": params.UpdateTime,
//		"describe":   params.Describe,
//	}
//	query := map[string]interface{}{
//		"id": params.Id,
//	}
//	count := c.MysqlSchema.Update(query, options)
//	if count <= 0 {
//		return false
//	}
//	return true
//}
//
//func (c *BootAnimationModel) InitMysqlConnect(dbName, tableName string) db.MysqlInterface {
//	return c._InitMyqlConnect(dbName, tableName)
//}

// func (c *WrapModel) InsertOne(params raw.Wrap) int64 {
// 	c.InitMysqlConnect("", new(raw.Wrap).TableName())
// 	return c.MysqlSchema.Insert(&params)
// }
