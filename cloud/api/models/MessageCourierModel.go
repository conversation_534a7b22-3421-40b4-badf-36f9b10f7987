package models

import (
	"backyard/cloud/api/common/log"
	"backyard/cloud/api/common/raw"
)

/*
 * 继承基础model
 */
type MessageCourierModel struct {
	BaseModel
}

/*
 * 获取列表
 */
func (c *MessageCourierModel) ReadMessageList(params raw.MessageCourier) (int64, []raw.MsgItem) {
	templateList := []raw.MsgItem{} //返回自定义结构体
	sql := `select 
				mc.id as msg_id,mc.title,LEFT(UNIX_TIMESTAMP(mc.updated_at),10) as push_time,mc.read_state,mc.top_state,mc.category,
                mc.update_state
			from 
				message_courier  as mc
			where 
				mc.staff_info_id=?  and mc.created_at > DATE_SUB(CURDATE(), INTERVAL 60 DAY) and mc.is_del=0
			order by 
				mc.top_state desc,mc.updated_at desc
			limit ?,?;`
	sum, atter := c.GetDI("db_coupon_r").Query(sql, params.StaffInfoId, params.Offset, params.Limit).FetchAll(&templateList)

	if atter != nil {
		log.Logger.Error(atter.Error())
	}
	if len(templateList) <= 0 {
		return 0, templateList
	}
	//TODO一些逻辑
	return sum, templateList
}

/*
 * backyard 消息未读
 */
func (c *MessageCourierModel) BackyardUnreadNum(params raw.MessageCourier) int64 {
	templateList := struct {
		BackyardUnreadNum int64
	}{}
	sql := `SELECT 
				count(0) as backyard_unread_num 
			FROM  
				message_courier  
			WHERE 
				read_state=0  and  staff_info_id=? and created_at > DATE_SUB(CURDATE(), INTERVAL 60 DAY) and is_del=0`
	err := c.GetDI("db").Query(sql, params.StaffInfoId).FetchOne(&templateList)
	if err != nil {
		log.Logger.Error(err.Error())
	}
	return templateList.BackyardUnreadNum
}
