package config

import (
	"backyard/cloud/api/common/config"
)

var (
	MysqlConnStr, ListenPort, AccessKey, SecretKey, SuperSign string
	YYBSearchUrl, Lang                                        string
)

func InitConf(confFile string) {
	config.InitConf(confFile)
	ListenPort, _ = config.DBConf.GetValue("server", "port")
	AccessKey, _ = config.DBConf.GetValue("sign", "ak")
	SecretKey, _ = config.DBConf.GetValue("sign", "sk")
	SuperSign, _ = config.DBConf.GetValue("sign", "superSign")
	YYBSearchUrl, _ = config.DBConf.GetValue("third", "yybSearchUrl")
}

//用户登陆
type UserInfo struct {
	StaffId          int64   `json:"staff_id"`
	Id               int64   `json:"id"`
	Name             string  `json:"name"`
	Mobile           string  `json:"mobile"`
	AvatarUrl        string  `json:"avatar_url"`
	OrganizationId   string  `json:"organization_id"`
	OrganizationName string  `json:"organization_name"`
	OrganizationType int64   `json:"organization_type"`
	StoreCategory    int64   `json:"store_category"`
	FranchiseeId     int64   `json:"franchisee_id"`
	FranchiseeName   string  `json:"franchisee_name"`
	DepartmentId     string  `json:"department_id"`
	JobTitle         int64   `json:"job_title"`
	Positions        []int64 `json:"positions"`
}
