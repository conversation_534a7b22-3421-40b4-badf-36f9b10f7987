package routers

import (
	"backyard/cloud/api/controllers"
	"github.com/astaxie/beego"
)

//路由
func init() {
	beego.Router("/*", &controllers.BaseController{}, "options:Options")
	beego.Router("/example/list", &controllers.ExampleController{}, "post:List")
	beego.Router("/backyard/msg_list", &controllers.BackyardController{}, "get,post:MsgLst")
	beego.Router("/backyard/un_read_and_audit", &controllers.BackyardController{}, "get:UnReadAndAudit")
	//beego.Router("/cloud/example/modify", &controllers.ExampleController{}, "post:Modify")
}
