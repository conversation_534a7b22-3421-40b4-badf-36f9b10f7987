package utils

import (
	configer "backyard/cloud/api/config"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/astaxie/beego/cache"
	_ "github.com/astaxie/beego/cache/redis"
	"github.com/astaxie/beego/config"
	"io/ioutil"
	"math/rand"
	"net/http"
	"os"
	"strings"
	"time"
)

var DbConfig config.Configer

//系统返回结构体
type Response struct {
	Code int         `json:"code"`
	Msg  interface{} `json:"msg"`
	Data interface{} `json:"data,omitempty"`
}

//controllers返回结构体
type ReturnJson struct {
	Code int         `json:"code"`
	Msg  interface{} `json:"message"`
	Data interface{} `json:"data,omitempty"`
}

//验证用户登陆结构体
type ApiToken struct {
	JsonRpc string `json:"jsonrpc"`
	Method  string `json:"method,omitempty"`
	Id      int64  `json:"id"`
	Error   struct {
		Code    int    `json:"code,omitempty"`
		Message string `json:"message,omitempty"`
	} `json:"error"`
	Params interface{}       `json:"params,omitempty"`
	Result configer.UserInfo `json:"result"`
}

func LoadConf(dbConfigPath string) {
	var err error
	DbConfig, err = config.NewConfig("ini", dbConfigPath)
	if err != nil {
		fmt.Printf("read .env failed: %s\n", err)
		os.Exit(1)
	}
}

func GetDBConn(config string) string {
	dbhost := DbConfig.DefaultString(config+"host", "0.0.0.0:3306")
	dbuser := DbConfig.DefaultString(config+"username", "root")
	dbpassword := DbConfig.DefaultString(config+"password", "")
	db := DbConfig.DefaultString(config+"dbname", "")
	conn := dbuser + ":" + dbpassword + "@tcp(" + dbhost + ")/" + db + "?charset=utf8&loc=Asia%2FShanghai"
	return conn
}

func GetRedis() cache.Cache {
	host := DbConfig.DefaultString("redis_host", "127.0.0.1")
	port := DbConfig.DefaultString("redis_port", "6379")
	key := DbConfig.DefaultString("redis_prefix", "bk_")
	auth := DbConfig.DefaultString("redis_auth", "")
	redisConfig := `{"conn":"` + host + `:` + port + `","key":"` + key + `","dbNum":"0","password":"`+auth+`"}`
	conn, err := cache.NewCache("redis", redisConfig)
	if err != nil {
		fmt.Printf("%s\n", err)
		os.Exit(1)
	}
	return conn
}

func GetServicePort() string {
	port := DbConfig.String("servicePort")
	return port
}

func GetBetterImg(imgUrl string) string {
	sol := "_260_360"
	if strings.Contains(imgUrl, "_260_360") {
		return imgUrl
	}
	var ind int
	var ret string
	if strings.HasSuffix(imgUrl, ".jpg") {
		ind = strings.Index(imgUrl, ".jpg")
		ret = imgUrl[0:ind] + sol + ".jpg"
	} else if strings.HasSuffix(imgUrl, ".jpeg") {
		ind = strings.Index(imgUrl, ".jpeg")
		ret = imgUrl[0:ind] + sol + ".jpeg"
	} else if strings.HasSuffix(imgUrl, ".png") {
		ind = strings.Index(imgUrl, ".png")
		ret = imgUrl[0:ind] + sol + ".png"
	}
	return ret
}

/**
 * sha1加密
 * @param str
 * @param key
 * @return string
 */
func HmacSha1(str string, key string) string {
	h := hmac.New(sha1.New, []byte(key))
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

/**
 * md5加密
 * @param str
 * @return string
 */
func MD5(body string) string {
	h := md5.New()
	h.Write([]uint8(body))
	return hex.EncodeToString(h.Sum(nil))
}

/**
 * java验证数据登陆
 * @param method
 * @param param
 * @param url
 * @return ApiToken|error
 */
func GetApiDatApiToken(method string, param []interface{}, url string) (ApiToken, error) {
	result := ApiToken{
		Result: configer.UserInfo{},
	}
	param_slice := []interface{}{
		map[string]string{"locale": configer.Lang},
	}
	param_slice = append(param_slice, param...)
	rand.Seed(time.Now().UnixNano())
	json_data := ApiToken{
		JsonRpc: "2.0",
		Method:  method,
		Id:      int64(rand.Int31()),
		Params:  param_slice,
	}
	buffer, err := json.Marshal(json_data)
	if err != nil {
		return result, err
	}
	resp, re_err := http.Post(url, "application/json", strings.NewReader(string(buffer)))
	if re_err != nil {
		return result, re_err
	}
	defer resp.Body.Close()
	body, body_err := ioutil.ReadAll(resp.Body)
	if body_err != nil {
		return result, body_err
	}
	json.Unmarshal(body, &result)
	if result.Error.Message != "" {
		return result, errors.New(result.Error.Message)
	}
	result.Result.StaffId = result.Result.Id
	return result, nil
}
