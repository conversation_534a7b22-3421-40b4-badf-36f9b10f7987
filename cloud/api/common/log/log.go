package log

import (
	"os"

	logging "github.com/op/go-logging"
)

var (
	Logger *logging.Logger
	format logging.Formatter
)

func InitLogger(mod string) {
	Logger = logging.MustGetLogger(mod)
	format = logging.MustStringFormatter(`%{color}%{time:15:04:05.000} %{shortfile} %{shortfunc} ▶ %{level:.4s} %{id:03x}%{color:reset} %{message}`)
	backend1 := logging.NewLogBackend(os.Stdout, "", 0)
	backend1Formatter := logging.NewBackendFormatter(backend1, format)
	logging.SetBackend(backend1Formatter)
}
