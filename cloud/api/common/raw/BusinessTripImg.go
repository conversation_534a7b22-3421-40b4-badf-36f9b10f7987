package raw

import "time"

//定义数据表模型
type BusinessTripImg struct {
	Id             int64     `orm:"pk" json:"id"` //orm标签、json标签
	BusinessTripId int64     `json:"business_trip_id"`
	ImgPath        string    `orm:"size(500)" json:"image_path"`
	Deleted        int64     `json:"deleted"`
	CreatedAt      time.Time `json:"-"`                  //json的时候忽略此字段
	UpdatedAt      time.Time `json:"-"`                  //日期类型
	CreatedAtStr   string    `orm:"-" json:"created_at"` //orm的时候忽略此字段
	UpdatedAtStr   string    `orm:"-" json:"update_at"`
	Offset         int64     `orm:"-" json:"-"`
	Limit          int64     `orm:"-" json:"-"`
}

func (c *BusinessTripImg) TableName() string {
	return "business_trip_img"
}
