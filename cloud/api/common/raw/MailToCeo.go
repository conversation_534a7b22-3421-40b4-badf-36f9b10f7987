package raw

import "time"

//定义数据表模型
type MailToCeo struct {
	Id          int64 `orm:"pk"` //orm标签、json标签
	StaffInfoId int64
	StaffName   string `orm:"size(50)"`
	Content     string `orm:"type(text)"`
	Mobile      string `orm:"size(20)"`
	Type        int64
	IsReply     int64
	CreateTime  time.Time
	IsRead      int64
	ImgUrl      string `orm:"size(512)"`
	Offset      int64  `orm:"-" json:"-"`
	Limit       int64  `orm:"-" json:"-"`
}

func (c *MailToCeo) TableName() string {
	return "mail_to_ceo"
}
