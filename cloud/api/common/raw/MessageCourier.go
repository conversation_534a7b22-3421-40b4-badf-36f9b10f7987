package raw

import "time"

//定义数据表模型
type MessageCourier struct {
	Id               string `orm:"size(32);pk"` //orm标签、json标签
	StaffInfoId      int64
	Title            string `orm:"size(800)"`
	Category         int64
	TopState         int64
	ReadState        int64
	PushState        int64
	PushTime         time.Time
	SourceType       int64
	CreatedAt        time.Time
	UpdateAt         time.Time
	MnsMessageId     string `orm:"size(100)"`
	MessageContentId string `orm:"size(32)"`
	Offset           int64  `orm:"-" json:"-"`
	Limit            int64  `orm:"-" json:"-"`
}

func (c *MessageCourier) TableName() string {
	return "message_courier"
}

/*
 * 定义返回结构体
 */
type MsgListReturnJson struct {
	Data     []MsgItem `json:"data"`
	LastPage int64     `json:"last_page"`
}
type MsgItem struct {
	MsgId       string `json:"msg_id"`
	Title       string `json:"title"`
	PushTime    int64  `json:"push_time"`
	ReadState   int64  `json:"read_state"`
	TopState    int64  `json:"top_state"`
	Category    int64  `json:"category"`
	UpdateState int64  `json:"update_state"`
}
