package raw

import "time"

//定义数据表模型
type StaffAuditUnion struct {
	Id           int64  `orm:"pk"` //orm标签、json标签
	IdUnion      string `orm:"size(16)"`
	StaffIdUnion int64
	TypeUnion    int64
	StatusUnion  int64
	StoreId      string `orm:"size(10)"`
	Data         string `orm:"type(text)"`
	Table        string `orm:"size(20)"`
	CreateAt     time.Time
	OriginId     int64
	ApprovalId   int64
	Offset       int64 `orm:"-" json:"-"`
	Limit        int64 `orm:"-" json:"-"`
}

func (c *StaffAuditUnion) TableName() string {
	return "staff_audit_union"
}
