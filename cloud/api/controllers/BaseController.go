package controllers

import (
	"backyard/cloud/api/config"
	"backyard/cloud/api/messages"
	"backyard/cloud/api/models"
	"backyard/cloud/api/utils"
	"encoding/json"
	"fmt"
	"github.com/astaxie/beego"
	"github.com/astaxie/beego/logs"
	"net/http"
	"runtime/debug"
	"strconv"
	"strings"
	"time"
)

const (
	ZERO              = iota
	PLATFORM_IPHONE   = "FB_IPHONE"
	PLATFORM_ANDROID  = "FB_ANDROID"
	PLATFORM_KIT      = "RB_KIT"
	PLATFORM_BACKYARD = "FB_BACKYARD"
	PLATFORM_CLIENT   = "CLIENT_HRIS"
)

type BaseController struct {
	beego.Controller
	response                      utils.Response
	returnJson                    utils.ReturnJson
	Platform, Lang, XFleSessionId string
	UserInfo                      config.UserInfo
}

type _Init interface {
	_Init()
}

func (c *BaseController) DealHeaders() {
	allowHeaders := "Qsc-Token,Content-Type,x-access-token,x-url-path"
	allowOrigin := "*"

	if c.Ctx.Input.Header("HTTP_ACCESS_CONTROL_REQUEST_HEADERS") != "" {
		allowHeaders = c.Ctx.Input.Header("HTTP_ACCESS_CONTROL_REQUEST_HEADERS")
	}

	if c.Ctx.Input.Header("Origin") != "" {
		allowOrigin = c.Ctx.Input.Header("Origin")
	}

	c.Ctx.Output.Header("Content-Type", "application/json;charset=utf-8")
	c.Ctx.Output.Header("Access-Control-Allow-Origin", allowOrigin)
	c.Ctx.Output.Header("Access-Control-Allow-headers", allowHeaders)
	c.Ctx.Output.Header("Access-Control-Allow-Methods", "POST, GET, OPTIONS, DELETE")
	c.Ctx.Output.Header("Access-Control-Request-Method", "*")
	c.Ctx.Output.Header("Access-Control-Request-Headers", "*")
	c.Ctx.Output.Header("Access-Control-Allow-Credentials", "true")
	c.Ctx.Output.Header("Access-Control-Expose-Headers", "X-My-Custom-Header, X-Another-Custom-Header")
}

func (c *BaseController) Options() {
	c.DealHeaders()
	c.response.Code = 1
	c.response.Msg = "请求成功!"
	c.Data["json"] = c.response
	c.ServeJSON()
	return
}

func (c *BaseController) ServeJSON(encoding ...bool) {
	c.DealHeaders()
	c.Controller.ServeJSON(encoding...)
}

func (c *BaseController) Catch() {
	if r := recover(); r != nil {
		c.response.Code = CODE_PARSE_DATA_FAIL
		message := fmt.Sprintf("%s", r)
		mapValue, ok := c.GetTranslation()[message]
		if ok {
			message = mapValue
		}
		c.response.Msg = message
		c.LogDebuger(message)
		c.Data["json"] = c.response
		c.ServeJSON()
	}
}

func (c *BaseController) FormatTrace(r interface{}) string {
	return fmt.Sprintf(
		"[%s] [%s] [%s]",
		c.Ctx.Request.RequestURI,
		c.Ctx.Input.RequestBody,
		fmt.Sprintf("%s", r),
	)
}

func (c *BaseController) LogDebuger(r interface{}) {
	logs.Error(c.FormatTrace(r) + " [--MESSAGE--]: " + strings.Replace(string(debug.Stack()), "\n", "", -1))
}

func (c *BaseController) _init() {
	c.response, c.returnJson = utils.Response{
		Code: CODE_OK,
		Msg:  MSG_OK,
	}, utils.ReturnJson{
		Code: CODE_OK,
		Msg:  MSG_OK,
	}
}

/**
 * 验证用户登录 状态 ，若未登录 ，则中转到登录页面
 */
func (c *BaseController) Prepare() {
	if c.Ctx.Request.Method == http.MethodOptions {
		return
	}
	x_fle_session_id := c.Ctx.Request.Header.Get("X-FLE-SESSION-ID")
	if x_fle_session_id == ""{
		x_fle_session_id = c.Ctx.Request.Header.Get("X-By-SESSION-ID")
	}
	platform := c.Ctx.Request.Header.Get("By-Platform")
	language := c.Ctx.Request.Header.Get("Accept-Language")
	timezone := c.Ctx.Request.Header.Get("Timezone")
	if timezone == "+07:00" || timezone == "+08:00" {
		models.Time_zone = timezone
	}
	if x_fle_session_id == "" || language == "" {
		c.response.Code = 0
		c.response.Msg = struct {
			XbySessionId   string `json:"X-By-Session-Id"`
			ByPlatform     string `json:"By-Platform"`
			AcceptLanguage string `json:"Accept-Language"`
		}{
			XbySessionId:   x_fle_session_id,
			ByPlatform:     platform,
			AcceptLanguage: language,
		}
		c.response.Data = []string{}
		c.Data["json"] = c.response
		c.ServeJSON()
	}
	config.Lang, c.Lang = language, language
	data_list := utils.ApiToken{}
	if platform == PLATFORM_CLIENT { //windows 客户端 招聘系统请求
		//验证token
		cache := utils.GetRedis()
		session_arr := strings.Split(x_fle_session_id, "_")
		str := utils.DbConfig.DefaultString("auth_key", "auth")
		timer := session_arr[0]
		staff_id := session_arr[2]
		auth := utils.HmacSha1(utils.MD5(timer+str+staff_id), "")

		if auth == session_arr[1] {
			cache_key := utils.MD5(auth)
			if cache.Get(cache_key) != nil {
				value, ok := cache.Get(cache_key).(string)
				if ok == false {
					c.LogDebuger(value)
					c.Abort(PLATFORM_CLIENT + "redis error")
				}
				err := json.Unmarshal([]byte(value), &data_list)
				if err != nil {
					c.LogDebuger(err.Error())
					c.Abort(err.Error())
				}
			}
		}
		c.Platform = platform
		c.Lang = language
		c.XFleSessionId = x_fle_session_id
		//write log
	} else { //其他平台请求
		//x_fle_session_id = '1551864037_478caa769f81f14d915a8f058818221d3c71de5beb73a77043bd09e4c2021113_30975';
		param := []interface{}{
			x_fle_session_id,
		}
		c.Platform = platform
		c.Lang = language
		c.XFleSessionId = x_fle_session_id
		//write log
		//redis缓存
		user_info_id := strings.Split(x_fle_session_id, "_")
		if len(user_info_id) == 3 {
			x_fle_session_id_key := "user_" + user_info_id[2]
			cache := utils.GetRedis()
			value, _ := cache.Get(x_fle_session_id_key).([]byte)
			json.Unmarshal([]byte(value), &data_list)
			//登陆过期
			if data_list.Result.Id <= 0 {
				//第一次登陆
				var err1 error
				data_list, err1 = utils.GetApiDatApiToken("getStaffInfoByToken", param, utils.DbConfig.DefaultString("get_staff_info_by_token", "http://192.168.0.228:8090/fle-svc/com.flashexpress.fle.svc.api.StaffAuthSvc"))
				logs.Info(data_list)
				if err1 != nil {
					c.LogDebuger(err1.Error())
				}
				buffer, err := json.Marshal(data_list)
				if err != nil {
					c.LogDebuger(err.Error())
				}
				cache_err := cache.Put(x_fle_session_id_key, string(buffer), time.Second*60*60*24)
				if cache_err != nil {
					c.LogDebuger(err.Error())
					c.Abort(err.Error())
				}
			}
		} else {
			var err error
			data_list, err = utils.GetApiDatApiToken("getStaffInfoByToken", param, utils.DbConfig.DefaultString("get_staff_info_by_token", "http://192.168.0.228:8090/fle-svc/com.flashexpress.fle.svc.api.StaffAuthSvc"))
			if err != nil {
				c.LogDebuger(err.Error())
			}
		}
		//$this->wLog('记录返回用户信息', $data_list, 'BASE');
		logs.Info(data_list)
	}
	//模拟登陆成功 取反
	if data_list.Result.Id > 0 {
		//具体查看返回值
		c.UserInfo = config.UserInfo{
			StaffId:          data_list.Result.Id,
			Id:               data_list.Result.Id,
			Name:             data_list.Result.Name,
			Mobile:           data_list.Result.Mobile,
			AvatarUrl:        data_list.Result.AvatarUrl,
			OrganizationId:   data_list.Result.OrganizationId,
			OrganizationName: data_list.Result.OrganizationName,
			OrganizationType: data_list.Result.OrganizationType,
			StoreCategory:    data_list.Result.StoreCategory,
			FranchiseeId:     data_list.Result.FranchiseeId,
			FranchiseeName:   data_list.Result.FranchiseeName,
			DepartmentId:     data_list.Result.DepartmentId,
			JobTitle:         data_list.Result.JobTitle,
			Positions:        data_list.Result.Positions,
		}
		c.Ctx.Output.Header("bk-UID:", strconv.Itoa(int(c.UserInfo.Id)))
	} else {
		c.response.Code = -3
		c.response.Msg = "3001!"
		c.Data["json"] = c.response
		c.ServeJSON()
		return
	}
	//初始化_Init
	if app, ok := c.AppController.(_Init); ok {
		app._Init()
	}
}

func (c *BaseController) Finish() {
	dump := struct{
		Method string
		Url string
		Header interface{}
		RequestBody interface{}
		Response interface{}
	}{
		Method:c.Ctx.Request.Method,
		Url:c.Ctx.Request.RequestURI,
		Header:c.Ctx.Request.Header,
		RequestBody:string(c.Ctx.Input.RequestBody),
		Response: c.Data["json"],
	}
	logJson , _ :=json.Marshal(dump)
	logs.Debug("http json",string(logJson))

}

type HelloService struct {
	getStaffInfoByToken func(string) string
}

func (c *BaseController) reLogin() {
	http.Redirect(c.Ctx.ResponseWriter, c.Ctx.Request, utils.DbConfig.String("domain::web")+"/login", 302)
	return
}

/**
 * [getTranslation 获取语言对象]
 * @return [type] [返回一个对应语言map]
 */
func (c *BaseController) GetTranslation() map[string]string {
	//检测语言
	var lang string
	c.DoWithLang()
	if c.Lang == "" {
		lang = "th"
	} else {
		lang = c.Lang
	}
	//语言包字符处理 language:th-TH
	lang = lang[:2]
	if lang == "zh" || lang == "zh-CN" {
		lang = "zh-CN"
	}
	//返回语言包
	switch strings.ToLower(lang) {
	case "th":
		return messages.TH
	case "zh-cn":
		return messages.ZH
	case "en":
		return messages.EN
	}
	return map[string]string{}
}

//region 检测并设置语言属性：lang
//第一步：优先检测url中的参数l 其优先级最高
func (c *BaseController) DoWithLang() {
	//获取body参数l
	lang := c.GetString("l")
	is_exsit := false
	//获取配置的语言切片
	lang_slice := beego.AppConfig.Strings("server::languages")
	for _, value := range lang_slice {
		if lang == value {
			is_exsit = true
		}
	}
	if lang != "" && is_exsit {
		c.Lang = lang
		c.Ctx.SetCookie("lang", c.Lang, 2592000)
	} else {
		if c.Ctx.GetCookie("lang") != "" {
			c.Lang = c.Ctx.GetCookie("lang")
		}
	}
	//最后：经过上面两步检测仍未发现语言，则设置成默认语言：th
	if c.Lang == "" {
		c.Lang = "zh-CN"
		c.Ctx.SetCookie("lang", c.Lang, 2592000)
	}
}
