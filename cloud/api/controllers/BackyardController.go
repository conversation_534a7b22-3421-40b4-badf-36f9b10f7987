package controllers

import (
	"backyard/cloud/api/common/raw"
	"backyard/cloud/api/models"
	"backyard/cloud/api/utils"
	"encoding/json"
	"github.com/astaxie/beego/cache"
	"strconv"
	"time"
)

type BackyardController struct {
	BaseController
	StaffId  int64
	MissArgs string
	Redis    cache.Cache
}

/*
 * 初始化返回json
 */
func (c *BackyardController) _Init() {
	c.MissArgs = c.GetTranslation()["miss_args"]
	c.StaffId = c.UserInfo.StaffId
	c.Redis = utils.GetRedis()
	c._init()
}

/**
 * backyard消息 的列表
 * @return false|string|void
 */
func (c *BackyardController) MsgLst() {
	//捕获异常
	defer c.Catch()
	//获取页数，默认1
	page, _ := c.GetInt64("p", 1)

	if c.StaffId <= 0 {
		c.returnJson.Code = 0
		c.returnJson.Msg = c.MissArgs
		c.Data["json"] = c.returnJson
		c.ServeJSON()
	}
	//redis key
	var key string = "mgs_list_" + strconv.Itoa(int(c.StaffId)) + "_" + strconv.Itoa(int(page))
	var pageSize, count int64 = 10, 0
	list := []raw.MsgItem{}
	cacheStr, _ := c.Redis.Get(key).([]byte)
	if true {
		count, list = new(models.MessageCourierModel).ReadMessageList(raw.MessageCourier{
			StaffInfoId: c.StaffId,
			Offset:      (page - 1) * pageSize,
			Limit:       pageSize,
		})
		cacheList, err := json.Marshal(list)
		if err != nil {
			c.LogDebuger(err.Error())
			panic(err)
		}
		redisErr := c.Redis.Put(key, cacheList, time.Second*300)
		if redisErr != nil {
			c.LogDebuger(redisErr.Error())
			panic("error")
		}
	} else {
		jsonErr := json.Unmarshal(cacheStr, &list)
		count = int64(len(list))
		if jsonErr != nil {
			c.LogDebuger(jsonErr.Error())
		}
	}

	var lastPage int64 = 0
	if count < pageSize {
		lastPage = 1
	}
	//返回
	c.returnJson.Data = raw.MsgListReturnJson{
		Data:     list,
		LastPage: lastPage,
	}
	c.Data["json"] = c.returnJson
	c.ServeJSON(true)
}

/**
 * CEO信箱 \审批 未读合并
 * @return false|string|void
 */
func (c *BackyardController) UnReadAndAudit() {
	//捕获异常
	defer c.Catch()
	if c.StaffId <= 0 {
		c.returnJson.Code = 0
		c.returnJson.Msg = c.MissArgs
		c.Data["json"] = c.returnJson
		c.ServeJSON()
	}
	resultQuery := map[string]int64{
		"un_read_num":         0,
		"un_audit_num":        0,
		"backyard_unread_num": 0,
	}
	//获取未读
	resultQuery["un_read_num"] = new(models.MailToCeoModel).UnReadCount(raw.MailToCeo{
		StaffInfoId: c.StaffId,
	})
	//获取待审核数量
	resultQuery["un_audit_num"] = new(models.StaffAuditUnionModel).WaitAuditNum(raw.StaffAuditUnion{
		StaffIdUnion: c.StaffId,
	})
	//backyard 消息未读
	resultQuery["backyard_unread_num"] = new(models.MessageCourierModel).BackyardUnreadNum(raw.MessageCourier{
		StaffInfoId: c.StaffId,
	})

	c.returnJson.Code = 1
	c.returnJson.Msg = "success"
	c.returnJson.Data = resultQuery
	c.Data["json"] = c.returnJson
	c.ServeJSON(true)
}
