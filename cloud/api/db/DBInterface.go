package db

type DBInterface interface {

	/**
	 * Connects to a database server
	 * @return error
	 */
	Connection(MyInterface) error

	/**
	 * Select a database
	 * @return nil
	 */

	SelectDB(string)

	/**
	 * Select a table collection
	 * @return nil
	 */
	SelectCollection(string)

	/**
	 * Session copy
	 * @return nil
	 */
	SessionCopy()

	/**
	 * Insert an struct into the collection
	 * @param struct Options for the insert.
	 * @return bool
	 */
	Insert(interface{}) int64
	Save(string, ...interface{})
	Update(interface{}, interface{}) int64
	BatchInsert(string, ...interface{})

	/**
	 * @param interface The query
	 * @param int skip limit
	 * @param interface container
	 * @return struct count error
	 */
	FindAll(interface{}, int, int, interface{}) (int, error)
	/**
	 * @param interface The query
	 * @return error
	 */
	FindOne(interface{}, interface{}) error

	FindByInnerJoin(FindBySqlOptions, ...interface{}) (interface{}, int)

	/**
	 * @param map by join
	 * @param interface The query
	 * @return interface
	 */
	FindByJoin([]map[string]string, ...interface{}) (interface{}, int)

	Query(string, ...interface{}) MyInterface

	FetchAll(interface{}) (int64, error)

	FetchOne(interface{}) error

	Remove(interface{}) int64
	/**
	 * Closes this database connection
	 * @param  nil
	 * @return nil
	 */
	Close()
}

type FindBySqlOptions struct {
	Fields    []string
	Table     string
	InnerJoin string
	LeftJoin  string
	On        string
	Where     string
	OrderBy   string
	GroupBy   string
	Limit     int
	Offset    int
}
