package db

import (
	"fmt"
	"github.com/astaxie/beego/logs"
	"github.com/astaxie/beego/orm"
	"reflect"
	"strconv"
	"strings"
)

type MyInterface interface {
	DBInterface
}

var (
	OrmCondition orm.Ormer
)

type MysqlCommandBuilder struct {
	TableName, DBName string
	OrmConnect        orm.Ormer
	OrmQuerySeter     orm.QuerySeter
	OrmConditoin      *orm.Condition
	OrmRawSeter       orm.RawSeter
	DBconnnection     MyInterface
}

func MysqlConnection(connectStr, dbName, tableName string) MyInterface {
	if dbName == "db" {
		dbName = "default"
	}
	var ormSchema orm.Ormer
	if OrmCondition != nil{
		ormSchema = OrmCondition
	}else{
		ormSchema = orm.NewOrm()
	}
	dbConnection, ok := interface{}(&MysqlCommandBuilder{
		OrmConnect: ormSchema,
		//OrmQuerySeter: ormSchema.QueryTable(tableName),
		OrmConditoin: orm.NewCondition(),
		DBName:       dbName,
		TableName:    tableName,
	}).(MyInterface)
	if ok == false {
		panic("mysql interface error")
	}
	if dbName != "" {
		dbConnection.SelectDB(dbName)
	}
	dbConnection.Connection(dbConnection)
	return dbConnection
}

func (c *MysqlCommandBuilder) Connection(con MyInterface) error {
	c.DBconnnection = con
	return nil
}

func (c *MysqlCommandBuilder) SelectDB(dbName string) {
	if err := c.OrmConnect.Using(c.DBName); err != nil {
		panic(err.Error())
	}
}

func (c *MysqlCommandBuilder) SelectCollection(tableName string) {
	c.OrmQuerySeter = c.OrmConnect.QueryTable(tableName)
}

func (c *MysqlCommandBuilder) SessionCopy() {}

/**
* @param pointer interface
* example:
* pointer = &User{
*		Age: 15,
*       Name: "姓名",
* }
* @return id
 */
func (c *MysqlCommandBuilder) Insert(pointer interface{}) int64 {
	id, err := c.OrmConnect.Insert(pointer)
	if err != nil {
		panic(err.Error())
	}
	return id
}

//TODO
func (c *MysqlCommandBuilder) Save(option string, args ...interface{}) {}

/**
* @param options interface
* example:
* options = map[string]interface{}{
*		"name": "姓名",
*       "age__lt":  12,
* }
* params = orm.Params{
*   "name": "张三",
*   "age": 14,
* }
* @return count
 */
func (c *MysqlCommandBuilder) Update(options interface{}, params interface{}) int64 {
	where, ok1 := options.(map[string]interface{})
	if ok1 == false {
		panic("update: options type error")
	}
	if len(where) <= 0 {
		panic("error: count map empty")
	}
	ormParams, ok2 := params.(orm.Params)
	if ok2 == false {
		panic("update: params type error")
	}
	query := c.OrmQuerySeter
	for key, value := range where {
		query = query.Filter(key, value)
	}
	count, err := query.Update(ormParams)
	if err != nil {
		panic(err)
	}
	return count
}

func (c *MysqlCommandBuilder) Query(sql string, args ...interface{}) MyInterface {
	c.OrmRawSeter = c.OrmConnect.Raw(sql, args...)
	return c.DBconnnection
}

func (c *MysqlCommandBuilder) FetchAll(container interface{}) (int64, error) {
	if reflect.TypeOf(container).Kind() != reflect.Ptr {
		panic("container must be a pointer")
	}
	return c.OrmRawSeter.QueryRows(container)
}

func (c *MysqlCommandBuilder) FetchOne(container interface{}) error {
	if reflect.TypeOf(container).Kind() != reflect.Ptr {
		panic("container must be a pointer")
	}
	return c.OrmRawSeter.QueryRow(container)
}

/**
* @param options interface
* example:
* options = map[string][]interface{}{
*		"and": {"profile", "B89A9A800011","time__gte",15932},
*       "andnot":  {"name", "姓名"},
*       "or": {"profile__contains", "B89A9A800012"},
*		"orderby": {"-id","age"},
* }
* WHERE profile = B89A9A800011 OR rofile like "%B89A9A800012%" ORDER BY id DESC
* @param skip,limit,container
* @return count error
 */
func (c *MysqlCommandBuilder) FindAll(options interface{}, skip, limit int, container interface{}) (int, error) {
	where, ok := options.(map[string][]interface{})
	if ok == false {
		panic("options error")
	}
	if reflect.TypeOf(container).Kind() != reflect.Ptr {
		panic("container must be a pointer")
	}
	defer c.SelectCollection(c.TableName)
	condSql := c.OrmConditoin
	for key, value := range where {
		switch strings.ToUpper(key) {
		case "AND":
			num := len(value) / 2
			remainder := len(value) % 2
			if remainder != 0 {
				panic("params errors")
			}
			if num >= 1 {
				for i := 0; i < num; i++ {
					keyNum := i * 2
					condSql = condSql.And(value[keyNum].(string), value[keyNum+1])
				}
			}
		case "OR":
			condSql = condSql.Or(value[0].(string), value[1])
		case "ANDNOT":
			condSql = condSql.AndNot(value[0].(string), value[1])
		case "ORDERBY":
			orderList := []string{}
			for _, v := range value {
				orderList = append(orderList, v.(string))
			}
			c.OrmQuerySeter = c.OrmQuerySeter.OrderBy(orderList...)
		case "GROUP":
			orderList := []string{}
			for _, v := range value {
				orderList = append(orderList, v.(string))
			}
			c.OrmQuerySeter = c.OrmQuerySeter.GroupBy(orderList...)
		default:
			panic("select error")
		}
	}
	c.OrmQuerySeter = c.OrmQuerySeter.SetCond(condSql)
	count, CountErr := c.OrmQuerySeter.Count()
	if CountErr != nil {
		panic(CountErr)
	}
	_, valueErr := c.OrmQuerySeter.Limit(limit, skip).All(container)
	return int(count), valueErr
}

/**
* @param options interface
* example:
* options = map[string][]interface{}{
*		"and": {"profile", "B89A9A800011","time__gte",15932},
*       "andnot":  {"name", "姓名"},
*       "or": {"profile__contains", "B89A9A800012"},
*		"orderby": {"-id","age"},
* }
* WHERE profile = B89A9A800011 OR rofile like "%B89A9A800012%" ORDER BY id DESC
* @return interface{},error
 */
func (c *MysqlCommandBuilder) FindOne(options interface{}, container interface{}) error {
	where, ok := options.(map[string][]interface{})
	if ok == false {
		panic("options error")
	}
	if reflect.TypeOf(container).Kind() != reflect.Ptr {
		panic("container must be a pointer")
	}
	defer c.SelectCollection(c.TableName)

	condSql := c.OrmConditoin
	for key, value := range where {
		switch strings.ToUpper(key) {
		case "AND":
			num := len(value) / 2
			remainder := len(value) % 2
			if remainder != 0 {
				panic("params errors")
			}
			if num >= 1 {
				for i := 0; i < num; i++ {
					keyNum := i * 2
					condSql = condSql.And(value[keyNum].(string), value[keyNum+1])
				}
			}
		case "OR":
			condSql = condSql.Or(value[0].(string), value[1])
		case "ANDNOT":
			condSql = condSql.AndNot(value[0].(string), value[1])
		case "ORDERBY":
			orderList := []string{}
			for _, v := range value {
				orderList = append(orderList, v.(string))
			}
			c.OrmQuerySeter = c.OrmQuerySeter.OrderBy(orderList...)
		case "GROUP":
			orderList := []string{}
			for _, v := range value {
				orderList = append(orderList, v.(string))
			}
			c.OrmQuerySeter = c.OrmQuerySeter.GroupBy(orderList...)
		}
	}

	c.OrmQuerySeter = c.OrmQuerySeter.SetCond(condSql).Limit(1)
	valueErr := c.OrmQuerySeter.One(container)
	if valueErr != nil {
		logs.Error(valueErr.Error())
		return valueErr
	}
	return nil
}

/**
* @param options FindBySqlOptions
* example:
* options = FindBySqlOptions{
*		Fields: {"user.name","profile.age"},
* 		Table: "user",
* 		InnerJoin : "profile",
* 		On: "user.id_user = profile.fk_user",
* 		Where: "id > ? and id < ? and name = ? ",
* 		OrderBy: "-name",
* 		Limit: 10,
* 	    Offset: 0,
* }
* value = 4,5,"姓名"
* @return ,[]interface{},count
 */
func (c *MysqlCommandBuilder) FindByInnerJoin(options FindBySqlOptions, values ...interface{}) (interface{}, int) {
	qb, buildErr := orm.NewQueryBuilder("mysql")
	qbCount, _ := orm.NewQueryBuilder("mysql")
	if buildErr != nil {
		panic(buildErr.Error())
	}
	if len(options.Fields) < 0 || options.Table == "" {
		panic("fields error")
	}

	if len(options.Fields) <= 0 {
		qb = qb.Select("*")
	} else {
		qb = qb.Select(options.Fields...)
	}
	qbCount = qbCount.Select("count(*)")

	qb = qb.From(options.Table)
	qbCount = qbCount.From(options.Table)

	if options.InnerJoin != "" {
		qb = qb.InnerJoin(options.InnerJoin)
		qbCount = qbCount.InnerJoin(options.InnerJoin)
	}
	if options.LeftJoin != "" {
		qb = qb.LeftJoin(options.LeftJoin)
		qbCount = qbCount.LeftJoin(options.LeftJoin)
	}
	if options.On != "" {
		qb = qb.On(options.On)
		qbCount = qbCount.On(options.On)
	}
	if options.Where != "" {
		qb = qb.Where(options.Where)
		qbCount = qbCount.Where(options.Where)
	}

	if options.GroupBy != "" {
		qb = qb.GroupBy(options.GroupBy)
		qbCount = qbCount.GroupBy(options.GroupBy)
	} else {
		qbCount = qbCount.GroupBy(options.Table + ".id")
	}

	if options.OrderBy != "" {
		orderByList := strings.Split(options.OrderBy, ",")
		var orderStr string
		for _, value := range orderByList {
			if value[0] == '-' {
				orderStr += strings.TrimLeft(value, "-") + " DESC,"
			} else {
				orderStr += strings.TrimLeft(value, "-") + " ASC,"
			}
		}
		orderStr = strings.Trim(orderStr, ",")
		if orderStr != "" {
			qb = qb.OrderBy(orderStr)
		}
	}

	lists := []orm.Params{}
	o := orm.NewOrm()
	count, countErr := o.Raw(qbCount.String(), values...).ValuesList(&[]orm.ParamsList{})
	if countErr != nil {
		panic(countErr.Error())
	}

	if count <= 0 {
		return lists, int(count)
	}

	if options.Limit > 0 {
		qb = qb.Limit(options.Limit)
	}
	if options.Offset > 0 {
		qb = qb.Offset(options.Offset)
	}
	_, valueErr := o.Raw(qb.String(), values...).Values(&lists)
	if valueErr != nil {
		logs.Error(valueErr.Error())
	}
	return lists, int(count)
}

/**
* @param options interface
* example:
* options = map[string]interface{}{
*		"name": "姓名",
*       "age__lt":  12,
* }
* @return count
 */
func (c *MysqlCommandBuilder) Remove(options interface{}) int64 {

	where, ok1 := options.(map[string]interface{})
	if ok1 == false {
		panic("update: options type error")
	}
	if len(where) <= 0 {
		panic("error: count map empty")
	}

	query := c.OrmQuerySeter
	for key, value := range where {
		query = query.Filter(key, value)
	}
	count, err := query.Delete()
	if err != nil {
		panic(err)
	}
	return count
}

func (c *MysqlCommandBuilder) Close() {
	c.OrmConnect = orm.NewOrm()
	c.OrmQuerySeter = c.OrmConnect.QueryTable(c.TableName)
	c.OrmConditoin = orm.NewCondition()
}

/**
* @param options []map[string]string
* example:
*	options = []map[string]string{
*		{"select": "app.id"},
*		{"from": "wrap_app"},
*		{"InnerJoin": "app"},
*		{"On": "wrap_app.app_id = app.id"},
*		{"LeftJoin": "(select wrap_label.wrap_id, group_concat(label.name) label_name from wrap_label left join label on label.id=wrap_label.label_id group by wrap_label.wrap_id) as label"},
*		{"on": "label.wrap_id = wrap_app.wrap_id"},
*		{"LeftJoin": "app_version"},
*		{"on": "app.id = app_version.app_id"},
*		{"LeftJoin": "(select app_label.app_id, group_concat(label.name) tag_name from app_label left join label on label.id=app_label.label_id group by app_label.app_id) as app_tags"},
*		{"on": "app_tags.app_id = wrap_app.app_id"},
*		{"LeftJoin": "wrap"},
*		{"on": "wrap.id = wrap_app.wrap_id"},
*		{"where": "wrap_app.wrap_id = ? and app.status= ? and app_version.status=?"},
*		{"orderby": "-wrap_app.sort_id"},
*		{"limit": "10"},
*		{"offset": "0"},
*	}
* value = 1,1,1
* @return []interface{},count
 */
func (c *MysqlCommandBuilder) FindByJoin(options []map[string]string, values ...interface{}) (interface{}, int) {
	defer c.Close()
	if len(options) <= 0 {
		panic("error: select map empty")
	}
	qb, buildErr := orm.NewQueryBuilder("mysql")
	if buildErr != nil {
		panic(buildErr)
	}
	qbCount, _ := orm.NewQueryBuilder("mysql")
	limit := 1000
	for _, value := range options {
		keys := reflect.ValueOf(value).MapKeys()
		if len(keys) <= 0 {
			panic("error: select map error")
		}
		key := fmt.Sprintf("%s", keys[0])
		switch strings.ToUpper(key) {
		case "SELECT":
			qb = qb.Select(value[key])
			qbCount = qbCount.Select("count(1) as num")
		case "FROM":
			qb = qb.From(value[key])
			qbCount = qbCount.From(value[key])
		case "INNERJOIN":
			qb = qb.InnerJoin(value[key])
			qbCount = qbCount.InnerJoin(value[key])
		case "LEFTJOIN":
			qb = qb.LeftJoin(value[key])
			qbCount = qbCount.LeftJoin(value[key])
		case "RIGHTJOIN":
			qb = qb.RightJoin(value[key])
			qbCount = qbCount.RightJoin(value[key])
		case "ON":
			qb = qb.On(value[key])
			qbCount = qbCount.On(value[key])
		case "WHERE":
			qb = qb.Where(value[key])
			qbCount = qbCount.Where(value[key])
		case "GROUPBY":
			qb = qb.GroupBy(value[key])
			qbCount = qbCount.GroupBy(value[key])
		case "HAVING":
			qb = qb.Having(value[key])
			qbCount = qbCount.Having(value[key])
		case "ORDERBY":
			if value[key][0] == '-' {
				qb = qb.OrderBy(strings.Trim(value[key], "-")).Desc()
			}
			if value[key][0] == '+' {
				qb = qb.OrderBy(strings.Trim(value[key], "+")).Asc()
			}
		case "LIMIT":
			limit, _ = strconv.Atoi(value[key])
			qb = qb.Limit(limit)
		case "OFFSET":
			offset, _ := strconv.Atoi(value[key])
			qb = qb.Offset(offset)
		default:
			panic("error: select map key error")
		}
	}
	sql := qb.String()
	countSQL := qbCount.String()
	maps := []orm.Params{}
	_, err := c.OrmConnect.Raw(sql, values...).Values(&maps)
	if err != nil {
		logs.Error(err)
		return maps, 0
	}
	count := struct {
		Count int `orm:"column(num)"`
	}{}
	c.OrmConnect.Raw(countSQL, values...).QueryRow(&count)
	return maps, count.Count
}

//TODO
func (c *MysqlCommandBuilder) BatchInsert(option string, args ...interface{}) {}
