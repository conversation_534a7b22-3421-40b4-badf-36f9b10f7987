package library

import (
	"backyard/cloud/api/common/log"
	"backyard/cloud/api/utils"
	"encoding/base64"
	"encoding/json"
	"github.com/aliyun/aliyun-mns-go-sdk"
	"github.com/astaxie/beego/orm"
)

type MqServer struct {
	response utils.Response
	inputUrl,accessKeyId,accessKeySecret,queueKey string
}

/*
 * 注册服务
 */
func (c *MqServer) Init() {
	c.inputUrl = utils.DbConfig.DefaultString("input_url","")
	c.accessKeyId = utils.DbConfig.DefaultString("access_key","")
	c.accessKeySecret = utils.DbConfig.DefaultString("secret_key","")
	c.queueKey = utils.DbConfig.DefaultString("queue","")
	go c.MNSQueueResponse()
}


type MyJsonName struct {
	Data   string `json:"data"`
	Locale string `json:"locale"`
	Method string `json:"method"`
}

type Item struct {
	LogisticName string 	`json:"logisticName"`
	BoxList []struct {
		ExpressSn string 	`json:"expressSn"`
		Goods struct {
			FEX00010 struct {
				Asset []interface{} `json:"asset"`
				Num   string        `json:"num"`
			} `json:"FEX00010"`
		} `json:"goods"`
	} `json:"boxList"`
	OrderSn          string `json:"orderSn"`
	OutWarehouseTime string `json:"outWarehouseTime"`
}

//阿里云MQ消费
func (c *MqServer) MNSQueueResponse() {
	queue := ali_mns.NewMNSQueue(c.queueKey, ali_mns.NewAliMNSClient(c.inputUrl, c.accessKeyId, c.accessKeySecret))
	endChan := make(chan int)
	respChan := make(chan ali_mns.MessageReceiveResponse)
	errChan := make(chan error)
	for{
		go func() {
			select {
			case resp := <-respChan:
				myJson := MyJsonName{}
				item := Item{}
				decodeBytes, _ := base64.StdEncoding.DecodeString(resp.MessageBody)
				jsonStruct , _ := base64.StdEncoding.DecodeString(string(decodeBytes))
				log.Logger.Info("MessageBody:", resp.MessageBody,string(jsonStruct))
				json.Unmarshal(jsonStruct,&myJson)
				if myJson.Data != "" {
					json.Unmarshal([]byte(myJson.Data),&item)
					expressSn := ""
					for _ ,v :=range item.BoxList {
						expressSn = v.ExpressSn
					}
					if item.OrderSn != ""{
						o:= orm.NewOrm()
						o.Using("default")
						sql := `update assets_info a,assets_order b 
								set a.state=1,a.out_time='`+item.OutWarehouseTime+`',a.express='`+item.LogisticName+`',a.express_sn='`+expressSn+`'
								where a.order_id = b.id and b.order_union_id = '`+item.OrderSn+`'`
						res, error := o.Raw(sql).Exec()
						if error != nil {
							log.Logger.Error(error.Error())
						}
						count , _ := res.RowsAffected()
						if count > 0 {
							log.Logger.Info("mq update success",string(jsonStruct))
						}
					}
				}

				if ret, e := queue.ChangeMessageVisibility(resp.ReceiptHandle, 5); e != nil {
					log.Logger.Info(e)
				} else {
					if e := queue.DeleteMessage(ret.ReceiptHandle); e != nil {
						log.Logger.Info(e)
					}
					endChan <- 1
				}
			case err := <-errChan:
				{
					log.Logger.Info("MQ end", err)
					endChan <- 1
				}
			}
		}()
		queue.ReceiveMessage(respChan, errChan, 30)
		<-endChan

	}
}

//返回
func (c *MqServer) SyntaxJSON(code int, msg string, data interface{}) string {
	c.response.Code = code
	c.response.Msg = msg
	c.response.Data = data
	jsonByte, _ := json.Marshal(c.response)
	log.Logger.Info("return json", string(jsonByte))
	return string(jsonByte)
}
