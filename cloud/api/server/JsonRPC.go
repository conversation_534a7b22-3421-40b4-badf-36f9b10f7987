package library

import (
	"backyard/cloud/api/common/log"
	"backyard/cloud/api/db"
	"backyard/cloud/api/utils"
	"encoding/json"
	"github.com/astaxie/beego"
	"github.com/astaxie/beego/orm"
	"github.com/bwmarrin/snowflake"
	"github.com/hprose/hprose-golang/rpc"
)

type JsonRPC struct {
	response utils.Response
}

/*
 * 注册RPC服务
 * 跨语言通讯服务
 */
func (c *JsonRPC) Init() {
	service := rpc.NewHTTPService()
	service.AddAllMethods(&JsonRPC{})
	service.AddAllMethods(&PTServer{}) 
	
	beego.Handler("/svc/call", service)
	
}

/*
 * PHP中调用golang的rpc服务
 * $client = new \Hprose\Http\Client('http://127.0.0.1:8051/svc/call', false);
 * $return_data = $client->Example();
 */
func (c *JsonRPC) Example() string {
	return "example test"
}

//验证用户是否注册手机
func (c *JsonRPC) GetStaffMobileInfo(staffId int64) string {
	o := db.OrmCondition
	error := o.Using("default")
	if error != nil{
		log.Logger.Error(error.Error())
	}

	sql := `
		SELECT
			*
		FROM
			staff_mobile
		WHERE
			create_id = ? and deleted = 0`
	var maps []orm.Params
	count, error := o.Raw(sql, staffId).Values(&maps)
	if error != nil {
		log.Logger.Error(error.Error())
		return c.SyntaxJSON(-3, error.Error(), "")
	}
	if count <= 0 {
		return c.SyntaxJSON(-1, "mobile empty", "")
	}
	return c.SyntaxJSON(1, "ok", maps[0])
}

func (c *JsonRPC) ModifyReadState(messageId string, state int64) string {
	o := db.OrmCondition
	o.Using("db")
	sql := `update message_courier set read_state=? ,updated_at=updated_at  where id= ?`
	res, error := o.Raw(sql, state, messageId).Exec()
	if error != nil {
		log.Logger.Error(error.Error())
		return c.SyntaxJSON(-1, error.Error(), "")
	}
	num, _ := res.RowsAffected()
	return c.SyntaxJSON(1, "ok", map[string]int64{
		"count": num,
	})
}

//获取唯一ID snowFlake 算法
func (c *JsonRPC) GetUniqueID(nodeNum int64) string {
	node, err := snowflake.NewNode(nodeNum)
	if err != nil {
		log.Logger.Error(err.Error())
		return c.SyntaxJSON(-2, err.Error(), "")
	}

	// Generate a snowflake ID.
	id := node.Generate().String()

	return c.SyntaxJSON(1, "ok", id)
}

//返回
func (c *JsonRPC) SyntaxJSON(code int, msg string, data interface{}) string {
	c.response.Code = code
	c.response.Msg = msg
	c.response.Data = data
	jsonByte, _ := json.Marshal(c.response)
	log.Logger.Info("return json",string(jsonByte))
	return string(jsonByte)
}
