package library
 
import (
    "encoding/json"
    "net/http"
    "strings"
	"io/ioutil"
    "crypto/tls"
    "encoding/xml"
    "log"
    "github.com/astaxie/beego"
    "github.com/astaxie/beego/httplib"
)

type PTServer struct {}

type EnvelopeCard_Statement_API struct {
    XMLName xml.Name `xml:"Envelope"`
    Body    struct {
        Card_Statement_API struct {
            StrJson string `xml:"strJson"`
        } `xml:"Card_Statement_API"`
    } `xml:"Body"`
}

type EnvelopeCheck_Balance_API struct {
    XMLName xml.Name `xml:"Envelope"`
    Body    struct {
        Response struct {
            Result string `xml:"Check_Balance_APIResult"`
        } `xml:"Check_Balance_APIResponse"`
    } `xml:"Body"`
}

type EnvelopeAES256GCM_Decrypt_API struct {
    XMLName xml.Name `xml:"Envelope"`
    Body    struct {
        Response struct {
            Result string `xml:"AES256GCM_Decrypt_APIResult"`
        } `xml:"AES256GCM_Decrypt_APIResponse"`
    } `xml:"Body"`
}
type EnvelopeGenerate_JWT_Token struct {
    XMLName xml.Name `xml:"Envelope"`
    Body    struct {
        Generate_JWT_TokenResponse struct {
            Generate_JWT_TokenResult string `xml:"Generate_JWT_TokenResult"`
        } `xml:"Generate_JWT_TokenResponse"`
    } `xml:"Body"`
}


func (c *PTServer) beegoXml(WSDL string){
    req := httplib.Post(WSDL)
    body := `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
        <soapenv:Header/>
        <soapenv:Body>
        <tem:Generate_JWT_Token>
            <!--Optional:-->
            <tem:user_id>dd22e1f1-3993-436f-8bab-da04f5459551</tem:user_id>
            <!--Optional:-->
            <tem:role>Admin</tem:role>
            <!--Optional:-->
            <tem:api_name>Check_Balance_API</tem:api_name>
        </tem:Generate_JWT_Token>
        </soapenv:Body>
    </soapenv:Envelope>`
    req.SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
    req.Header("Content-Type","text/xml; charset=UTF-8")
    req.Body(strings.NewReader(body))
    str, err := req.String()
    if err != nil {
        log.Println(err)
    }
    log.Println(str)
}
 
func (c *PTServer) Generate_JWT_Token() (string){
    userID := beego.AppConfig.String("PT.USER_ID")
    role := beego.AppConfig.String("PT.ROLE")
    body := `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
        <soapenv:Header/>
        <soapenv:Body>
        <tem:Generate_JWT_Token>
            <!--Optional:-->
            <tem:user_id>`+userID+`</tem:user_id>
            <!--Optional:-->
            <tem:role>`+role+`</tem:role>
            <!--Optional:-->
            <tem:api_name>Check_Balance_API</tem:api_name>
        </tem:Generate_JWT_Token>
        </soapenv:Body>
    </soapenv:Envelope>`
    JWT_Token := WSDL_POST("",body)

    log.Println(JWT_Token)
    _jsonArray := make(map[string]string)
    err := json.Unmarshal([]byte(JWT_Token), &_jsonArray)
    
    v := EnvelopeGenerate_JWT_Token{}
    err = xml.Unmarshal([]byte(_jsonArray["data"]), &v)
    if err != nil {
        log.Println(err.Error())
    }
    log.Println(v.Body)
    if v.Body.Generate_JWT_TokenResponse.Generate_JWT_TokenResult != "" {
        JWT_Token = v.Body.Generate_JWT_TokenResponse.Generate_JWT_TokenResult
    }
    return JWT_Token
}

func (c *PTServer) AES256GCM_Decrypt_API(decryptText string) (string){
    hexKey := beego.AppConfig.String("PT.ENC_HEX_KEY")
    hexIV := beego.AppConfig.String("PT.ENC_HEX_IV")
    body := `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
        <soapenv:Header/>
        <soapenv:Body>
        <tem:AES256GCM_Decrypt_API>
            <!--Optional:-->
            <tem:decryptText>`+decryptText+`</tem:decryptText>
            <!--Optional:-->
            <tem:hexKey>`+hexKey+`</tem:hexKey>
            <!--Optional:-->
            <tem:hexIV>`+hexIV+`</tem:hexIV>
        </tem:AES256GCM_Decrypt_API>
        </soapenv:Body>
    </soapenv:Envelope>`
	return WSDL_POST("",body)
}
func (c *PTServer) AES256GCM_Encrypt_API(plainTextJson string) (string){
    hexKey := beego.AppConfig.String("PT.ENC_HEX_KEY")
    hexIV := beego.AppConfig.String("PT.ENC_HEX_IV")
    body := `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
        <soapenv:Header/>
        <soapenv:Body>
        <tem:AES256GCM_Encrypt_API>
            <!--Optional:-->
            <tem:plainTextJson>`+plainTextJson+`</tem:plainTextJson>
            <!--Optional:-->
            <tem:hexKey>`+hexKey+`</tem:hexKey>
            <!--Optional:-->
            <tem:hexIV>`+hexIV+`</tem:hexIV>
        </tem:AES256GCM_Encrypt_API>
        </soapenv:Body>
    </soapenv:Envelope>`
	return WSDL_POST("",body)
}

func WSDL_POST(head,body string)(string){
    WSDL := beego.AppConfig.String("PT.WSDL")
    tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}
    resp, err := http.NewRequest("POST",WSDL,strings.NewReader(body))
    resp.Header.Set("Content-Type", "text/xml; charset=UTF-8")
    if head != "" {
        resp.Header.Set("Authorization", "Bearer "+head)
    }
   
    res, err := client.Do(resp)

    result := make(map[string]interface{})
    if nil != err {
        log.Println("http post err:", err)
        result["code"] = -1
        result["data"] = ""
        result["msg"] = err 
    }
    defer res.Body.Close()
    
	log.Println(res.StatusCode)
    // return status
    if 200 != res.StatusCode {
        log.Println("WebService soap1.1 request fail, status: %s\n", res.StatusCode)
        result["code"] = -1
        result["data"] = ""
        result["msg"] = err 
    }
 
    rawbody, err := ioutil.ReadAll(res.Body)
    log.Println("webService soap1.1 response: ", string(rawbody))
    
    result["code"] = 0
    result["data"] = string(rawbody)
    result["msg"] = "ok"

    if nil != err {
        log.Println("ioutil ReadAll err:", err)
        result["code"] = -1
        result["data"] = ""
        result["msg"] = err 
    }

	resA, _ := json.Marshal(result)
	return string(resA)
}

func (c *PTServer) Check_Balance_API(head,strJson string) (string){
    body := `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
        <soapenv:Header/>
        <soapenv:Body>
        <tem:Check_Balance_API>
            <!--Optional:-->
            <tem:strJson>`+strJson+`</tem:strJson>
        </tem:Check_Balance_API>
        </soapenv:Body>
    </soapenv:Envelope>`
    if head == "" {
        //1 直接获取TOKEN
        head = c.Generate_JWT_Token()
    }
    JWT_Data := WSDL_POST(head,body)
    log.Println(JWT_Data)
    _jsonArray := make(map[string]string)
    err := json.Unmarshal([]byte(JWT_Data), &_jsonArray)
    //2 XML 解析
    v := EnvelopeCheck_Balance_API{}
    err = xml.Unmarshal([]byte(_jsonArray["data"]), &v)
    if err != nil {
        log.Println(err.Error())
    }
    log.Println(v.Body)
    if v.Body.Response.Result != "" {
        //3 解密
        JWT_Data = c.AES256GCM_Decrypt_API(v.Body.Response.Result)
        err = json.Unmarshal([]byte(JWT_Data), &_jsonArray)
        //4 XML在解析
        vAES256GCM_Decrypt := EnvelopeAES256GCM_Decrypt_API{}
        err = xml.Unmarshal([]byte(_jsonArray["data"]), &vAES256GCM_Decrypt)
        if err != nil {
            log.Println(err.Error())
        }
        log.Println(vAES256GCM_Decrypt.Body.Response.Result)
        if vAES256GCM_Decrypt.Body.Response.Result != "" {
            JWT_Data = vAES256GCM_Decrypt.Body.Response.Result
        }

    }
    return JWT_Data
}

func (c *PTServer) Card_Statement_API(head,strJson string) (string){
    body := `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
        <soapenv:Header/>
        <soapenv:Body>
        <tem:Card_Statement_API>
            <!--Optional:-->
            <tem:strJson>`+strJson+`</tem:strJson>
        </tem:Card_Statement_API>
        </soapenv:Body>
    </soapenv:Envelope>`
    if head == "" {
        head = c.Generate_JWT_Token()
    }
	return WSDL_POST(head,body)
}
