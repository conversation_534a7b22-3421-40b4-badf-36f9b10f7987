package messages

var EN = map[string]string{
	// 公共
	"lang":             "English",
	"account":          "Account ID",
	"password":         "Password",
	"login":            "Login",
	"login_user":       "Please input account",
	"login_pwd":        "Please input password",
	"login_all":        "Incorrect account ID or password",
	"no_server":        "The server is abnormal. Please try again later…",
	"dot":              "Branch",
	"details":          "Details",
	"this_week":        "This Week ",
	"last_week":        "Last Week ",
	"this_month":       "This Month ",
	"last_month":       "Last Month ",
	"date":             "Date",
	"time":             "Time",
	"houre":            "Houre",
	"operating_report": "Operation report",
	"sales_rankings":   "Sales report",
	"already_opened":   "Opened",
	"not_open":         "In process",
	"unknown":          "Null",
	"personal":         "Personal",
	"enterprise":       "Business",
	"piece":            "Piece",
	"info":             " Details",
	"example":          "example: 30 hours 56 mins will show as 30:56 in this report",
	"500":              "รหัสยืนยัน {|code|} คุณกำลังแก้ไขข้อมูลส่วนตัว หากคุณไม่ได้เป็นผู้ดำเนินการ โปรดระวังความปลอดภัยของบัญชีคุณ",

	//审批管理 1000
	"1001":           "This employee does not exist!",
	"1002":           "There is no missing attendance need to make up for the selected time!",
	"1003":           "There is no missing attendance need to make up for the selected time!",
	"1004":           "There is a make-up request for the selected time already，please do not submit repeatedly!!",
	"1005":           "There is a make-up request for the selected time already，please do not submit repeatedly!!",
	"1006":           "Make-up failure!",
	"1007":           "You can only select dates within 3 days",
	"1008":           "You cannot submit the make-up request on the selected time!",
	"1009":           "Failed to submit leave!",
	"1010":           "The start time must be earlier than or equal to the end time!",
	"1011":           "There is a make-up request for A1:E87 selected time already，please do not submit repeatedly!",
	"1012":           "The selected time has a leave record, please do not repeat the submission!!",
	"1014":           "Approval failure!",
	"1015":           "There is no such approval!",
	"1016":           "This order has been approved and cannot be approved again!",
	"1017":           "Sorry, you have no permission to audit!",
	"1018":           "Leave date must be at least the current date!",
	"1019":           "Enter a maximum of 500 words!",
	"1020":           "Enter a maximum of 500 words!",
	"1021":           "You cannot submit the make-up request on the selected time!",
	"1022":           "You cannot submit the make-up request on the selected time!",
	"1023":           "Please select start time",
	"1024":           "Please select end time",
	"1025":           "You can only apply to make up three times a month and this month the limit has been exceeded!",
	"1026":           "The sick leave optional date is nearly three days or greater than the current date!",
	"1027":           "Please upload your doctor\"s diagnosis!",
	"1028":           "Tips：You still have |num| chances",
	"1029":           "Not a chance! If you need to continue, please report it to the department manager for approval",
	"leave_for_five": "You can apply day off just one day each time, 5 times a month.",

	//类型 2000
	"2001":        "Punch in",
	"2002":        "Punch out",
	"2003":        "Annual leave",
	"2004":        "Personal leave(Paid)",
	"2005":        "Sick leave",
	"2006":        "Maternity leave",
	"2007":        "Paternity leave",
	"2008":        "National military training holiday",
	"2009":        "Funeral Leave",
	"2010":        "Leave for sterilization",
	"2011":        "Personal Trained Leave",
	"2012":        "Marriage leave",
	"2013":        "Pabbajja leave",
	"2014":        "Personal leave(Unpaid)",
	"2015":        "Days off",
	"2016":        "Other ",
	"2017":        "Rest day",
	"2018":        "Flash Trained Leave",
	"2019":        "Rest day",
	"paid":        "paid",
	"un_paid":     "Unpaid",
	"leave_limit": "you have run out of remaining days",

	//LC申请2100
	"2101": "Please select the date",
	"2102": "Please select the start time",
	"2103": "Please enter the license plate number",
	"2104": "Start time format is incorrect",
	"2105": "License plate number format error",
	"2106": "Image cannot be empty",
	"2107": "You don\"t have permission!",
	"2108": "You have already submitted an LH fee application today, please do not submit it again!!",
	"2109": "Application failed!",
	"2110": "LH starts at 18pm and before 9am!",

	//ceo信箱翻译
	"advice":        "Advice",
	"salary":        "Salary",
	"incentive":     "Incentive",
	"complaint":     "Complaint",
	"other":         "Other",
	"string_len":    "Enter only 10-2000 characters",
	"already_reply": "Replied",
	"wait_reply":    "Wait for reply",
	"has_read":      "read",
	"un_read":       "Unread",
	"miss_args":     "missing required parameters",
	"select_type":   "Please select the problem type",

	//招聘职位 3000
	"3001": "The post name is not empty and is within 50 characters",
	"3002": "Job description is not empty",
	"3003": "The department is not empty",
	"3004": "Reporting line cannot be empty",
	"3005": "Repeat the post name, please re-add",
	"3006": "Post addition failed",
	"3007": "Incorrect position",
	"3008": "No such position exists",
	"3009": "There is no such department",
	"3010": "Position cannot be empty",
	"3011": "Reporting line is incorrect",
	"3012": "Submitter name cannot be empty",
	"3013": "The JD associated with HC can not be deleted",

	//hr系统 4000
	"4001": "Approval",
	"4002": "Recruitment",
	"4003": "Completed",
	"4004": "Obsoleted",
	"4005": "Refused",
	"4006": "Communicated",
	"4007": "Not Communicating",
	"4008": "Error",
	"4009": "Insufficient permissions",
	"4010": "There is no such branch!",
	"4011": "There are * people in the interview, please contact the recruiter to finish the interview process.",
	"4012": "The status has changed, please refresh the page",
	"4013": "Insufficient permissions",
	"4014": "The reason should be within 300 characters",
	"4015": "Pending",
	"4016": "Application",
	"4017": "Agreed",
	"4018": "Insufficient permissions",
	"4019": "Work branch cannot be empty",
	"4020": "The demand has to be greater than 0 and less than 999",

	// 简历
	"4101": "Creation failed!",
	"4102": "fail to edit!",
	"4103": "failed to delete!",
	"4104": "Query failed!",
	"4105": "No such job!",
	"4106": "No such entry!",
	"4107": "The resume has been uploaded to the resume library, please do not upload it again!",
	"4108": "Please fill in your resume!",
	"4109": "Wrong email entry!",
	"4110": "Wrong id card input!",
	"4111": "Graduation school is entered error!",
	"4112": "Major input error!",
	"4113": "Wrong entry of work experience!",
	"4114": "Expected salary entry error!",
	"4115": "Wrong name entry!",
	"4116": "Gender typing error!",
	"4117": "Wrong number entry!",
	"4118": "Expected working place input error!",
	"4119": "Expected position entry error!",
	"4120": "Input personnel error!",
	"4121": "The maximum number of deletions at one time is",

	//面试HC状态
	"4201": "Not filled",
	"4202": "Filled",
	"4203": "Terminated",
	"4204": "expired",

	//取消面试原因 4300
	"4301": "Did not contact the interview candidate",
	"4302": "The interview candidate has something to come",
	"4303": "Candidate has found a new job",
	"4304": "HC application needs to be cancelled",
	"4305": "Jobs do not match after communication",
	"4306": "other",

	//面试预约
	"4401": "The interviewer can\"t be empty!",
	"4402": "The interview time cannot be empty!",
	"4403": "The state cannot be empty",
	"4404": "The state cannot be empty",
	"4405": "Provinces cannot be empty",
	"4406": "Provinces cannot be empty",
	"4407": "Cities cannot be empty",
	"4408": "Cities cannot be empty",
	"4409": "The region cannot be empty",
	"4410": "The region cannot be empty",
	"4411": "The detailed address cannot be empty",
	"4412": "HC cannot be empty",
	"4413": "Your resume should not be empty",
	"4414": "HC is not correct",
	"4415": "An appointment interview does not exist",
	"4416": "Failed to add interview appointment",
	"4417": "An appointment interview already exists. You cannot add an interview",
	"4418": "The reservation ID cannot be empty!",
	"4419": "Cancel the failure",
	"4420": "The interview information cannot be empty",
	"4421": "The evaluation content is too long",
	"4422": "Wrong choice of currency",
	"4423": "Suggested salary is incorrect",
	"4424": "This offer has been sent. Do not send it again",
	"4425": "Sorry,this position is filled, offer sent failed ",
	"4426": "The interviewer has been admitted",

	//操作历史 4500
	"4501": "Suggested salary is incorrect",
	"4502": "Feedback information",

	//操作历史动作4600
	"4601":   "create",
	"4602":   "Modify the",
	"4603":   "delete",
	"4603_1": "View",

	//"4601"             : "创建",
	"4604": "In",
	"4605": "",
	"4606": "cancel",
	"4701": "Send failure",
	"4702": "Available time is not available",
	"4703": "Feedback failed!",

	//简历状态4800
	"4801": "Not communicating",
	"4802": "Waiting for an interview",
	"4803": "Interviewing",
	"4804": "Pending Offer",
	"4805": "Offered",
	"4806": "rejected",
	"4807": "Cancelled",
	"4808": "Deleted",
	"4809": "Already employed",

	//入职管理 4900
	"4900": "male",
	"4901": "Female",
	"4902": "other",
	"4903": "Already employed",
	"4904": "Waiting for entry",

	//backyard修改资料 5000
	"5001": "Submitted successfully",
	"5002": "Submitted successfully",

	//OT管理
	"5101":   "End time must not be less than start time",
	"5102":   "Repeat selection",
	"5103":   "Only about 3 days",
	"5104":   "Approval Pending",
	"5105":   "Rejected",
	"5106":   "Agree",
	"cancel": "Cancelled",
	"closed": "over time and closed",
	"5107":   "Weekdays OT",
	"5108":   "Rest&PH attendance",
	"5109":   "Working nights：Time can only be selected after 19:00, before 8:00 the next day",
	"5110":   "Up to 1000 characters",
	//新增类型
	"5111": "Rest&PH OT",

	//push消息
	"6001": "has submitted the application for ",
	"6002": "please examine and approve",
	"6003": "Attendance Amending",
	"6004": "Ask for Leave",
	"6005": "Apply for LH fee",
	"6006": "Backyrad ข้อความตรวจสอบ",
	"6007": "You have 1 approval messages.",
	"6008": "You have",
	"6009": "bar unread message",
	"6010": "OT",

	//请假  加班 晚班 lh  补申请 提示
	"5200": "Application date is limited to: April 16th to April 24th",
	"5204": "Application date is limited to: April 16th to May 10th",
	"5205": "Application date is limited to: May 16th to June 15th",
	"5206": "The LH time you apply for must be between 21:00 and 6:00 the next day",
	"5201": "Cannot apply for LH and OT at the same time on the same date",
	"5202": "Do not submit again",

	//日期工资
	"6200": "year",
	"6201": "month",
	"6202": "salary",
	//面试操作日志
	"6100": "Interview",
	"6101": "offer",
	"6102": "On-boarding",
	"6103": "Interview feedback",
	"6104": "go to next step",
	"6105": "Not Pass",
	"6106": "Interview Pass",
	"6107": "Modify content",
	"6108": "create",
	"6109": "Modify the",
	"6110": "delete",
	"6111": "cancel",
	"6112": "View",
	"6113": "reservation",
	"6114": "fill in",
	"6115": "send",
	"6116": "Handle",
	"6117": "Feedback",
	"6118": "Resume",

	//面试操作日志
	"7100": "user is not staff of the branch",
	"7101": "the number is not correct",
	"7102": "reason for application should be 10-500 words",
	"7103": "material can not be the same",

	//出差申请相关翻译
	"7111": "airport",
	"7112": "train",
	"7113": "car",
	"7114": "other",
	"7115": "single trip",
	"7116": "round trip",

	"7120": "please fill in the reason for business trip",
	"7121": "please fill in the city ",
	"7122": "please choose",
	"7123": "10-500 words",
	"7124": "please complete the information",
	"7125": "Departure time should earlier than ending time",
	"7126": "remark should between 10-500 words",
	"7127": "reson should between 10-50 words",
	"7128": "please reenter the city",

	//车辆里程相关翻译
	"7130": "Please enter the correct license plate number",
	"7131": "Cannot choose future dates",
	"7132": "Please enter 16-20 digit fuel card number",
	"7133": "ปตท. Oil is 16 digit card number, please enter again",
	"7134": "Please upload pictures of vehicles",
	"7135": "Please upload photos on the personal information page of your license",
	"7136": " You can make up nearly 3 days\" mileage only",
	"7137": "{} oppotunity for making up mileage left",
	"7138": "The opportunity for making up mileage has run out!！",
	"7139": "Off-work mileage cannot be less than on-work mileage",
	"7140": "Making up the mileage cannot exceed 50KM a day",

	"7141": "diesel",
	"7142": "gasoline",

	"7146": "New mileage must be greater than or equal to last mileage",
	"7147": "There are new miles after the currently selected time. The current mileage cannot be greater than the new mileage",

	//请假撤销需求翻译提示语
	"2201": "No data！",
	"2202": "You can only cancel your application！",
	"2204": "There is no information about the employee\"s supervisor！",
	"2205": "Only the direct superior can undo！",
	"2206": "The current application cannot be cancelled",
	"2207": "After 20th, you cannot cancel the application before 15th of this month",

	//审批类型
	"7001": "make up",
	"7002": "leave",
	"7003": "apply for LH fee",
	"7004": "approval OT",
	"7005": "apply for materials",
	"7006": "apply for business trip",
	"7007": "apply for HC",

	//新增打卡提示信息
	"1101":                         "The selected time does not match the actual clocking time, so you cannot apply for OT",
	"overtime_store_allowed":       "The OT time of branch staff shall not exceed 4h!",
	"overtime_department_allowed":  "The OT time of header office staff shall not exceed 8h!",
	"overtime_limit":               "According to your attendance time of the day, the OT duration is insufficient {selected duration}, please choose again!",
	"overtime_act_last":            "The actual OT time cannot be less than 2h!",
	"overtime_attendance_limit_4":  "If your attendance is less than 4 hours, you cannot apply for OT!",
	"overtime_attendance_limit_6":  "If your attendance is less than 6 hours, you cannot apply for OT!",
	"overtime_attendance_limit_11": "If your attendance is less than 11 hours, you cannot apply for OT!",
	"overtime_work_limit_4":        "Due to less than 4 hours of attendance on that day, you cannot apply for holiday OT",
	"overtime_invalid":             "Since there is no matching attendance record, you will not receive OT fee",
	"overtime_five_days":           "You can choose only today, the last 2 days, or the next 2 days",
	"overtime_24":                  "Daily attendance time cannot exceed 24h",

	//补卡需求 新逻辑
	"repair_same_day":  "Punch time and attendance date must be the same day",
	"repair_early":     "Punch in time must be earlier than the punch out time",
	"repair_late":      "Punch out time must be later than the punch in time",
	"repair_tomorrow":  "Punch out time must be earlier than the punch in the next day",
	"repair_limit_16":  "Make up for the punch out attendance must not over 22 hours after  punching in",
	"repair_yesterday": "Punch in time shall not be earlier than punch out time the previous day",

	//    "apply_attendance_exist" : "已有打卡记录，不能提交补申请",
	"leave_16_notice":  "You can choose only today, the last 8 days, or the next 2 days",
	"leave_over_limit": "The maximum training leave is 8 days",

	"over_7_days": "Applications over 5 days are automatically lapsed and cannot be approved",

	"probation_limit": "Employees on probation can only apply for leave_type",

	"wrong_date":       "please choose correct date",
	"overtime_weekend": "date selected is not weekend or holidays",

	"attendance_date":       "date of attendance",
	"reissue_card_time":     "make-up time",
	"attendance_type":       "type of making up",
	"audit_reason":          "reason for absence",
	"leave_type":            "type of leave",
	"start_time":            "start time",
	"end_time":              "end time",
	"days":                  "days",
	"audit_leave_reason":    "reason",
	"photo":                 "photo",
	"plate_number":          "Plate No.",
	"remark":                "remark",
	"OT_date":               "date of over time",
	"OT_type":               "type of over time",
	"duration":              "duration",
	"OT_reason":             "type of over time",
	"send_request":          "apply",
	"goods_name":            "name of material",
	"num":                   "number",
	"employ_user":           "user",
	"reason_application":    "reason for application",
	"approver_is_not_exist": "status of approver is abnormal， please wait a moment and then try again.",

	"traffic_tools":       "transportation",
	"oneway_or_roundtrip": "single or round trip",
	"departure_city":      "place of departure",
	"destination_city":    "destination",
	"days_num":            "days",
	"selectJD":            "selectJD",
	"department":          "department",
	"store":               "branch",
	"expirationdate":      "deadline",
	"demandnumber":        "HC in demand",
	"other_requirement":   "other requirement",
	"hcreason":            "reason of recruitment",
	"has_wait":            "You have waited",
	"hours":               "hours",
	"minute":              "minutes",
	"please try again":    "Please refresh or try again",

	"1_times_salary":   "1 times salary",
	"1.5_times_salary": "1.5 times salary",
	"3_times_salary":   "3 times salary",
	"could_change":     "change for day off",

	"mileage_date":     "Date",
	"start_kilometres": "On-work mileage",
	"started_img":      "On-work mileage photo",
	"end_kilometres":   "Off-work mileage",
	"end_img":          "Off-work mileage phone",

	"invalid_for_apply": "remaining time：{day}days {hour}hours {min}minutes",
}
