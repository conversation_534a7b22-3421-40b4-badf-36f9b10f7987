package messages

var ZH = map[string]string{
	// 公共
	"lang":             "简体中文",
	"account":          "账户",
	"password":         "密码",
	"login":            "立即登录",
	"login_user":       "请输入用户名",
	"login_pwd":        "请输入密码",
	"login_all":        "用户名或密码错误",
	"no_server":        "服务器异常，请稍后再试…",
	"dot":              "网点",
	"details":          "详情",
	"this_week":        "本周 ",
	"last_week":        "上周 ",
	"this_month":       "本月 ",
	"last_month":       "上月 ",
	"date":             "日期",
	"time":             "时间",
	"houre":            "小时",
	"operating_report": "运营报表 ",
	"sales_rankings":   "销售龙虎榜",
	"already_opened":   "已开通",
	"not_open":         "未开通",
	"unknown":          "未知",
	"personal":         "个人",
	"enterprise":       "企业",
	"piece":            "件",
	"info":             " 明细",
	"example":          "例：时长30小时56分钟 在表格中显示为 30:56 ",
	"500":              "验证码{|code|}.您正在修改个人信息,如果非本人操作,请注意账号安全",

	//审批管理 1000
	"1001":           "此员工不存在!",
	"1002":           "当日有上班打卡记录，无需补卡!",
	"1003":           "当日有下班打卡记录，无需补卡!",
	"1004":           "当日有上班补卡申请，请勿重复提交!",
	"1005":           "当日有下班补卡申请，请勿重复提交!",
	"1006":           "补卡失败!",
	"1007":           "只能选择3天内的日期!",
	"1008":           "还没有到所选打卡时间!",
	"1009":           "请假失败!",
	"1010":           "开始时间必须早于或等于结束时间!",
	"1011":           "所选时间有待审批的请假记录，请勿重复提交!",
	"1012":           "所选时间有请假记录，请勿重复提交!",
	"1014":           "审批失败!",
	"1015":           "没有此审批单!",
	"1016":           "此单已审批完成,无法重复审批!",
	"1017":           "对不起,您无权限审核!",
	"1018":           "请假日期必须大于等于当前日期!",
	"1019":           "申请原因应该在500个字符以内!",
	"1020":           "驳回原因应该在500个字符以内!",
	"1021":           "你还可以打上班卡,无需补卡!",
	"1022":           "你还可以打下班卡,无需补卡!",
	"1023":           "请选择开始时间",
	"1024":           "请选择结束时间",
	"1025":           "每个月只能补卡3次，本月已超出上限!",
	"1026":           "病假可选日期是近三天或者大于当前日期!",
	"1027":           "请上传医生诊断证明!",
	"1028":           "提示：您还剩余|num|次补卡次数",
	"1029":           "您的补卡次数已使用完毕,如需继续补卡请上报部门经理审批!",
	"leave_for_five": "一个月最多申请5次休息日，每次一天",

	//类型 2000
	"2001": "上班打卡",
	"2002": "下班打卡",
	"2003": "年假",
	"2004": "带薪事假",
	"2005": "病假",
	"2006": "产假",
	"2007": "陪产假",
	"2008": "国家军训假",
	"2009": "家人去世假",
	"2010": "绝育手术假",
	"2011": "个人受训假",
	"2012": "婚假",
	"2013": "出家假",
	"2014": "不带薪事假",
	"2015": "调休",
	"2016": "其他",
	"2017": "休息日",
	"2018": "公司培训假",
	//新增考勤日历上显示 假期类型
	"2019":    "休息日",
	"paid":    "带薪",
	"un_paid": "不带薪",
	//新增请假 限制逻辑
	"leave_limit": "请假余额已用完",

	//LC申请2100
	"2101": "请选择日期",
	"2102": "请选择开始时间",
	"2103": "请输入车牌号",
	"2104": "开始时间格式不正确",
	"2105": "车牌号格式不正确",
	"2106": "图片不能为空",
	"2107": "您没有权限!",
	"2108": "当日有LH申请，请勿重复提交!",
	"2109": "申请失败!",
	"2110": "LH开始时间为晚上18点之后,早上9点之前!",

	//ceo信箱翻译
	"advice":        "建议",
	"salary":        "工资问题",
	"incentive":     "提成问题",
	"complaint":     "投诉&举报",
	"other":         "其他",
	"string_len":    "只能输入10-2000字符",
	"already_reply": "已回复",
	"wait_reply":    "待回复",
	"has_read":      "已读",
	"un_read":       "未读",
	"miss_args":     "缺少必须的参数",
	"select_type":   "请选择问题类型",

	//招聘职位 3000
	"3001": "岗位名称不为空且在50个字符以内!",
	"3002": "职位描述不为空!",
	"3003": "所属部门不为空!",
	"3004": "汇报线不能为空!",
	"3005": "岗位名称重复请重新添加!",
	"3006": "职位添加失败!",
	"3007": "岗位不正确!",
	"3008": "不存在此职位!",
	"3009": "不存在此部门!",
	"3010": "职位不能为空!",
	"3011": "汇报线不正确!",
	"3012": "提交人名称不能为空!",
	"3013": "该JD已关联HC不可删除!",

	//hr系统 4000
	"4001": "审批中",
	"4002": "招聘中",
	"4003": "已完成",
	"4004": "已作废",
	"4005": "已拒绝",
	"4006": "已沟通",
	"4007": "未沟通",
	"4008": "异常错误",
	"4009": "权限不足",
	"4010": "不存在此网点!",
	"4011": "有人员正在面试中，请先联系招聘人员结束面试流程!",
	"4012": "状态已变更请刷新页面",
	"4013": "此申请不是由你提交的，您无权作废",
	"4014": "原因应该在300个字符以内",
	"4015": "待审批",
	"4016": "申请",
	"4017": "已同意",
	"4018": "对不起,您无权限更新此状态!",
	"4019": "工作网点不能为空",
	"4020": "需求人数需大于0小于999",

	//简历 4100
	"4101": "创建失败！",
	"4102": "修改失败！",
	"4103": "删除失败！",
	"4104": "查询失败！",
	"4105": "无该职务！",
	"4106": "无该录入人员！",
	"4107": "重复的简历提交！",
	"4108": "请填写简历！",
	"4109": "邮箱录入有误！",
	"4110": "身份证录入有误！",
	"4111": "毕业学校录入有误！",
	"4112": "所学专业录入有误！",
	"4113": "工作经历录入有误！",
	"4114": "期望薪资录入有误！",
	"4115": "姓名录入有误！",
	"4116": "性别录入有误！",
	"4117": "手机号录入有误！",
	"4118": "期望工作地录入有误！",
	"4119": "期望职务录入有误！",
	"4120": "录入人员录入有误！",
	"4121": "一次最多删除个数为",

	//面试HC状态
	"4201": "未招满",
	"4202": "已招满",
	"4203": "已终止",
	"4204": "已过期",

	//取消面试原因 4300
	"4301": "未联系上面试者",
	"4302": "面试者有事不来",
	"4303": "面试者已找到新的工作",
	"4304": "HC申请需要撤销",
	"4305": "沟通岗位不匹配",
	"4306": "其他",

	//面试预约
	"4401": "面试官不能为空!",
	"4402": "面试时间不能为空!",
	"4403": "国家不能为空",
	"4404": "国家不能为空",
	"4405": "省份不能为空",
	"4406": "省份不能为空",
	"4407": "城市不能为空",
	"4408": "城市不能为空",
	"4409": "区域不能为空",
	"4410": "区域不能为空",
	"4411": "详细地址不能为空",
	"4412": "HC不能为空",
	"4413": "简历不能为空",
	"4414": "HC不正确",
	"4415": "预约面试不存在",
	"4416": "预约面试添加失败!",
	"4417": "已存在预约面试,无法添加面试",
	"4418": "预约ID不能为空!",
	"4419": "取消失败!",
	"4420": "面试信息不能为空",
	"4421": "评价内容过长",
	"4422": "币种选择有误",
	"4423": "建议薪资输入过长",
	"4424": "此offer已发送过，请勿重复发送",
	"4425": "抱歉，招聘人数已满额，offer发送失败",
	"4426": "面试人已入职",

	//操作历史 4500
	"4501": "预约信息",
	"4502": "反馈信息",

	//操作历史动作4600
	"4601":   "创建",
	"4602":   "修改",
	"4603":   "删除",
	"4603_1": "查看",
	//"4601"             : "创建",
	"4604": "于",
	"4605": "由",
	"4606": "取消",
	"4701": "发送失败",
	"4702": "到岗时间不能为空",
	"4703": "反馈失败！",

	//简历状态4800
	"4801": "未沟通",
	"4802": "待面试",
	"4803": "面试中",
	"4804": "待发OFFER",
	"4805": "已发OFFER",
	"4806": "已拒绝",
	"4807": "已取消",
	"4808": "已删除",
	"4809": "已入职",

	//入职管理 4900
	"4900": "男",
	"4901": "女",
	"4902": "其他",
	"4903": "已入职",
	"4904": "待入职",

	//backyard修改资料 5000
	"5001": "请求成功",
	"5002": "提交成功",

	//OT管理
	"5101":   "结束时间不得小于开始时间",
	"5102":   "所选时间已申请过OT，请勿重复提交！",
	"5103":   "只能选择3天内的日期",
	"5104":   "待审批",
	"5105":   "已驳回",
	"5106":   "同意",
	"cancel": "已撤销",
	"closed": "超时关闭",
	"5107":   "工作日加班",
	"5108":   "休息日和假期加班",
	"5109":   "工作日晚间加班：只能选择19:00之后，第二天8点之前！",
	"5110":   "最大1000字符!",
	//新增类型
	"5111": "休息日和假期正常上班",

	//push消息
	"6001": "提交了",
	"6002": "请你审批",
	"6003": "补卡",
	"6004": "请假",
	"6005": "申请LH费",
	"6006": "Backyrad ข้อความตรวจสอบ",
	"6007": "你有1条审批消息",
	"6008": "你有",
	"6009": "条未读消息",

	//请假  加班 晚班 lh  补申请 提示
	"5200": "申请日期限制为：4月16号至4月24号",
	"5204": "申请日期限制为：4月16号至5月10号",
	"5205": "申请日期限制为：5月16号至5月15号",
	"5206": "申请时间必须在21:00-次日6:00之间",
	"5201": "同一日期，不能同时申请LH和OT",
	"5202": "请勿重复提交",

	//日期工资
	"6200": "年",
	"6201": "月份",
	"6202": "工资",

	"6010": "OT",

	//面试操作日志
	"6100": "面试",
	"6101": "offer",
	"6102": "入职",
	"6103": "面试反馈",
	"6104": "进入下一步",
	"6105": "不通过",
	"6106": "终试通过",
	"6107": "修改内容",
	"6108": "创建",
	"6109": "修改",
	"6110": "删除",
	"6111": "取消",
	"6112": "查看",
	"6113": "预约",
	"6114": "填写",
	"6115": "发送",
	"6116": "办理",
	"6117": "反馈",
	"6118": "简历",

	//面试操作日志
	"7100": "使用者不属于本网点",
	"7101": "物品数量有误",
	"7102": "申请理由需在10-500字符之间",
	"7103": "不能选择重复的物品",

	//出差申请相关翻译
	"7111": "飞机",
	"7112": "火车",
	"7113": "汽车",
	"7114": "其他",
	"7115": "单程",
	"7116": "往返",

	"7120": "请输入出差事由",
	"7121": "请输入城市",
	"7122": "请选择",
	"7123": "10-500字符",
	"7124": "请填写完整",
	"7125": "出发时间需早于结束时间",
	"7126": "出差备注需在10-500字之间",
	"7127": "出差事由需在10-50字之间",
	"7128": "请重新输入城市",

	//车辆里程相关翻译
	"7130": "请输入正确的车牌号",
	"7131": "不能选择未来的日期",
	"7132": "请输入16-20位油卡号",
	"7133": "ปตท.油卡号码为16位数字，请重新输入",
	"7134": "请上传车辆图片",
	"7135": "请上传行驶证个人信息页照片",
	"7136": "只能补近3天的里程",
	"7137": "您还剩余{}次补里程次数",
	"7138": "本月补填里程数的次数已用完！",
	"7139": "下班里程数不能小于上班里程数",
	"7140": "补填报的里程数，一天不能超过50KM",

	"7141": "柴油",
	"7142": "汽油",

	"7146": "新里程数必须大于等于上次里程数",
	"7147": "在当前选择的时间之后，已有新的里程数。当前里程数不能大于新的里程数",

	//请假撤销需求翻译提示语
	"2201": "没有记录！",
	"2202": "只能撤销自己的申请！",
	"2204": "没有该员工的上级信息！",
	"2205": "只有直线上级可以撤销！",
	"2206": "当前申请不能撤销",
	"2207": "20号之后不可撤销本月15号之前的申请",

	//审批类型
	"7001": "补卡申请",
	"7002": "请假申请",
	"7003": "申请LH费",
	"7004": "OT审批",
	"7005": "申请设备和物料",
	"7006": "申请出差",
	"7007": "用人申请",
	"7008": "车辆里程",

	//新增打卡提示信息
	"1101":                         "所选时间和实际打卡时间不匹配，不能申请OT",
	"overtime_store_allowed":       "网点员工OT时长不能超过4h!",
	"overtime_department_allowed":  "总部员工OT时长不能超过8h!",
	"overtime_limit":               "根据您当天的打卡时间，OT时长不足{所选时长}，请重新选择！",
	"overtime_act_last":            "实际OT时长不能小于2h!",
	"overtime_attendance_limit_4":  "当日出勤时长不足4小时，不能申请OT！",
	"overtime_attendance_limit_6":  "当日出勤时长不足6小时，不能申请OT！",
	"overtime_attendance_limit_11": "当日出勤时长不足11小时，不能申请OT！",
	"overtime_work_limit_4":        "当日出勤时长不足4小时，不能申请假期加班费",
	"overtime_invalid":             "由于没有匹配的打卡记录，你不会得到OT费",
	"overtime_five_days":           "只能选择今天、过去2天或未来2天的日期",
	"overtime_24":                  "单日上班时长不能超过24小时",

	//补卡需求 新逻辑
	"repair_same_day":  "上班打卡时间和出勤日期必须是同一天",
	"repair_early":     "上班打卡时间必须早于下班打卡时间",
	"repair_late":      "下班打卡时间必须晚于上班打卡时间",
	"repair_tomorrow":  "下班打卡必须早于第二天的上班打卡",
	"repair_limit_16":  "下班补卡时间距离上班打卡时间不得超过22小时",
	"repair_yesterday": "上班打卡时间不得早于前一天的下班打卡时间",

	//    "apply_attendance_exist" : "已有打卡记录，不能提交补申请",
	"leave_16_notice":  "只能选择今天、明天和后天，或者过去8天的日期", //公司培训假限制
	"leave_over_limit": "公司培训假最多申请8天",

	"over_7_days": "超过5天的申请自动失效，不能审批",

	"probation_limit": "试用期员工不能申请leave_type",

	"wrong_date":       "请选择正确时间",
	"overtime_weekend": "所选日期非休息日或假日！",

	"attendance_date":       "出勤日期",
	"reissue_card_time":     "补卡时间",
	"attendance_type":       "打卡类型",
	"audit_reason":          "缺卡原因",
	"leave_type":            "请假类型",
	"start_time":            "开始时间",
	"end_time":              "结束时间",
	"days":                  "天数",
	"audit_leave_reason":    "原因",
	"photo":                 "图片",
	"plate_number":          "车牌号",
	"remark":                "备注",
	"OT_date":               "OT日期",
	"OT_type":               "加班类型",
	"duration":              "时长",
	"OT_reason":             "加班原因",
	"send_request":          "发起申请",
	"goods_name":            "物品名称",
	"num":                   "数量",
	"employ_user":           "使用者",
	"reason_application":    "申请理由",
	"approver_is_not_exist": "审批人当前状态异常，请稍后再试。",

	"traffic_tools":       "交通工具",
	"oneway_or_roundtrip": "单程往返",
	"departure_city":      "出发城市",
	"destination_city":    "目的城市",
	"days_num":            "出差天数",

	"selectJD":          "选择JD",
	"department":        "所属部门",
	"store":             "工作网点",
	"expirationdate":    "截止日期",
	"demandnumber":      "需求人数",
	"other_requirement": "其他要求",
	"hcreason":          "用人原因",
	"has_wait":          "已经等待",
	"hours":             "小时",
	"minute":            "分钟",
	"please try again":  "请刷新或重试",

	"mileage_date":     "日期",
	"start_kilometres": "上班里程数",
	"started_img":      "上班里程图片",
	"end_kilometres":   "下班里程数",
	"end_img":          "下班里程图片",

	"1_times_salary":   "1倍日薪",
	"1.5_times_salary": "1.5倍日薪",
	"3_times_salary":   "3倍日薪",
	"could_change":     "可调休",

	"invalid_for_apply": "剩余时间：{day}天{hour}小时{min}分钟",
}
