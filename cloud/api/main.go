package main

import (
	"backyard/cloud/api/common/log"
	"backyard/cloud/api/server"
	_ "backyard/cloud/api/routers"
	"github.com/astaxie/beego"
	"github.com/astaxie/beego/logs"
	"runtime"
)

func init() { 	//是否开启session，默认关闭
	logs.SetLogger(logs.AdapterConsole, `{"filename":"api.log","daily":true,"maxdays":10}`) //注册日志组件
	log.InitLogger("example")                                                                      //初始化
}

func main() {
	new(library.JsonRPC).Init() //注册RPC服务
	//new(library.MqServer).Init()
	cpus := runtime.NumCPU()    //获取cpu内核数
	runtime.GOMAXPROCS(cpus)    //设置可用内核量
	beego.Run()                 //启动服务
}
