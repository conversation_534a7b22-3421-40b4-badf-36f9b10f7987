package library

import (
	"github.com/astaxie/beego/logs"
	"github.com/astaxie/beego/orm"
	"strconv"
)

/**
 * 获取下级
 * @Access  public
 * @Param   request
 * @Return  array
 */
func GetSubordinateStaffId(staffInfoId int64) []map[string]string {
	//orm使用
	o := orm.NewOrm()
	o.Using("db_rbi")
	var maps []orm.Params
	var returnData []map[string]string
	sql := "select * from hr_staff_items where item = 'MANGER' and `value` = '" + strconv.Itoa(int(staffInfoId)) + "' "
	count, err := o.Raw(sql).Values(&maps)
	if err != nil {
		logs.Error(err.Error())
	}
	if count <= 0 {
		return returnData
	}
	for key, value := range maps {
		returnData = append(returnData, map[string]string{})
		for k, v := range value {
			returnData[key][k], _ = v.(string)
		}
	}
	return returnData
}
