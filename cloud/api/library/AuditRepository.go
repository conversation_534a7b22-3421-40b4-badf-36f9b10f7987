package library

import (
	"github.com/astaxie/beego/logs"
	"github.com/astaxie/beego/orm"
	"strconv"
)

/**
 * 员工校验
 * @Access  public
 * @Param   staffId 员工ID
 * @Return  struct 员工信息
 */
func CheckoutStaff(staffId, returnType int64) []map[string]string {
	//orm使用
	o := orm.NewOrm()
	o.Using("db_fle")
	var maps []orm.Params
	var returnData []map[string]string
	if staffId > 0 {
		staffInfoSql := "SELECT * FROM `staff_info` where id=" + strconv.Itoa(int(staffId))
		_, err := o.Raw(staffInfoSql).Values(&maps)
		if err != nil {
			logs.Error(err.Error())
		}
		if len(maps) > 0 {
			returnData = append(returnData, map[string]string{})
			for key, value := range maps[0] {
				returnData[0][key], _ = value.(string)
			}
		}
	} else {
		staffInfoSql := "SELECT id,`name` FROM `staff_info`"
		_, err := o.Raw(staffInfoSql).Values(&maps)
		if err != nil {
			logs.Error(err.Error())
		}
		returnType = 1
		if returnType == 1 {
			for key, value := range maps {
				returnData = append(returnData, map[string]string{})
				returnData[key]["id"], _ = value["id"].(string)
				returnData[key]["name"], _ = value["name"].(string)
			}
		} else {
			returnData = append(returnData, map[string]string{})
			for _, value := range maps {
				mapKey, _ := value["id"].(string)
				returnData[0][mapKey], _ = value["name"].(string)
			}
		}
	}
	return returnData
}
