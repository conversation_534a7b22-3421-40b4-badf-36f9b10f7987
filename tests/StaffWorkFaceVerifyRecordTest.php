<?php

namespace Tests;

use FlashExpress\bi\App\Server\StaffWorkFaceVerifyRecordServer;
use FlashExpress\bi\App\Models\backyard\StaffWorkFaceVerifyRecordModel;

/**
 * 人脸识别记录测试类
 * Class StaffWorkFaceVerifyRecordTest
 */
class StaffWorkFaceVerifyRecordTest
{
    private $server;

    public function __construct()
    {
        $this->server = new StaffWorkFaceVerifyRecordServer('zh-CN', '+08:00');
    }

    /**
     * 测试获取记录列表
     */
    public function testGetFaceVerifyRecordData()
    {
        echo "=== 测试获取人脸识别记录列表 ===\n";
        
        $params = [
            'page' => 1,
            'size' => 10,
            'start_date' => '2024-01-01',
            'end_date' => '2024-12-31'
        ];
        
        try {
            $result = $this->server->getFaceVerifyRecordData($params);
            echo "✓ 获取记录列表成功\n";
            echo "总记录数: " . $result['total'] . "\n";
            echo "当前页记录数: " . count($result['list']) . "\n";
            
            if (!empty($result['list'])) {
                $firstRecord = $result['list'][0];
                echo "第一条记录ID: " . $firstRecord['id'] . "\n";
                echo "员工工号: " . $firstRecord['staff_info_id'] . "\n";
                echo "验证渠道: " . $firstRecord['verify_channel_text'] . "\n";
                echo "验证结果: " . $firstRecord['success_text'] . "\n";
            }
        } catch (\Exception $e) {
            echo "✗ 获取记录列表失败: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }

    /**
     * 测试获取记录详情
     */
    public function testGetRecordById()
    {
        echo "=== 测试获取记录详情 ===\n";
        
        // 先获取一条记录的ID
        $listResult = $this->server->getFaceVerifyRecordData(['page' => 1, 'size' => 1]);
        
        if (empty($listResult['list'])) {
            echo "✗ 没有找到测试数据\n\n";
            return;
        }
        
        $recordId = $listResult['list'][0]['id'];
        
        try {
            $result = $this->server->getRecordById($recordId);
            echo "✓ 获取记录详情成功\n";
            echo "记录ID: " . $result['id'] . "\n";
            echo "员工姓名: " . ($result['staff_name'] ?? '未知') . "\n";
            echo "身份证号: " . ($result['identity'] ?? '未知') . "\n";
            echo "手机号: " . ($result['mobile'] ?? '未知') . "\n";
            echo "考勤日期: " . $result['attendance_date'] . "\n";
            echo "创建时间: " . $result['created_at'] . "\n";
        } catch (\Exception $e) {
            echo "✗ 获取记录详情失败: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }

    /**
     * 测试获取统计数据
     */
    public function testGetStatisticsData()
    {
        echo "=== 测试获取统计数据 ===\n";
        
        $params = [
            'start_date' => '2024-01-01',
            'end_date' => '2024-12-31'
        ];
        
        try {
            $result = $this->server->getStatisticsData($params);
            echo "✓ 获取统计数据成功\n";
            
            echo "按验证渠道统计:\n";
            foreach ($result['channel_stats'] as $stat) {
                $channelText = StaffWorkFaceVerifyRecordModel::getVerifyChannelText($stat['verify_channel']);
                $successRate = $stat['count'] > 0 ? round($stat['success_count'] / $stat['count'] * 100, 2) : 0;
                echo "  {$channelText}: 总数 {$stat['count']}, 成功 {$stat['success_count']}, 成功率 {$successRate}%\n";
            }
            
            echo "按日期统计 (最近10天):\n";
            $dateStats = array_slice($result['date_stats'], 0, 10);
            foreach ($dateStats as $stat) {
                $successRate = $stat['count'] > 0 ? round($stat['success_count'] / $stat['count'] * 100, 2) : 0;
                echo "  {$stat['date']}: 总数 {$stat['count']}, 成功 {$stat['success_count']}, 成功率 {$successRate}%\n";
            }
        } catch (\Exception $e) {
            echo "✗ 获取统计数据失败: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }

    /**
     * 测试模型常量
     */
    public function testModelConstants()
    {
        echo "=== 测试模型常量 ===\n";
        
        echo "验证渠道常量:\n";
        $channels = [
            StaffWorkFaceVerifyRecordModel::VERIFY_CHANNEL_ALI,
            StaffWorkFaceVerifyRecordModel::VERIFY_CHANNEL_TENCENT,
            StaffWorkFaceVerifyRecordModel::VERIFY_CHANNEL_BAIDU,
            StaffWorkFaceVerifyRecordModel::VERIFY_CHANNEL_AI,
            StaffWorkFaceVerifyRecordModel::VERIFY_CHANNEL_ORIGINAL,
            StaffWorkFaceVerifyRecordModel::VERIFY_CHANNEL_SILENT_LIVENESS,
        ];
        
        foreach ($channels as $channel) {
            echo "  {$channel}: " . StaffWorkFaceVerifyRecordModel::getVerifyChannelText($channel) . "\n";
        }
        
        echo "组织类型常量:\n";
        $types = [
            StaffWorkFaceVerifyRecordModel::ORGANIZATION_TYPE_STORE,
            StaffWorkFaceVerifyRecordModel::ORGANIZATION_TYPE_DEPARTMENT,
        ];
        
        foreach ($types as $type) {
            echo "  {$type}: " . StaffWorkFaceVerifyRecordModel::getOrganizationTypeText($type) . "\n";
        }
        
        echo "✓ 模型常量测试完成\n\n";
    }

    /**
     * 测试参数验证
     */
    public function testParameterValidation()
    {
        echo "=== 测试参数验证 ===\n";
        
        // 测试空记录ID
        try {
            $this->server->getRecordById(0);
            echo "✗ 应该抛出异常但没有抛出\n";
        } catch (\Exception $e) {
            echo "✓ 空记录ID验证正确: " . $e->getMessage() . "\n";
        }
        
        // 测试不存在的记录ID
        try {
            $result = $this->server->getRecordById(999999999);
            if (empty($result)) {
                echo "✓ 不存在的记录ID返回空数组\n";
            } else {
                echo "✗ 不存在的记录ID应该返回空数组\n";
            }
        } catch (\Exception $e) {
            echo "✓ 不存在的记录ID处理正确: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }

    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "开始运行人脸识别记录接口测试...\n\n";
        
        $this->testModelConstants();
        $this->testGetFaceVerifyRecordData();
        $this->testGetRecordById();
        $this->testGetStatisticsData();
        $this->testParameterValidation();
        
        echo "所有测试完成!\n";
    }
}

// 如果直接运行此文件，执行测试
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    $test = new StaffWorkFaceVerifyRecordTest();
    $test->runAllTests();
}
