<?php

namespace Tests\Integration;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeRelateModel;
use FlashExpress\bi\App\Traits\FindApprovalNodeTrait;
use tests\unit\server\UnitTestCase;

/**
 * FindApprovalNodeTrait 集成测试
 * 测试与真实数据库的交互
 */
class FindApprovalNodeIntegrationTest extends UnitTestCase
{
    use FindApprovalNodeTrait;

    protected $testFlowId;
    protected $testAuditId;
    protected $testAuditType;
    protected $testStaffId;

    protected function setUp()
    {
        parent::setUp();
        
        $this->testFlowId = 'test_flow_' . time();
        $this->testAuditId = 999999;
        $this->testAuditType = 999;
        $this->testStaffId = 888888;
        
        // 清理可能存在的测试数据
        $this->cleanupTestData();
        
        // 创建测试数据
        $this->createTestData();
    }

    protected function tearDown()
    {
        // 清理测试数据
        $this->cleanupTestData();
        $this->clearCache();
        parent::tearDown();
    }

    /**
     * 测试findFirstApprovalNode - 集成测试
     */
    public function testFindFirstApprovalNodeIntegration()
    {
        $params = [
            'auditType' => $this->testAuditType,
            'auditId' => $this->testAuditId,
            'staff_id' => $this->testStaffId
        ];

        $result = $this->findFirstApprovalNode($params);
        
        // 在我们的测试数据中，用户888888是第一个审批节点的审批人
        $this->assertTrue($result, '应该找到用户是第一个审批节点的审批人');
    }

    /**
     * 测试findFirstApprovalNode - 用户不是第一个审批人
     */
    public function testFindFirstApprovalNodeNotFirstApprover()
    {
        $params = [
            'auditType' => $this->testAuditType,
            'auditId' => $this->testAuditId,
            'staff_id' => 777777 // 不存在的用户
        ];

        $result = $this->findFirstApprovalNode($params);
        
        $this->assertFalse($result, '用户不是第一个审批人时应该返回false');
    }

    /**
     * 测试findLastApprovalNode - 集成测试
     */
    public function testFindLastApprovalNodeIntegration()
    {
        // 首先需要将测试用户设置为最后一个审批节点的已审批状态
        $this->setLastApprovalNodeApproved();

        $params = [
            'auditType' => $this->testAuditType,
            'auditId' => $this->testAuditId,
            'staff_id' => $this->testStaffId
        ];

        $result = $this->findLastApprovalNode($params);
        
        $this->assertTrue($result, '应该找到用户是最后一个审批节点的审批人');
    }

    /**
     * 测试不存在的审批申请
     */
    public function testNonExistentAuditApply()
    {
        $params = [
            'auditType' => 888,
            'auditId' => 888888,
            'staff_id' => $this->testStaffId
        ];

        $resultFirst = $this->findFirstApprovalNode($params);
        $resultLast = $this->findLastApprovalNode($params);
        
        $this->assertFalse($resultFirst, '不存在的审批申请应该返回false');
        $this->assertFalse($resultLast, '不存在的审批申请应该返回false');
    }

    /**
     * 测试复杂审批流程 - 多个审批节点
     */
    public function testComplexWorkflowMultipleApprovalNodes()
    {
        // 创建更复杂的审批流程
        $this->createComplexTestData();

        $params = [
            'auditType' => $this->testAuditType + 1,
            'auditId' => $this->testAuditId + 1,
            'staff_id' => $this->testStaffId
        ];

        $resultFirst = $this->findFirstApprovalNode($params);
        $resultLast = $this->findLastApprovalNode($params);
        
        // 在复杂流程中，用户可能既是第一个也是最后一个审批人
        $this->assertTrue($resultFirst, '应该找到第一个审批节点');
        
        // 清理复杂测试数据
        $this->cleanupComplexTestData();
    }

    /**
     * 测试缓存功能 - 重复调用
     */
    public function testCachePerformance()
    {
        $params = [
            'auditType' => $this->testAuditType,
            'auditId' => $this->testAuditId,
            'staff_id' => $this->testStaffId
        ];

        // 第一次调用
        $startTime = microtime(true);
        $result1 = $this->findFirstApprovalNode($params);
        $time1 = microtime(true) - $startTime;

        // 第二次调用（应该使用缓存）
        $startTime = microtime(true);
        $result2 = $this->findFirstApprovalNode($params);
        $time2 = microtime(true) - $startTime;

        // 结果应该一致
        $this->assertEquals($result1, $result2, '缓存前后结果应该一致');
        
        // 第二次调用应该更快（使用了缓存）
        $this->assertLessThan($time1, $time2, '使用缓存的调用应该更快');
    }

    // ==================== 测试数据管理方法 ====================

    /**
     * 创建测试数据
     */
    private function createTestData()
    {
        // 创建审批申请
        $auditApply = new AuditApplyModel();
        $auditApply->setBizType($this->testAuditType);
        $auditApply->setBizValue($this->testAuditId);
        $auditApply->setFlowId($this->testFlowId);
        $auditApply->setState(enums::APPROVAL_STATUS_PENDING);
        $auditApply->setSubmitterId($this->testStaffId);
        $auditApply->setCurrentFlowNodeId(2); // 指向第一个审批节点
        $auditApply->save();

        // 创建工作流节点
        $this->createWorkflowNodes();

        // 创建节点关联关系
        $this->createNodeRelations();

        // 创建审批数据
        $this->createApprovalData();
    }

    /**
     * 创建工作流节点
     */
    private function createWorkflowNodes()
    {
        // 申请人节点
        $submitterNode = new WorkflowNodeModel();
        $submitterNode->setId(1);
        $submitterNode->setFlowId($this->testFlowId);
        $submitterNode->setType(enums::NODE_SUBMITTER);
        $submitterNode->setDeleted(0);
        $submitterNode->save();

        // 第一个审批节点
        $firstApprovalNode = new WorkflowNodeModel();
        $firstApprovalNode->setId(2);
        $firstApprovalNode->setFlowId($this->testFlowId);
        $firstApprovalNode->setType(enums::NODE_APPROVER);
        $firstApprovalNode->setDeleted(0);
        $firstApprovalNode->save();

        // 第二个审批节点
        $secondApprovalNode = new WorkflowNodeModel();
        $secondApprovalNode->setId(3);
        $secondApprovalNode->setFlowId($this->testFlowId);
        $secondApprovalNode->setType(enums::NODE_APPROVER);
        $secondApprovalNode->setDeleted(0);
        $secondApprovalNode->save();

        // 结束节点
        $finalNode = new WorkflowNodeModel();
        $finalNode->setId(4);
        $finalNode->setFlowId($this->testFlowId);
        $finalNode->setType(enums::NODE_FINAL);
        $finalNode->setDeleted(0);
        $finalNode->save();
    }

    /**
     * 创建节点关联关系
     */
    private function createNodeRelations()
    {
        $relations = [
            [1, 2], // 申请人 -> 第一个审批节点
            [2, 3], // 第一个审批节点 -> 第二个审批节点
            [3, 4], // 第二个审批节点 -> 结束节点
        ];

        foreach ($relations as $relation) {
            $nodeRelate = new WorkflowNodeRelateModel();
            $nodeRelate->setFlowId($this->testFlowId);
            $nodeRelate->setFromNodeId($relation[0]);
            $nodeRelate->setToNodeId($relation[1]);
            $nodeRelate->setDeleted(0);
            $nodeRelate->save();
        }
    }

    /**
     * 创建审批数据
     */
    private function createApprovalData()
    {
        // 第一个审批节点的待审批数据
        $approval1 = new AuditApprovalModel();
        $approval1->setBizType($this->testAuditType);
        $approval1->setBizValue($this->testAuditId);
        $approval1->setFlowNodeId(2);
        $approval1->setApprovalId($this->testStaffId);
        $approval1->setState(enums::APPROVAL_STATUS_PENDING);
        $approval1->setDeleted(0);
        $approval1->save();

        // 第二个审批节点的待审批数据
        $approval2 = new AuditApprovalModel();
        $approval2->setBizType($this->testAuditType);
        $approval2->setBizValue($this->testAuditId);
        $approval2->setFlowNodeId(3);
        $approval2->setApprovalId($this->testStaffId);
        $approval2->setState(enums::APPROVAL_STATUS_PENDING);
        $approval2->setDeleted(0);
        $approval2->save();
    }

    /**
     * 设置最后审批节点为已审批状态
     */
    private function setLastApprovalNodeApproved()
    {
        // 更新第二个审批节点的状态为已审批
        $approval = AuditApprovalModel::findFirst([
            'conditions' => 'biz_type = :type: and biz_value = :value: and flow_node_id = 3',
            'bind' => [
                'type' => $this->testAuditType,
                'value' => $this->testAuditId,
            ]
        ]);

        if ($approval) {
            $approval->setState(enums::APPROVAL_STATUS_APPROVAL);
            $approval->save();
        }
    }

    /**
     * 创建复杂测试数据
     */
    private function createComplexTestData()
    {
        $complexFlowId = $this->testFlowId . '_complex';
        $complexAuditType = $this->testAuditType + 1;
        $complexAuditId = $this->testAuditId + 1;

        // 创建审批申请
        $auditApply = new AuditApplyModel();
        $auditApply->setBizType($complexAuditType);
        $auditApply->setBizValue($complexAuditId);
        $auditApply->setFlowId($complexFlowId);
        $auditApply->setState(enums::APPROVAL_STATUS_PENDING);
        $auditApply->setSubmitterId($this->testStaffId);
        $auditApply->save();

        // 创建更多节点和关系...
        // 这里可以根据需要创建更复杂的审批流程
    }

    /**
     * 清理测试数据
     */
    private function cleanupTestData()
    {
        // 清理审批申请
        $auditApplies = AuditApplyModel::find([
            'conditions' => 'biz_type = :type: and biz_value = :value:',
            'bind' => [
                'type' => $this->testAuditType,
                'value' => $this->testAuditId,
            ]
        ]);
        foreach ($auditApplies as $apply) {
            $apply->delete();
        }

        // 清理工作流节点
        $nodes = WorkflowNodeModel::find([
            'conditions' => 'flow_id = :flow_id:',
            'bind' => ['flow_id' => $this->testFlowId]
        ]);
        foreach ($nodes as $node) {
            $node->delete();
        }

        // 清理节点关联
        $relations = WorkflowNodeRelateModel::find([
            'conditions' => 'flow_id = :flow_id:',
            'bind' => ['flow_id' => $this->testFlowId]
        ]);
        foreach ($relations as $relation) {
            $relation->delete();
        }

        // 清理审批数据
        $approvals = AuditApprovalModel::find([
            'conditions' => 'biz_type = :type: and biz_value = :value:',
            'bind' => [
                'type' => $this->testAuditType,
                'value' => $this->testAuditId,
            ]
        ]);
        foreach ($approvals as $approval) {
            $approval->delete();
        }
    }

    /**
     * 清理复杂测试数据
     */
    private function cleanupComplexTestData()
    {
        $complexAuditType = $this->testAuditType + 1;
        $complexAuditId = $this->testAuditId + 1;

        // 清理复杂测试的审批申请
        $auditApplies = AuditApplyModel::find([
            'conditions' => 'biz_type = :type: and biz_value = :value:',
            'bind' => [
                'type' => $complexAuditType,
                'value' => $complexAuditId,
            ]
        ]);
        foreach ($auditApplies as $apply) {
            $apply->delete();
        }

        // 清理其他相关数据...
    }
} 