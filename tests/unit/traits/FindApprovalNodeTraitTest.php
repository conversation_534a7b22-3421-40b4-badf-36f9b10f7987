<?php

namespace Tests\Unit\Traits;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\AuditApplyModel;
use FlashExpress\bi\App\Models\backyard\AuditApprovalModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeModel;
use FlashExpress\bi\App\Models\backyard\WorkflowNodeRelateModel;
use FlashExpress\bi\App\Traits\FindApprovalNodeTrait;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;

/**
 * FindApprovalNodeTrait 测试用例
 */
class FindApprovalNodeTraitTest extends TestCase
{
    use FindApprovalNodeTrait;

    private $mockAuditApplyModel;
    private $mockWorkflowNodeModel;
    private $mockWorkflowNodeRelateModel;
    private $mockAuditApprovalModel;

    protected function setUp(): void
    {
        parent::setUp();
        $this->clearCache();
        
        // 创建模型的Mock对象
        $this->mockAuditApplyModel = $this->createMock(AuditApplyModel::class);
        $this->mockWorkflowNodeModel = $this->createMock(WorkflowNodeModel::class);
        $this->mockWorkflowNodeRelateModel = $this->createMock(WorkflowNodeRelateModel::class);
        $this->mockAuditApprovalModel = $this->createMock(AuditApprovalModel::class);
    }

    protected function tearDown(): void
    {
        $this->clearCache();
        parent::tearDown();
    }

    /**
     * 测试参数验证
     */
    public function testValidateParams()
    {
        $method = $this->getMethod('validateParams');

        // 测试有效参数
        $validParams = [
            'auditType' => 1,
            'auditId' => 123,
            'staff_id' => 456
        ];
        $this->assertTrue($method->invokeArgs($this, [$validParams]));

        // 测试缺少auditType
        $invalidParams1 = [
            'auditId' => 123,
            'staff_id' => 456
        ];
        $this->assertFalse($method->invokeArgs($this, [$invalidParams1]));

        // 测试缺少auditId
        $invalidParams2 = [
            'auditType' => 1,
            'staff_id' => 456
        ];
        $this->assertFalse($method->invokeArgs($this, [$invalidParams2]));

        // 测试缺少staff_id
        $invalidParams3 = [
            'auditType' => 1,
            'auditId' => 123
        ];
        $this->assertFalse($method->invokeArgs($this, [$invalidParams3]));

        // 测试空值
        $emptyParams = [
            'auditType' => '',
            'auditId' => 0,
            'staff_id' => null
        ];
        $this->assertFalse($method->invokeArgs($this, [$emptyParams]));
    }

    /**
     * 测试findFirstApprovalNode - 成功情况
     * @group approval_node
     */
    public function testFindFirstApprovalNodeSuccess()
    {
        $params = [
            'auditType' => 71,
            'auditId' => 2296,
            'staff_id' => 26370
        ];

        // Mock数据准备
        $this->setupMockDataForFirstNode();

        // 执行测试
        $result = $this->findFirstApprovalNode($params);
        
        $this->assertTrue($result, '应该找到第一个审批节点');
    }

    /**
     * 测试findFirstApprovalNode - 无审批申请数据
     */
    public function testFindFirstApprovalNodeNoAuditApply()
    {
        $params = [
            'auditType' => 1,
            'auditId' => 999,
            'staff_id' => 456
        ];

        // Mock getWorkflowData返回null（模拟没有找到审批申请）
        $method = $this->getMethod('getWorkflowData');
        $this->setMethod('getWorkflowData', function() { return null; });

        $result = $this->findFirstApprovalNode($params);
        
        $this->assertFalse($result, '没有审批申请数据时应该返回false');
    }

    /**
     * 测试findLastApprovalNode - 成功情况
     * @group approval_node
     */
    public function testFindLastApprovalNodeSuccess()
    {
        $params = [
            'auditType' => 71,
            'auditId' => 2294,
            'staff_id' => 26370
        ];

        // Mock数据准备
        $this->setupMockDataForLastNode();

        $result = $this->findLastApprovalNode($params);
        
        $this->assertTrue($result, '应该找到最后一个审批节点');
    }

    /**
     * 测试findLastApprovalNode - 无最终节点
     */
    public function testFindLastApprovalNodeNoFinalNode()
    {
        $params = [
            'auditType' => 1,
            'auditId' => 123,
            'staff_id' => 456
        ];

        // Mock getWorkflowData返回有效数据但getNodeByType返回null
        $this->setupMockWorkflowData();
        $this->setMethod('getNodeByType', function() { return null; });

        $result = $this->findLastApprovalNode($params);
        
        $this->assertFalse($result, '没有最终节点时应该返回false');
    }

    /**
     * 测试isUserApprover方法
     */
    public function testIsUserApprover()
    {
        $method = $this->getMethod('isUserApprover');

        // Mock审批信息存在的情况
        $mockApproval = $this->createMock(AuditApprovalModel::class);
        
        // 方法存在时返回true
        $result1 = $method->invokeArgs($this, [100, 456, enums::APPROVAL_STATUS_PENDING]);
        
        // 注意：在实际环境中，这里需要Mock AuditApprovalModel::findFirst方法
        // 由于我们无法直接Mock静态方法，这里主要测试方法调用逻辑
        $this->assertTrue(is_bool($result1), '应该返回布尔值');
    }

    /**
     * 测试findApprovalNodeInDirection方法 - 向前查找
     */
    public function testFindApprovalNodeInDirectionNext()
    {
        $method = $this->getMethod('findApprovalNodeInDirection');

        // 设置测试数据
        $nodeList = [
            1 => enums::NODE_SUBMITTER,    // 申请人节点
            2 => enums::NODE_APPROVER,     // 审批人节点
            3 => enums::NODE_CC,           // 抄送节点
            4 => enums::NODE_APPROVER,     // 另一个审批人节点
            5 => enums::NODE_FINAL         // 结束节点
        ];
        
        $nodeRelateList = [
            1 => 2,  // 申请人 -> 审批人
            2 => 3,  // 审批人 -> 抄送
            3 => 4,  // 抄送 -> 审批人
            4 => 5   // 审批人 -> 结束
        ];

        $this->setNodeList($nodeList);
        $this->setNodeRelateList($nodeRelateList);

        // 从申请人节点开始找下一个审批节点
        $result = $method->invokeArgs($this, [1, 'next']);
        $this->assertEquals(2, $result, '应该找到节点2作为下一个审批节点');

        // 从抄送节点找下一个审批节点
        $result2 = $method->invokeArgs($this, [3, 'next']);
        $this->assertEquals(4, $result2, '应该找到节点4作为下一个审批节点');
    }

    /**
     * 测试findApprovalNodeInDirection方法 - 向后查找
     */
    public function testFindApprovalNodeInDirectionPrevious()
    {
        $method = $this->getMethod('findApprovalNodeInDirection');

        $nodeList = [
            1 => enums::NODE_SUBMITTER,    // 申请人节点
            2 => enums::NODE_APPROVER,     // 审批人节点
            3 => enums::NODE_CC,           // 抄送节点
            4 => enums::NODE_APPROVER,     // 另一个审批人节点
            5 => enums::NODE_FINAL         // 结束节点
        ];
        
        $nodeRelateList = [
            1 => 2,  // 申请人 -> 审批人
            2 => 3,  // 审批人 -> 抄送
            3 => 4,  // 抄送 -> 审批人
            4 => 5   // 审批人 -> 结束
        ];

        $this->setNodeList($nodeList);
        $this->setNodeRelateList($nodeRelateList);

        // 从结束节点向前找上一个审批节点
        $result = $method->invokeArgs($this, [5, 'previous']);
        $this->assertEquals(4, $result, '应该找到节点4作为上一个审批节点');

        // 从抄送节点向前找上一个审批节点
        $result2 = $method->invokeArgs($this, [3, 'previous']);
        $this->assertEquals(2, $result2, '应该找到节点2作为上一个审批节点');
    }

    /**
     * 测试循环引用保护
     */
    public function testCircularReferenceProtection()
    {
        $method = $this->getMethod('findApprovalNodeInDirection');

        // 创建循环引用的节点关系
        $nodeList = [
            1 => enums::NODE_SUBMITTER,
            2 => enums::NODE_CC,
            3 => enums::NODE_CC,
        ];
        
        $nodeRelateList = [
            1 => 2,
            2 => 3,
            3 => 2,  // 循环引用：3 -> 2
        ];

        $this->setNodeList($nodeList);
        $this->setNodeRelateList($nodeRelateList);

        $result = $method->invokeArgs($this, [1, 'next']);
        $this->assertFalse($result, '循环引用应该被检测到并返回false');
    }

    /**
     * 测试无效节点处理
     */
    public function testInvalidNodeHandling()
    {
        $method = $this->getMethod('findApprovalNodeInDirection');

        // 空的节点列表
        $this->setNodeList([]);
        $this->setNodeRelateList([]);

        $result = $method->invokeArgs($this, [1, 'next']);
        $this->assertFalse($result, '空节点列表应该返回false');
    }

    /**
     * 测试缓存功能
     */
    public function testCacheFunction()
    {
        // 测试缓存清理
        $this->clearCache();
        $this->assertEmpty($this->workflow_data_cache, '缓存应该被清空');

        // 测试缓存设置（这里主要测试属性访问）
        $reflection = new \ReflectionClass($this);
        $cacheProperty = $reflection->getProperty('workflow_data_cache');
        $cacheProperty->setAccessible(true);
        
        $testData = ['test' => 'data'];
        $cacheProperty->setValue($this, $testData);
        
        $cachedData = $cacheProperty->getValue($this);
        $this->assertEquals($testData, $cachedData, '缓存数据应该正确设置');

        $this->clearCache();
        $this->assertEmpty($cacheProperty->getValue($this), '清理后缓存应该为空');
    }

    /**
     * 数据提供器：各种参数组合测试
     * @return array
     */
    public function parameterProvider(): array
    {
        return [
            'valid_params' => [
                'params' => ['auditType' => 1, 'auditId' => 123, 'staff_id' => 456],
                'expected' => true,
                'message' => '有效参数应该通过验证'
            ],
            'missing_audit_type' => [
                'params' => ['auditId' => 123, 'staff_id' => 456],
                'expected' => false,
                'message' => '缺少auditType应该验证失败'
            ],
            'zero_values' => [
                'params' => ['auditType' => 0, 'auditId' => 0, 'staff_id' => 0],
                'expected' => false,
                'message' => '零值应该验证失败'
            ],
            'null_values' => [
                'params' => ['auditType' => null, 'auditId' => null, 'staff_id' => null],
                'expected' => false,
                'message' => 'null值应该验证失败'
            ]
        ];
    }

    /**
     * 使用数据提供器测试参数验证
     * @dataProvider parameterProvider
     */
    public function testParameterValidationWithProvider($params, $expected, $message)
    {
        $method = $this->getMethod('validateParams');
        $result = $method->invokeArgs($this, [$params]);
        $this->assertEquals($expected, $result, $message);
    }

    // ==================== 辅助方法 ====================

    /**
     * 获取私有或受保护的方法
     */
    protected function getMethod($methodName)
    {
        $reflection = new \ReflectionClass($this);
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);
        return $method;
    }

    /**
     * 设置私有方法的Mock实现
     */
    protected function setMethod($methodName, $callback)
    {
        $reflection = new \ReflectionClass($this);
        
        // 这里需要具体的Mock实现，实际项目中可能需要使用更复杂的Mock策略
    }

    /**
     * 设置第一个节点的Mock数据
     */
    private function setupMockDataForFirstNode()
    {
        // 这里应该设置Mock对象的返回值
        // 实际项目中需要根据具体的数据库操作来Mock
        
        $workflowData = [
            'flow_id' => 'test_flow_123',
            'node_list' => [
                1 => enums::NODE_SUBMITTER,
                2 => enums::NODE_APPROVER,
                3 => enums::NODE_FINAL
            ],
            'node_relate_list' => [
                1 => 2,
                2 => 3
            ]
        ];

        $this->setNodeList($workflowData['node_list']);
        $this->setNodeRelateList($workflowData['node_relate_list']);
    }

    /**
     * 设置最后一个节点的Mock数据
     */
    private function setupMockDataForLastNode()
    {
        $workflowData = [
            'flow_id' => 'test_flow_123',
            'node_list' => [
                1 => enums::NODE_SUBMITTER,
                2 => enums::NODE_APPROVER,
                3 => enums::NODE_FINAL
            ],
            'node_relate_list' => [
                1 => 2,
                2 => 3
            ]
        ];

        $this->setNodeList($workflowData['node_list']);
        $this->setNodeRelateList($workflowData['node_relate_list']);
    }

    /**
     * 设置基础工作流数据
     */
    private function setupMockWorkflowData()
    {
        $workflowData = [
            'flow_id' => 'test_flow_123',
            'node_list' => [],
            'node_relate_list' => []
        ];

        $this->setNodeList($workflowData['node_list']);
        $this->setNodeRelateList($workflowData['node_relate_list']);
    }
} 