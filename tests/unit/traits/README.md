# FindApprovalNodeTrait 测试用例说明

本目录包含 `FindApprovalNodeTrait` 的 PHPUnit 测试用例。

## 测试文件说明

### FindApprovalNodeTraitTest.php
- **目的**: 单元测试，测试 Trait 中各个方法的逻辑
- **覆盖内容**:
  - `findFirstApprovalNode()` 方法测试
  - `findLastApprovalNode()` 方法测试
  - 参数验证测试
  - 节点查找算法测试
  - 缓存功能测试
  - 循环引用保护测试

### 测试用例包含

#### 1. 参数验证测试
```php
public function testValidateParams()
```
- 测试有效参数
- 测试缺少必要参数的情况
- 测试空值处理

#### 2. 第一个审批节点查找测试
```php
public function testFindFirstApprovalNodeSuccess()
public function testFindFirstApprovalNodeNoAuditApply()
```
- 测试成功找到第一个审批节点
- 测试无审批申请数据的情况

#### 3. 最后一个审批节点查找测试
```php
public function testFindLastApprovalNodeSuccess()
public function testFindLastApprovalNodeNoFinalNode()
```
- 测试成功找到最后一个审批节点
- 测试无最终节点的情况

#### 4. 节点方向查找测试
```php
public function testFindApprovalNodeInDirectionNext()
public function testFindApprovalNodeInDirectionPrevious()
```
- 测试向前查找审批节点
- 测试向后查找审批节点

#### 5. 边界情况测试
```php
public function testCircularReferenceProtection()
public function testInvalidNodeHandling()
```
- 测试循环引用保护
- 测试无效节点处理

#### 6. 缓存功能测试
```php
public function testCacheFunction()
```
- 测试缓存设置和清理

## 运行测试

### 运行所有测试
```bash
cd /path/to/project
phpunit tests/unit/traits/FindApprovalNodeTraitTest.php
```

### 运行特定分组的测试
```bash
phpunit --group approval_node tests/unit/traits/FindApprovalNodeTraitTest.php
```

### 运行单个测试方法
```bash
phpunit --filter testValidateParams tests/unit/traits/FindApprovalNodeTraitTest.php
```

## 测试数据说明

测试使用模拟数据，主要包括：
- 节点类型：申请人节点、审批人节点、抄送节点、会签节点、结束节点
- 节点关联关系：定义节点之间的流转关系
- 审批状态：待审批、已审批、已驳回等

## 注意事项

1. **Mock限制**: 由于 Phalcon 模型的静态方法难以 Mock，部分测试主要验证方法调用逻辑
2. **数据库依赖**: 集成测试需要数据库环境，单元测试不依赖数据库
3. **测试隔离**: 每个测试方法都会清理缓存，确保测试间的隔离

## 扩展测试

如需添加新的测试用例，请遵循以下原则：
1. 每个测试方法只测试一个特定功能
2. 使用有意义的测试方法名称
3. 添加必要的测试说明注释
4. 确保测试数据的隔离性

## 测试覆盖率

当前测试覆盖的主要功能：
- ✅ 参数验证
- ✅ 第一个审批节点查找
- ✅ 最后一个审批节点查找
- ✅ 双向节点查找算法
- ✅ 循环引用保护
- ✅ 缓存功能
- ✅ 边界情况处理

### 待完善的测试内容
- 与真实数据库的集成测试
- 复杂审批流程的测试
- 性能测试
- 错误处理的完整覆盖 