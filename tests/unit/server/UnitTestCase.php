<?php

namespace tests\unit\server;

use Phalcon\Di;
use Phalcon\Di\FactoryDefault;
use Phalcon\Mvc\Application;
use Phalcon\Test\UnitTestCase as PhalconTestCase;


abstract class UnitTestCase extends PhalconTestCase
{
    private $_loaded = false;

    protected $userInfo = [];
    protected $locale   = [];

    public $lang = 'zh-CN'; // 语言变量
    public $timezone = '+07:00';


    public function setUp()
    {
        $this->languagePack = $this->getDI()->get('languagePack');
        $this->languagePack->setLanguage(getCountryDefaultLang());
        $this->lang     = $this->languagePack->lang;
        $config         = $this->getDI()->getConfig();
        $this->timezone = $config->application['timeZone'];

        $this->_loaded  = true;
    }

    public function __destruct()
    {
        if (!$this->_loaded) {

        }
    }

    public function tearDown()
    {
    }
}