<?php

namespace Tests\Unit;

use FlashExpress\bi\App\Server\ToolServer;
use Phalcon\Test\Module\UnitTest;
use PHPUnit\Framework\TestCase;
use tests\unit\server\UnitTestCase;

class ToolServerTest extends UnitTestCase
{
    private $toolServer;

    public function setUp(): void
    {
        parent::setUp();
        $this->toolServer = new ToolServer($this->lang, $this->timezone);
    }

    /**
     * @dataProvider sortingTestCaseProvider
     */
    public function testQuickSort(array $input, array $expected, string $message)
    {
        $result = $this->invokeMethod($this->toolServer, 'quickSort', [$input]);
        $this->assertEquals($expected, $result, $message);
    }

    /**
     * Data provider for quickSort test cases
     */
    public function sortingTestCaseProvider(): array
    {
        return [
            'Empty array' => [
                'input' => [],
                'expected' => [],
                'message' => 'Empty array should return empty array'
            ],
            'Single element' => [
                'input' => [1],
                'expected' => [1],
                'message' => 'Single element array should return same array'
            ],
            'Already sorted array' => [
                'input' => [1, 2, 3, 4, 5],
                'expected' => [1, 2, 3, 4, 5],
                'message' => 'Sorted array should remain unchanged'
            ],
            'Reverse sorted array' => [
                'input' => [5, 4, 3, 2, 1],
                'expected' => [1, 2, 3, 4, 5],
                'message' => 'Reverse sorted array should be properly sorted'
            ],
            'Array with duplicates' => [
                'input' => [3, 1, 4, 1, 5, 9, 2, 6, 5, 3, 5],
                'expected' => [1, 1, 2, 3, 3, 4, 5, 5, 5, 6, 9],
                'message' => 'Array with duplicates should be properly sorted'
            ],
            'Array with negative numbers' => [
                'input' => [-3, 1, -4, 1, -5, 9, -2, 6, 5, -3, 5],
                'expected' => [-5, -4, -3, -3, -2, 1, 1, 5, 5, 6, 9],
                'message' => 'Array with negative numbers should be properly sorted'
            ],
            'Array with zero' => [
                'input' => [0, 3, -1, 0, 5, -2, 0, 1],
                'expected' => [-2, -1, 0, 0, 0, 1, 3, 5],
                'message' => 'Array with zeros should be properly sorted'
            ],
            'Large numbers' => [
                'input' => [1000000, 10, 999999, 100, 999998],
                'expected' => [10, 100, 999998, 999999, 1000000],
                'message' => 'Array with large numbers should be properly sorted'
            ],
            'Mixed types (will be treated as numbers)' => [
                'input' => ['3', 1, '2', 5, '4'],
                'expected' => [1, '2', '3', '4', 5],
                'message' => 'Array with mixed types should be properly sorted'
            ],
            'Random array' => [
                'input' => [42, 17, 99, 23, 56, 71, 8, 33],
                'expected' => [8, 17, 23, 33, 42, 56, 71, 99],
                'message' => 'Random array should be properly sorted'
            ]
        ];
    }

    /**
     * Helper method to test protected/private methods
     */
    protected function invokeMethod($object, $methodName, array $parameters = [])
    {
        $reflection = new \ReflectionClass(get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);
        return $method->invokeArgs($object, $parameters);
    }
}