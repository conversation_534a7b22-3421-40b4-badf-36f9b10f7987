
.table-3column-striped > tbody > tr td:nth-child(6n+3),
.table-3column-striped > tbody > tr td:nth-child(6n+4),
.table-3column-striped > tbody > tr td:nth-child(6n+5) {
    background-color: #f9f9f9;
}

.table-3column-striped > tbody > tr td:nth-child(6n+1),
.table-3column-striped > tbody > tr td:nth-child(6n+2),
.table-3column-striped > tbody > tr td:nth-child(6n) {
    background-color: #ffffff;
}

.table-column-striped > tbody > tr td:nth-child(2n) {
    background-color: #f9f9f9;
}

.table-column-striped > tbody > tr td:nth-child(2n+1){
    background-color: #ffffff;
}

.table-3column-striped > tbody > tr:hover td {
    background-color: unset !important;
}

.getClick{
    color:blue;
    text-decoration:underline;
    cursor: pointer;
}
.th{
    background: dodgerblue;
    width:160px;
    text-align: center;
}
.margin{
    margin-left:100px;
    margin-top:15px;
}
.arrow {
    width: 0px;
    height: 0px;
    border-top: 10px solid transparent;
    border-right: 170px solid;
    border-bottom: 20px solid transparent;
    position: absolute;
    left: 40px;
    top: 100px;
    margin-left: -10px;
    margin-top: 10px;
 /*   border-right-color: maroon;*/
    border-right-color: lightblue;
    z-index:8888;
    transform: rotate(40.5deg);
    -o-transform:rotate(40.5deg);
    -webkit-transform:rotate(40.5deg);
    -moz-transform:rotate(40.5deg);
    display: none;
}
.x_panel {
    padding: 10px;
    overflow-x: auto;
}