*{
  padding: 0;
  margin: 0;
  list-style: none;
  outline: none;
  text-decoration: none;
}
.clearfix{
  height: 0;
  line-height: 0;
  overflow: hidden;
  clear: both;
}
.clearfix1:after {
            content: ".";
            clear: both;
            display: block;
            overflow: hidden;
            font-size: 0;
            height: 0;
        }
.clearfix1 {
            zoom: 1;
        }
.fl{
  float: left;
}
.fr{
  float: right;
}
input{
  -webkit-appearance: none;
  appearance: none;
}
body{
  /* width: calc(100% - 15px); */
  font-size: 12px;
  background-color: #eff2f7;
  font-family: "Microsoft YaHei",Arial, Helvetica, sans-serif;
  font-weight: 100;
}
.login-body{
  background-color: #f5f6fa;
}
.login-box{
  width: 100%;
  height: 100%;
  display: table;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
}
.login-box-row{
  display: table-row;
}
.login-box-cell{
  display: table-cell;
  vertical-align: middle;
}
.login-box-main{
  width: 800px;
  margin: auto;
}
.language-change{
  float: right;
}
.language-change .language-box{
  width: 80px;
  line-height: 40px;
  padding: 0 20px 0 10px;
  height: 40px;
  /* appearance:none;
  -moz-appearance:none;
  -webkit-appearance:none;
  -moz-appearance:textfield; */
  border: 1px solid #dcdcdc;
  background: #fff url(../images/select_arrow.png) no-repeat 90px center;
  background-size: 12px;
  cursor: pointer;
  position: relative;
  margin-top: 10px;
}
.language-list{
  width: 100%;
  border: 1px solid #dcdcdc;
  border-top: 0;
  position: absolute;
  top: 45px;
  left: -1px;
  background: #fff;
  display: none;
}
.language-list a{
  border-bottom: 1px solid #dcdcdc;
  display: block;
  color: #666;
  padding: 2px 10px;
}
.login-box-content{
  margin-top: 30px;
  padding-bottom: 65px;
  background-color: #fff;
  border-radius: 20px;
  box-shadow: 0 15px 36px rgba(0, 0, 0, 0.1)
}
.login-title{
  text-align: center;
  padding: 65px 0;
  font-size: 30px;
  color: #313043;
  font-family: Arial, Helvetica, sans-serif;
  font-weight: bold;
}
.login-input-box{
  width: 455px;
  margin: auto;
  font-size: 22px;
  color: #666;
}
.login-input-box .title{
  color: #313043;
  font-family: Arial, Helvetica, sans-serif;
  font-weight: bold;
}
.login-input-box .input-row{
  padding-top: 45px;
}
.login-input-box .input-label{
  width: 120px;
  height:45px;
  line-height: 45px;
  float: left;
}
.login-input-box .input-val-box{
  width: 330px;
  float: right
}
.login-input-box .input{
  width: 100%;
  height: 45px;
  border: 1px solid #e7e7e7;
  box-sizing: border-box;
  padding: 0 10px;
  border-radius: 4px;
  color: #666;
}
.login-btn{
  width: 330px;
  height: 50px;
  line-height: 50px;
  display: block;
  text-align: center;
  background-color: #323042;
  color: #fefefe;
  border: 0;
  font-size: 18px;
}
.login-btn-box{
  position: relative;
}
.err-info{
  color: #ff0000;
  font-size: 12px;
  display: block;
  position: absolute;
  left: 0;
  top: -25px;
}

.side-bar
{
  width: 180px;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  background-color: #313043;
  box-shadow: 2px 0 3px rgba(0, 0, 0, .2);
  z-index: 999;
}
.side-bar .side-bar-header{
  height: 80px;
  padding: 0 20px;
  border-bottom: 1px solid #424254;
}
.side-bar-header .logo{
  width: 145px;
  height: 42px;
  float: left;
  background: url(../images/page_logo.png) no-repeat;
  margin-top: 20px;
}

.side-bar-nav{
  padding: 0px 0px 24px 0px;
}
.side-control{
  background: url(../images/nav_control.png) no-repeat 30px center;
  cursor: pointer;
  padding: 30px 0;
  border-bottom: 1px solid #424254;
}
.side-bar-li{
  border-bottom: 1px solid #424254;
  background: url(../images/nav_arrow.png) no-repeat 140px center;
  padding: 20px 0;
  margin-left: 20px;
}
.side-bar-li-label{
  height: 30px;
  line-height: 30px;
  background: no-repeat 10px center;
  color: #fff;
  /* font-weight: bold; */
  padding-left: 38px;
  display: block;
}
/* .side-bar-li .side-a{
  color: #fff;
} */
.bar-input{
  background-image: url(../images/input_sign.png);
}
.bar-search{
  background-image: url(../images/search_sign.png);
}
.page-header-box{
  height: 60px;
  position: fixed;
  top: 0;
  left: 180px;
  right: 0;
  background: #fff;
  border-bottom: 1px solid #dfe1e6;
  box-shadow: 0 0 2px rgba(0, 0, 0, .1);
  z-index: 998;
}
.page-header{
  padding-right: 0px;
  float: right;
}
.page-header .language-change{
  float: left;
}
.page-header .language-change .language-box{
  border: 0;
  font-size: 12px;
}
.page-header .language-change .language-list{
  border: 1px solid #dcdcdc;
}
.page-header .log-user{
  height: 60px;
  line-height: 60px;
  border-left: 1px solid #eff2f7;
  color: #313043;
  float: left;
  margin-left: 20px;
  padding-left: 20px;
}
.page-header .name{
  margin-right: 18px;}
.page-header .logout{
  color: #0e97e6;
  cursor: pointer;
  display: inline-block;
  min-width: 80px;
}

.page-map{
  height: 46px;
  line-height: 46px;
  padding-left: 20px;
  overflow: hidden;
  background-color: #f8f8f8;
  box-shadow: 0 0 6px rgba(0, 0, 0, .1);
}
.page-map .icon{
  width: 20px;
  height: 15px;
  background: url(../images/page_position.png) no-repeat;
  float: left;
  margin-top: 22px;
}
.page-map .page-label{
  float: left;
  font-size: 14px;
  color: #313043;
}

.page-map-info-left{
  float: left;
}
.main{
  padding: 60px 0 0 180px;
}
.page-main-box{
  padding: 10px;
  margin-bottom: 10px;
}
.page-main-box-white{
  background: #fff;
}
.page-main{
  background-color: #fff;
  padding: 10px 10px;
}
.page-search-section{
  padding-bottom: 30px;
}
.search-row .search-label,.search-row .search-val-box{
  height: 30px;
  line-height: 30px;
  float: left;
}
.search-row .search-label{
  margin-right: 10px;
}
.search-date-row .search-label,.search-date-row .search-val-box{
  float: none;
}
.search-date-info{
  margin-left: 15px;
}
.search-list{
  display: table;
  width: 100%;
  border:1px solid #e7e7e7;
  border-right: 0;
}
.search-list .list-row{
  display: table-row;
  color: #666;
}
.search-list .list-cell{
  display: table-cell;
  vertical-align: middle;
  height: 64px;
  padding: 0 18px;
  border-right: 1px solid #e7e7e7;
  width: 20%;
}
.search-list li:nth-child(2n+1){
  background-color: #fafafa;
}
.search-list li.list-header{
  background-color: #eff2f7;
  /* font-weight: bold; */
  color: #000
}
.search-list .item-time{
  min-width: 170px;
}
.search-list .item-name{
  min-width: 170px;
}
.search-list .item-num{
  min-width: 200px;
}

.pagination-box{
  border-top: 1px solid #e7e7e7;
  margin-top: 30px;
  padding: 30px 0 0;
  overflow: hidden;
}
.pagination-box a{
  color: #666;
  margin-left: 18px;
}
.pagination-box a.active, .a-style{
  color: #0190fe;
}

.info-bar{
  color: #767677;
  /* padding-bottom: 20px; */
  float: right;
  margin-right: 20px;
  line-height: 45px;
}
.info-bar .red-f{
  color: #ff0000;
}

.page-add-section{
  position: relative;
}


/* 轮播图 */
.sowing-map-box{
  width: 620px;
  /* height: 800px; */
  position: absolute;
  left: 0;
  top: 0;
}
.page-add-section{
  width: 100%;
  /* min-width: 820px; */
  box-sizing: border-box;
  padding-left: 620px;
  min-height: 800px;
  overflow: auto;
  overflow-y: hidden;
  padding-bottom: 50px;
}

.sowing-map-box .num-bar{
  color: #313043;
  font-size: 20px;
  padding-bottom: 20px;
}
.package-info{
  /* width: 800px; */
  min-width: 300px;
  padding: 0 20px;
  background-color: #daebf2;
}
.consignee-info{
  background-color: #e1e5fa;
  margin-top: 30px;
}
.package-info .package-header{
  height: 46px;
  line-height: 46px;
  border-bottom: 1px solid #fff;
  margin-bottom: 20px;
  font-size: 14px;
}
.tagging{
  color: #ff0000;
  display: inline-block;
  margin-left: 4px;
  line-height: normal;
}
.package-table{
  width: 100%;
}
.package-row{
  padding-bottom: 15px;
  position: relative;
  padding-left: 120px;
}
.package-row-two{
  padding-left: 0;
}
.package-row .package-label{
  width: 120px;
  height: 26px;
  line-height: 30px;
  float: left;
  color: #767677;
  position: absolute;
  left: 0;
  top: 0;
}
.package-row .package-label-two{
  padding-left: 15px;
}

.package-row .package-val-box{
  /* width: 650px;  */
  width: 100%;
  float: left;
}
.package-row .package-short{
  width: 50%;
  position: relative;
  padding-left: 120px;
  box-sizing: border-box;
  float: left;
}
.package-row .package-margin-r{
  margin-right: 20px;
}
.package-row .package-input,.tinyselect .selectbox{
  width: 100%;
  height: 26px;
  border-radius: 2px;
  border: 0;
  box-sizing: border-box;
  padding: 0 10px;
  background: #fff;
}
.package-row .package-short .package-input{
}
.package-btn{
  width: 100%;
  /* min-width: 840px; */
  min-width: 340px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  background-color: #00994f;
  display: block;
  color: #fff;
  font-size: 14px;
  margin-top: 82px;
  border: 0;
}
.work-status-box{
  height: 22px;
  line-height: 22px;
  border-radius: 20px;
  background: #313043;
  padding-right: 32px;
  padding-left: 14px;
  position: relative;
  float: left;
  margin-top: 20px;
  margin-right: 20px;
  color: #fff;
  cursor: pointer;
}
.work-status-rest{
  background: #dcdcdc;
  text-align: right;
  color: #313043;
  padding-right: 20px;
  padding-left: 32px;
}
.work-status-box .work-status{
  width: 22px;
  height: 22px;
  background: #ffea33;
  border-radius: 20px;
  position: absolute;
  right: -1px;
  top: 0px;
  box-shadow: 0 0 7px rgba(0, 0, 0, 0.7)
}
.work-status-rest .work-status{
  left: -1px;
  right: auto;
}
.no-task{
  padding-top: 180px;
  text-align: center;
  font-size: 12px;
  line-height: 26px;
}
.no-task img{
  width: 180px;
  display: inline-block;
  margin: auto;
  margin-bottom: 0px;
}
.tinyselect{
  width: 100% !important;
  min-width: auto;
}
.tinyselect .selectbox::after{
  background: url(../images/select_arrow.png) no-repeat center center;
  border-left: 0;
}
.yichang{    
  display: inline-block;
  margin-left: 30px;
  padding: 0px 10px;
  height: 22px;
  background: #313043;
  line-height: 22px;
  text-align: center;
  color: #fff; text-decoration: none;
  border-radius: 4px;
  


}

/*异常弹层开始*/
.yc_back{ width: 100%; height: 100%;  background:#000; filter:alpha(opacity:50); opacity:0.5;  -moz-opacity:0.5;-khtml-opacity: 0.5; position: fixed; left: 0px; top: 0px; z-index: 999999;}
.yc_box{ width: 400px; height: 190px; border-radius: 8px; background: #fff; box-shadow: 0px 0px 5px #b2b2b2;  z-index: 99999999; position: fixed; top: 200px; left: 50%; margin-left: -200px;}
.yc_box .xz{ padding: 60px 50px 40px; border-bottom: 1px solid #dedede;}
.yc_box .xz .zi{ float: left;line-height: 24px; font-size: 12px; color: #767677; display: inline-block; }
.yc_box .xz .xuan{ width: 180px; float: right; height: 24px; line-height: 24px; color: #313043; border: 1px solid #dedede;}
.yc_box .yesbox{ padding: 18px 0px; }
.yc_box .yesbox a{ display: block; width: 199px; float: left; height: 30px; line-height: 30px; text-align: center; color: #008eff; text-decoration: none; }
.yc_box .yesbox a:first-child{border-right: 1px solid #dedede; color: #666;}

/*异常弹层结束*/
/* 缩放 */
.side-bar-shrink .side-bar{
  width: 38px;
}
.side-bar-shrink .side-bar .side-bar-header{
  padding: 0;
  height: 45px;
}
.side-bar-shrink .side-bar .side-bar-header .logo{
  width: 90%;
  margin: auto;
  float: none;
  display: none;
}
.side-bar-shrink .side-bar .side-control{
  background-position: 10px center;
  /* padding: 0; */
}
.side-bar-shrink .side-bar .side-bar-li{
  margin-left: 0;
}
.side-bar-shrink .main{
  padding-left: 38px;
}
.side-bar-shrink .page-header-box{
  left: 38px;;
}

/* 幻灯片 */

.container{padding:5px; overflow:hidden; background-color: #fff; }
.spec-preview{
  width:600px;
  height: 442px;
  display: block;
  overflow: hidden;
  border: 1px solid #f6f6f6;
}
.jqzoom{width:100%; height: 100%; background-color: #e6e8e8; float:left;border:none;position:relative;padding:0px;cursor:pointer;margin:0px;display:block; overflow: hidden; display: block;}
.zoomdiv{z-index:100;position:absolute;top:0px;left:590px;width:350px;height:350px;background:#ffffff;border:1px solid #dedede;display:none;text-align:center;overflow:hidden; z-index: 9999999;}
.jqZoomPup{z-index:10;visibility:hidden;position:absolute;top:0px;left:0px;width:20px;height:20px;border:1px solid #aaa;background:#ffffff /*url(../images/zoom.png) 50% center no-repeat*/;opacity: 0.5;-moz-opacity: 0.5;-khtml-opacity: 0.5;filter: alpha(Opacity=50);z-index: 9999999;}

/*图片小图预览列表*/
.spec-scroll{clear:both;margin-top:5px; position: relative; }
.spec-scroll .prev{float:left;margin-right:4px;}
.spec-scroll .next{float:right;}
.spec-scroll .prev,.spec-scroll .next{display:none;font-family:"宋体";text-align:center;width:10px;height:54px; line-height:54px;border:1px solid #CCC;background:#EBEBEB;cursor:pointer;text-decoration:none;}
.spec-scroll .items{float:left;position:relative;width:600px;overflow:hidden;  height:151px;}
.spec-scroll .items ul{position:absolute;width:999999px;}
.spec-scroll .items ul li{float:left;width:190px; height:143px; margin: 0 6px 0px 0px; text-align:center; overflow: hidden; padding:2px; border: 1px solid #f6f6f6;}
.spec-scroll .items ul li:last-child{margin-right: 0px;}
.spec-scroll .items ul li:hover{border-color: #ffea33;}
.spec-scroll .items ul li img{ max-width: 100%; max-height: 100%;}
/*.jqzoom img{*/
  /*max-width: 100%;*/
  /*max-height: 100%;*/
/*}*/
.spec-scroll .xuanzhuan{ position: absolute; cursor: pointer; right: 0px; top: -37px; width: 30px; height: 30px; background: #fff; z-index: 997;  border: 1px solid #f6f6f6; text-align: center;}
.spec-scroll .xuanzhuan img{ width: 24px; margin-top: 3px; }


@media screen and (max-width: 1600px) {
  .package-row .package-short{
    width: 100%;
  }
  .package-row .package-short-first{
    padding-bottom: 15px;
  }
  .package-row .package-label-two{
    padding-left: 0;
  }
}
@media screen and (max-width: 1540px) {
  .main{
    padding-top: 61px;
  }
  .page-header-box{
    height: 60px;
  }
  .page-header .language-change .language-box,.work-status-box{
    font-size: 12px;
  }
  .page-header .log-user{
    height:60px;
    line-height: 60px;
  }
  .page-map{
    height: 45px;
    line-height: 45px;
  }
  .page-map .icon{
    margin-top: 15px;
  }
  .page-main-box{
    padding: 15px 20px;
  }
  .input-section .num-bar{
    font-size: 16px;
    padding-bottom: 10px;
  }
  .package-info .package-header{
    height: 45px;
    line-height: 45px;
    font-size: 14px;
    margin-bottom: 15px;
  }
  .package-row,.package-row .package-short-first{
    padding-bottom: 10px;
  }
  .consignee-info{
    margin-top: 15px;
  }
  .package-row .package-input, .tinyselect .selectbox{
    height: 24px;
    border-radius: 2px;
  }
  .package-btn{
    margin-top: 47px;
  }

}



@media screen and (min-width: 1541px) and (max-width: 1920px){
  .page-add-section{
    min-height: 1000px;
  }
  .sowing-map-box{
    width: 820px;
  }
  .spec-preview{
    width: 800px; 
    height: 600px;
  }
  .page-add-section{
    padding-left: 820px;
  }
  .spec-scroll .items{
    width: 800px;
    height: 200px;
  }
  .spec-scroll .items ul li{
    width: 258px;
    height: 194px;
  }
}
@media screen and (min-width: 1921px){
  .page-add-section{
    min-height: 1000px;
  }
  .sowing-map-box{
    width: 1020px;
  }
  .spec-preview{
    width: 1000px; 
    height: 675px;
  }
  .page-add-section{
    padding-left: 1020px;
  }
  .spec-scroll .items{
    width: 1000px;
    height: 240px;
  }
  .spec-scroll .items ul li{
    width: 323px;
    height: 225px;
  }
}

















