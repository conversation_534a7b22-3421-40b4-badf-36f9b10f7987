<?php
use Phalcon\Mvc\Application;

define('BASE_PATH', dirname(__DIR__));
define('APP_PATH', BASE_PATH . '/app');

try {
    require BASE_PATH.'/vendor/autoload.php';
    require BASE_PATH.'/app/common/functions.php';
    //require BASE_PATH.'/app/util/ErrorHandler.php';
    //加载环境变量
    $dotenv = new Dotenv\Dotenv(BASE_PATH);
    $dotenv->load();
   
    //设定运行环境变量
    $runtime = env('runtime','dev');
    define('RUNTIME', $runtime);
    //生产不报PHP错误
    $level = $runtime == 'pro' ? 0 : E_ALL;
    error_reporting($level);
    if ($runtime != 'pro') {
        if (function_exists('opcache_reset')) {
            opcache_reset();
        }
        ini_set('opcache.enable', '0');
    }
     /**
     * Read services
     */
    include APP_PATH . '/config/services.php';

    $application = new Application($di);
   
    //使用DI里注册的config服务 同$this->get("config");
   
    $modules = require APP_PATH . '/config/modules.php';
    /**
     * 国家模块
     */
    $application->registerModules($modules);
   
     /**
     * Include Autoloader
     */
    include APP_PATH . '/config/loader.php';

    //设置时区
    $time_zone_GMT = $di->getConfig()->application->timeZoneGMT;
    date_default_timezone_set($time_zone_GMT);
    /**
     *
     */
    $application->handle()->send();
} catch (\Throwable $e) {
    if (in_array(RUNTIME, ['dev','test','tra'])) {
        $msg =  $e->getMessage() . '<br>';
        $msg .= '<pre>' . $e->getTraceAsString() . '</pre>';
    }else {
        $msg = "<pre> Something went wrong, if the error continue please contact us </pre>";
    }
    //region 记录系统异常日志
    $log = array(
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'code' => $e->getCode(),
        'msg' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
    );
    $di = $application->getDI();
    $exception_logger = $di->get('logger');
    $exception_logger->write_log(json_encode($log,JSON_UNESCAPED_UNICODE));


    $result = [
        'code' => 0,
        'msg' => $msg,
        'data' => null
    ];
    $response = $application->response;
    $response->setStatusCode(500);
    $response->setJsonContent($result);
    $response->send();

}
