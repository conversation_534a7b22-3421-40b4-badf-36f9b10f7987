<!DOCTYPE html>
<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <title>supervisor Ack</title>
  <style>
    @page {
      size: 210mm 297mm;
      margin: 25mm 10mm 10mm;
    }

    body {
      margin: 0;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif
    }

    .box {
      box-sizing: border-box;
      font-size: 3mm;
      font-weight: 400;
      text-align: justify;
      /* text-indent: 10mm; */
      line-height: 170%;
    }

    .page {
      margin-top: 5mm;
      height: 260mm;
    }

    .page-top-title {
      text-align: left;
      font-size: 4mm;
      margin: 4mm 0 4mm;
    }


    .select-box input[type=checkbox] {
      margin-right: 5px;
      cursor: pointer;
      font-size: 14px;
      width: 15px;
      height: 12px;
      position: relative;
    }

    .select-box input[type=checkbox]:after {
      position: absolute;
      width: 10px;
      height: 15px;
      top: 0;
      content: " ";
      background-color: #fff;
      color: #000;
      display: inline-block;
      visibility: visible;
      padding: 0px 2px;
      border: 1px solid #000;
    }

    .select-box input[type=checkbox]:checked:after {
      content: "✓";
      font-size: 14px;
    }

    .table-box {
      margin-top: 5mm;
      width: 100%;
    }

    .table-box-top p {
      text-align: center;
      margin: 0;
      font-size: 3mm;
    }

    .table-box-top>p:last-child {
      /* margin-bottom: 2mm; */
    }

    .table-1,
    .table-2 {
      width: 98%;
      border: 1px solid #333;
    }

    .table-3,
    .table-4,
    .table-5 {
      width: 98%;
      border-collapse: collapse;
      /* border: 1px solid #333; */
    }

    .table-1 td,
    .table-2 td {
      display: inline-block;
      box-sizing: border-box;
      padding: 0.8mm 1.4% 0;
      width: 30%;
    }


    .table-2 td {
      width: 49%;
    }

    .table-2 {
      border-top: none;
      border-bottom: none;
    }

    .table-3 td,
    .table-4 td {
      border: 1px solid #333;
    }

    .table-3 p {
      margin: 0;
    }

    .table-5 td {
      border: none;
    }

    .slot {
      font-style: normal;
      width: 150mm;
      padding: 0 12px;
      border-bottom: 1px solid #333;
      display: inline-block;
      line-height: 1.5;
    }

    .slot-p {
      width: 175mm;
      padding: 0 12px;
      border-bottom: 1px solid #333;
      line-height: 1.5;
    }

    .signature-box {
      margin: 20mm 10mm 0;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
    }

    .signature-span {
      padding: 0 12px;
      border-bottom: 1px solid #333;
    }
  </style>
</head>

<body>
  <div class="box">
    <!-- 第一页 -->
    <div>
      <p class="page-top-title"> <strong>Acknowledgement</strong></p>

      <div class="select-box">
        <input type="checkbox" name="method" value="plus" checked>I Fully acknowledge that my Performance Evaluation
        below was discussed to me by my Superior.<br>
      </div>
      <div class="table-box">
        <div class="table-box-top">
          <p>Flash Express Ltd</p>
          <p>${tpl_name}</p>
        </div>
        <table class="table-1" border="0" style="margin:0 auto;">
          <tr valign="top">
            <td>Name: <span>${name}</span></td>
            <td>ID: <span>${staff_info_id}</span></td>
            <td>Position: <span>${job_name}</span></td>
          </tr>
          <tr valign="top">
            <td>Rank: <span>F${job_title_grade_v2}</span></td>
            <td>First-level Department: <span>${department_name}</span></td>
            <td>Second-level Department: <span>${node_department_name}</span></td>
          </tr>
          <tr valign="top">
            <td>${hire_text}: <span>${hire_date}</span></td>
            <td>Deadline for Stage I Assessment: <span>${first_deadline_date}</span></td>
            <td>Deadline of Stage II assessment: <span>${second_deadline_date}</span></td>
          </tr>

        </table>
        <table class="table-2" border="0" style="margin:0 auto;">
          <tr valign="top">
            <td>Evaluation criteria</td>
            <td></td>
          </tr>
          <tr valign="top">
            <td>1-2 Unqualified</td>
            <td>7-8 Good and often exceeds the requirements</td>
          </tr>
          <tr valign="top">
            <td>3-4 Poor, below general expectations</td>
            <td>9-10 Excellent, exceed expectations</td>
          </tr>
          <tr valign="top">
            <td>5-6 Generally qualified, basically achieve the requirements</td>
            <td>Note: The employee second stage must score more than 6.0</td>
          </tr>
          <tr valign="top">
            <td></td>
            <td>The second stage is the final evaluation, and the score will affect whether the employee becomes
              positive.</td>
          </tr>
        </table>
        <table border="0" class="table-3" style="margin:0 auto;">
          <tr>
            <td width='70%' valign="top">Assessment questions and instructions</td>
            <td width='10%' align="center">weight</td>
            <td width='10%' align="center">The first score</td>
            <td width='10%' align="center">The second score</td>
          </tr>

         <#list score.list as list>
                    <#list list.list as item>

                        <tr>
                            <td width='70%' valign="top">
                    <#if item_index == 0>
                      <p>${list_index+1} ${list.name}</p>
                      <p>${item.name}:</p>
                    <#else>
                      <p>${list_index+1}.${item_index+1} ${item.name}:</p>
                    </#if>
                      <p>${item.info}</p>
                      </td>
                            <td width='10%' align="center">${item.score_rule}</td>
                            <td width='10%' align="center">${item.score}</td>
                            <td width='10%' align="center">${item.second_score}</td>
                        </tr>

                    </#list>
                     <tr>
                              <td width='70%' valign="top">
                                <p>${list.name}</p>

                              </td>
                              <td width='10%' align="center">1</td>
                              <td width='10%' align="center">${list.score}</td>
                              <td width='10%' align="center">${list.second_score}</td>
                            </tr>
         </#list>

        </table>
      </div>
    </div>

    <!--第2页  -->
    <div>
      <table border="0" class="table-3" style="margin:0 auto;">
        <tr>
          <td width='70%' valign="top">
            <p>Comprehensive score (work performance * 0.5 + personal quality * 0.5)</p>

          </td>
          <td width='10%' align="center"></td>
          <td width='10%' align="center">${score["score"]}</td>
          <td width='10%' align="center">${score["second_score"]}</td>
        </tr>
      </table>
      <p style="width: 100%;"></p>
      <table border="0" class="table-4" style="margin:0 auto;">
        <tr>
          <td width='20%' align="center" height="150px">Opinion (comment)</td>
          <td valign="top" colspan="5">${comment}</td>
        </tr>
        <tr>

          <td width="70%" valign="top" colspan="3">3 Discipline assessment</td>
          <td width='10%' align="center"></td>
          <td width='10%' align="center"></td>
          <td width='10%' align="center"></td>
        </tr>
        <tr>
          <td width="70%" valign="top" colspan="3" height="200px">
            <table border="0" class="table-5" style="margin:0 auto;">
              <tr>
                <td width='50%' align="center"></td>
                <td width='25%' align="center">Attendance</td>
                <td width='25%' align="center"></td>
              </tr>
              <tr>
                <td width='50%' valign="top"></td>
                <td width='25%' align="center">The first assessment </td>
                <td width='25%' align="center">The second</td>
              </tr>
              <tr>
                <td width='50%' valign="top">Late (minutes)</td>
                <td width='25%' align="center">${attendance["1"].late}</td>
                <td width='25%' align="center">${attendance["2"].late}</td>
              </tr>
              <tr>
                <td width='50%' valign="top">sick leave (day)</td>
                <td width='25%' align="center">${attendance["1"]["sick"]}</td>
                <td width='25%' align="center">${attendance["2"]["sick"]}</td>
              </tr>
              <tr>
                <td width='50%' valign="top">Personal leave (day)</td>
                <td width='25%' align="center">${attendance["1"]["casual"]}</td>
                <td width='25%' align="center">${attendance["2"]["casual"]}</td>
              </tr>
              <tr>
                <td width='50%' valign="top">Lack of attendance (day)</td>
                <td width='25%' align="center">${attendance["1"]["lack"]}</td>
                <td width='25%' align="center">${attendance["2"]["lack"]}</td>
              </tr>

            </table>
          </td>
          <td valign="top" colspan="3">
            <p>Disciplinary Punishment (Warning Letter)</p>
            <p>The first time:</p>
            <#if alert?? && (alert?size > 0 )>
            <p>${alert[0]["type_code_text"]}</p>
            <p>${alert[0]["date_at"]}</p>
            </#if>
            <p>The second time:</p>
            <#if alert?? && (alert?size > 1 )>
            <p>${alert[1]["type_code_text"]}</p>
            <p>${alert[1]["date_at"]}</p>
            </#if>
          </td>
        </tr>
      </table>
      <p style="margin:0 0 20mm">Original FORM: Company HR department / employee profile</p>
      <div style="padding-left:5mm">
        <!-- checked 控制打钩 -->
        <div class="select-box">
          <input type="checkbox" name="method" value="plus"  <#if option_type==1> checked </#if> >I agree with the evaluation presented and discussed
          to me<br>
        </div>
        <div class="select-box">
          <input type="checkbox" name="method" value="plus"  <#if option_type==2> checked </#if> >I somehow agree with the evaluation presented and discussed
          to me<br>
        </div>
        <div class="select-box">
          <input type="checkbox" name="method" value="plus"  <#if option_type==3> checked </#if> >I disagree with the evaluation presented and discussed to
          me<br>
        </div>
        <div style="margin: 0 5mm 0; ">
          <p>
            <span>because:</span><span class="slot"></span>
          </p>
          <p class="slot-p">${acknowledge_remark}</p>
        </div>
      </div>
      <div class="signature-box">
        Signature: <span class="signature-span">
         <#if staff_sign_img??>
        <img src="${staff_sign_img}" class="qm-img"  style="width: auto; height: 80px;object-fit: cover;" alt=""/>
        </#if>
        </span>
      </div>
    </div>
  </div>
</body>
</html>